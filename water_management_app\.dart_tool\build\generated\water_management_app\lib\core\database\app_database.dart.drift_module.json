{"elements": {"AppDatabase": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "AppDatabase"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 11263, "name": "AppDatabase"}, "references": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "villages"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "meters"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "readings"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "pricing_tiers"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "bills"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "payments"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "app_settings"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "audit_logs"}], "type": "database", "tables": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "villages"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "meters"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "readings"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "pricing_tiers"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "bills"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "payments"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "app_settings"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "audit_logs"}], "views": [], "includes": [], "queries": [], "schema_version": 1, "daos": [], "has_constructor_arg": false}, "users": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 293, "name": "Users"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 331, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "username", "nameInDart": "username", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 383, "name": "username"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": 3, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "email", "nameInDart": "email", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 459, "name": "email"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "password_hash", "nameInDart": "passwordHash", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 525, "name": "passwordHash"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 255}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "full_name", "nameInDart": "fullName", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 589, "name": "fullName"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "phone", "nameInDart": "phone", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 649, "name": "phone"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "role", "nameInDart": "role", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 716, "name": "role"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_active", "nameInDart": "isActive", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 771, "name": "isActive"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(true)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "two_factor_enabled", "nameInDart": "twoFactorEnabled", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 847, "name": "twoFactorEnabled"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "two_factor_secret", "nameInDart": "twoFactorSecret", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 932, "name": "twoFactorSecret"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 32}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "last_login", "nameInDart": "lastLogin", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1013, "name": "lastLogin"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "failed_login_attempts", "nameInDart": "failed<PERSON><PERSON>in<PERSON><PERSON><PERSON>s", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1067, "name": "failed<PERSON><PERSON>in<PERSON><PERSON><PERSON>s"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "locked_until", "nameInDart": "lockedUntil", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1155, "name": "lockedUntil"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1216, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1296, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Users", "row_class_name": "User", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "villages": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "villages"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1378, "name": "Villages"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1419, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1471, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "code", "nameInDart": "code", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1527, "name": "code"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "main_meter_id", "nameInDart": "mainMeterId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1591, "name": "mainMeterId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": true, "nameInSql": "latitude", "nameInDart": "latitude", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1664, "name": "latitude"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": true, "nameInSql": "longitude", "nameInDart": "longitude", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1714, "name": "longitude"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "population", "nameInDart": "population", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1764, "name": "population"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1819, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_active", "nameInDart": "isActive", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1872, "name": "isActive"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(true)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 1952, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2032, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Villages", "row_class_name": "Village", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "subscribers": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2118, "name": "Subscribers"}, "references": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "villages"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2162, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "subscriber_number", "nameInDart": "subscriberNumber", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2214, "name": "subscriberNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "full_name", "nameInDart": "fullName", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2290, "name": "fullName"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "village_id", "nameInDart": "villageId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2349, "name": "villageId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "villages"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "subscription_type", "nameInDart": "subscriptionType", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2418, "name": "subscriptionType"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2485, "name": "status"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('active')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "phone", "nameInDart": "phone", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2580, "name": "phone"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "email", "nameInDart": "email", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2647, "name": "email"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "address", "nameInDart": "address", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2715, "name": "address"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "national_id", "nameInDart": "nationalId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2764, "name": "nationalId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "connection_date", "nameInDart": "connectionDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2840, "name": "connectionDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "disconnection_date", "nameInDart": "disconnectionDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2904, "name": "disconnectionDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "notes", "nameInDart": "notes", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 2967, "name": "notes"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "discount_percentage", "nameInDart": "discountPercentage", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3014, "name": "discountPercentage"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_charitable", "nameInDart": "isCharitable", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3094, "name": "isCharitable"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3179, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3259, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Subscribers", "row_class_name": "Subscriber", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "meters": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "meters"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3344, "name": "Meters"}, "references": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3383, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "serial_number", "nameInDart": "serialNumber", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3435, "name": "serialNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "subscriber_id", "nameInDart": "subscriberId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3506, "name": "subscriberId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "meter_type", "nameInDart": "meterType", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3581, "name": "meterType"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('residential')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3684, "name": "status"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('active')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "installation_date", "nameInDart": "installationDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3783, "name": "installationDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "last_maintenance", "nameInDart": "lastMaintenance", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3849, "name": "lastMaintenance"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "next_maintenance", "nameInDart": "nextMaintenance", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3914, "name": "nextMaintenance"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": true, "nameInSql": "latitude", "nameInDart": "latitude", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 3975, "name": "latitude"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": true, "nameInSql": "longitude", "nameInDart": "longitude", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4025, "name": "longitude"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "initial_reading", "nameInDart": "initialReading", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4076, "name": "initialReading"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "current_reading", "nameInDart": "currentReading", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4152, "name": "currentReading"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "multiplier", "nameInDart": "multiplier", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4228, "name": "multiplier"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(1.0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "brand", "nameInDart": "brand", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4302, "name": "brand"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "model", "nameInDart": "model", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4369, "name": "model"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "diameter", "nameInDart": "diameter", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4436, "name": "diameter"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4510, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4590, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Meters", "row_class_name": "<PERSON>er", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "readings": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "readings"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4675, "name": "Readings"}, "references": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "meters"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4716, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "meter_id", "nameInDart": "meterId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4767, "name": "meterId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "meters"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "current_reading", "nameInDart": "currentReading", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4832, "name": "currentReading"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "previous_reading", "nameInDart": "previousReading", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4877, "name": "previousReading"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "consumption", "nameInDart": "consumption", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4923, "name": "consumption"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "reading_date", "nameInDart": "readingDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 4969, "name": "readingDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "reading_period", "nameInDart": "readingPeriod", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5015, "name": "readingPeriod"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 7}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "read_by", "nameInDart": "readBy", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5088, "name": "readBy"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "reading_method", "nameInDart": "readingMethod", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5162, "name": "readingMethod"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_validated", "nameInDart": "isValidated", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5226, "name": "isValidated"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_estimated", "nameInDart": "isEstimated", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5306, "name": "isEstimated"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "validation_notes", "nameInDart": "validationNotes", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5386, "name": "validationNotes"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "image_path", "nameInDart": "imagePath", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5443, "name": "imagePath"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 255}], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": true, "nameInSql": "gps_latitude", "nameInDart": "gpsLatitude", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5515, "name": "gpsLatitude"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": true, "nameInSql": "gps_longitude", "nameInDart": "gpsLongitude", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5568, "name": "gpsLongitude"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5626, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Readings", "row_class_name": "Reading", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "pricing_tiers": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "pricing_tiers"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5716, "name": "PricingTiers"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5761, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "subscription_type", "nameInDart": "subscriptionType", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5813, "name": "subscriptionType"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "tier_number", "nameInDart": "tierNumber", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5879, "name": "tierNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "min_consumption", "nameInDart": "minConsumption", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5923, "name": "minConsumption"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": true, "nameInSql": "max_consumption", "nameInDart": "maxConsumption", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 5968, "name": "maxConsumption"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "price_per_unit", "nameInDart": "pricePerUnit", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6024, "name": "pricePerUnit"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "fixed_charge", "nameInDart": "fixedCharge", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6067, "name": "fixedCharge"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_active", "nameInDart": "isActive", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6140, "name": "isActive"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(true)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "effective_from", "nameInDart": "effectiveFrom", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6220, "name": "effectiveFrom"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "effective_to", "nameInDart": "effectiveTo", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6272, "name": "effectiveTo"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6333, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "PricingTiers", "row_class_name": "PricingTier", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "bills": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "bills"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6418, "name": "Bills"}, "references": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "meters"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "readings"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6456, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "bill_number", "nameInDart": "bill<PERSON><PERSON><PERSON>", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6508, "name": "bill<PERSON><PERSON><PERSON>"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "subscriber_id", "nameInDart": "subscriberId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6577, "name": "subscriberId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "meter_id", "nameInDart": "meterId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6651, "name": "meterId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "meters"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "reading_id", "nameInDart": "readingId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6715, "name": "readingId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "readings"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "billing_period", "nameInDart": "billingPeriod", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6795, "name": "billingPeriod"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 7}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "issue_date", "nameInDart": "issueDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6873, "name": "issueDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "due_date", "nameInDart": "dueDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6921, "name": "dueDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "previous_reading", "nameInDart": "previousReading", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 6988, "name": "previousReading"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "current_reading", "nameInDart": "currentReading", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7034, "name": "currentReading"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "consumption", "nameInDart": "consumption", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7079, "name": "consumption"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "water_charges", "nameInDart": "waterCharges", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7144, "name": "waterCharges"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "fixed_charges", "nameInDart": "fixedCharges", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7187, "name": "fixedCharges"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "service_charges", "nameInDart": "serviceCharges", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7261, "name": "serviceCharges"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "penalties", "nameInDart": "penalties", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7337, "name": "penalties"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "discount_amount", "nameInDart": "discountAmount", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7408, "name": "discountAmount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "tax_amount", "nameInDart": "taxAmount", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7484, "name": "taxAmount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "total_amount", "nameInDart": "totalAmount", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7555, "name": "totalAmount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7619, "name": "status"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('pending')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "paid_amount", "nameInDart": "paidAmount", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7715, "name": "paidAmount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "remaining_amount", "nameInDart": "remainingAmount", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7787, "name": "remainingAmount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "notes", "nameInDart": "notes", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7856, "name": "notes"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_estimated", "nameInDart": "isEstimated", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7903, "name": "isEstimated"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "generated_by", "nameInDart": "generatedBy", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 7982, "name": "generatedBy"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8065, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8145, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Bills", "row_class_name": "Bill", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "payments": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "payments"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8231, "name": "Payments"}, "references": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "bills"}, {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8272, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "payment_number", "nameInDart": "paymentNumber", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8324, "name": "paymentNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "subscriber_id", "nameInDart": "subscriberId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8396, "name": "subscriberId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "subscribers"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "bill_id", "nameInDart": "billId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8470, "name": "billId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "bills"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "amount", "nameInDart": "amount", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8565, "name": "amount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "payment_date", "nameInDart": "paymentDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8606, "name": "paymentDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "payment_method", "nameInDart": "paymentMethod", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8652, "name": "paymentMethod"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "currency_code", "nameInDart": "currencyCode", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8716, "name": "currencyCode"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('SAR')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 3}], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "exchange_rate", "nameInDart": "exchangeRate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8813, "name": "exchangeRate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(1.0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "receipt_number", "nameInDart": "receiptNumber", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8911, "name": "receiptNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "bank_reference", "nameInDart": "bankReference", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 8986, "name": "bankReference"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "check_number", "nameInDart": "checkNumber", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9062, "name": "checkNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "collected_by", "nameInDart": "collectedBy", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9157, "name": "collectedBy"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "collection_location", "nameInDart": "collectionLocation", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9236, "name": "collectionLocation"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9341, "name": "status"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('confirmed')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "confirmed_by", "nameInDart": "confirmedBy", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9438, "name": "confirmedBy"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "confirmation_date", "nameInDart": "confirmationDate", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9521, "name": "confirmationDate"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "notes", "nameInDart": "notes", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9606, "name": "notes"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "attachment_path", "nameInDart": "attachmentPath", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9653, "name": "attachmentPath"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 255}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9734, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9814, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Payments", "row_class_name": "Payment", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "app_settings": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "app_settings"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9906, "name": "AppSettings"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 9950, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "key", "nameInDart": "key", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10002, "name": "key"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}, {"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "value", "nameInDart": "value", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10065, "name": "value"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10101, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10158, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "AppSettings", "row_class_name": "AppSetting", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "audit_logs": {"id": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "audit_logs"}, "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10247, "name": "AuditLogs"}, "references": [{"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10289, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "table_name", "nameInDart": "tableName", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10341, "name": "tableName"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 50}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "record_id", "nameInDart": "recordId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10400, "name": "recordId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "operation", "nameInDart": "operation", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10442, "name": "operation"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 20}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "user_id", "nameInDart": "userId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10527, "name": "userId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:water_management_app/core/database/app_database.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "timestamp", "nameInDart": "timestamp", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10605, "name": "timestamp"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "ip_address", "nameInDart": "ip<PERSON><PERSON><PERSON>", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10681, "name": "ip<PERSON><PERSON><PERSON>"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 45}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "user_agent", "nameInDart": "userAgent", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10752, "name": "userAgent"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "old_values", "nameInDart": "oldValues", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10820, "name": "oldValues"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "new_values", "nameInDart": "newValues", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10871, "name": "newValues"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "changed_fields", "nameInDart": "changed<PERSON>ields", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 10922, "name": "changed<PERSON>ields"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "reason", "nameInDart": "reason", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 11000, "name": "reason"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "session_id", "nameInDart": "sessionId", "declaration": {"source_uri": "package:water_management_app/core/database/app_database.dart", "offset": 11048, "name": "sessionId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": null, "max_length": 100}], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "AuditLogs", "row_class_name": "AuditLog", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:drift/native.dart", "package:path_provider/path_provider.dart", "package:path/path.dart", "package:crypto/crypto.dart"]}