import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'dart:convert';

part 'app_database.g.dart';

// جدول المستخدمين
class Users extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get username => text().withLength(min: 3, max: 50).unique()();
  TextColumn get email => text().withLength(max: 100).unique()();
  TextColumn get passwordHash => text().withLength(max: 255)();
  TextColumn get fullName => text().withLength(max: 100)();
  TextColumn get phone => text().withLength(max: 20).nullable()();
  TextColumn get role => text().withLength(max: 20)();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  BoolColumn get twoFactorEnabled => boolean().withDefault(const Constant(false))();
  TextColumn get twoFactorSecret => text().withLength(max: 32).nullable()();
  DateTimeColumn get lastLogin => dateTime().nullable()();
  IntColumn get failedLoginAttempts => integer().withDefault(const Constant(0))();
  DateTimeColumn get lockedUntil => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول القرى
class Villages extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(max: 100)();
  TextColumn get code => text().withLength(max: 20).unique()();
  TextColumn get mainMeterId => text().withLength(max: 50).nullable()();
  RealColumn get latitude => real().nullable()();
  RealColumn get longitude => real().nullable()();
  IntColumn get population => integer().nullable()();
  TextColumn get description => text().nullable()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول المشتركين
class Subscribers extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get subscriberNumber => text().withLength(max: 20).unique()();
  TextColumn get fullName => text().withLength(max: 100)();
  IntColumn get villageId => integer().references(Villages, #id)();
  TextColumn get subscriptionType => text().withLength(max: 20)();
  TextColumn get status => text().withLength(max: 20).withDefault(const Constant('active'))();
  TextColumn get phone => text().withLength(max: 20).nullable()();
  TextColumn get email => text().withLength(max: 100).nullable()();
  TextColumn get address => text().nullable()();
  TextColumn get nationalId => text().withLength(max: 20).nullable()();
  DateTimeColumn get connectionDate => dateTime().nullable()();
  DateTimeColumn get disconnectionDate => dateTime().nullable()();
  TextColumn get notes => text().nullable()();
  RealColumn get discountPercentage => real().withDefault(const Constant(0))();
  BoolColumn get isCharitable => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول العدادات
class Meters extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get serialNumber => text().withLength(max: 50).unique()();
  IntColumn get subscriberId => integer().references(Subscribers, #id)();
  TextColumn get meterType => text().withLength(max: 20).withDefault(const Constant('residential'))();
  TextColumn get status => text().withLength(max: 20).withDefault(const Constant('active'))();
  DateTimeColumn get installationDate => dateTime().nullable()();
  DateTimeColumn get lastMaintenance => dateTime().nullable()();
  DateTimeColumn get nextMaintenance => dateTime().nullable()();
  RealColumn get latitude => real().nullable()();
  RealColumn get longitude => real().nullable()();
  RealColumn get initialReading => real().withDefault(const Constant(0))();
  RealColumn get currentReading => real().withDefault(const Constant(0))();
  RealColumn get multiplier => real().withDefault(const Constant(1.0))();
  TextColumn get brand => text().withLength(max: 50).nullable()();
  TextColumn get model => text().withLength(max: 50).nullable()();
  TextColumn get diameter => text().withLength(max: 20).nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول القراءات
class Readings extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get meterId => integer().references(Meters, #id)();
  RealColumn get currentReading => real()();
  RealColumn get previousReading => real()();
  RealColumn get consumption => real()();
  DateTimeColumn get readingDate => dateTime()();
  TextColumn get readingPeriod => text().withLength(max: 7)(); // YYYY-MM
  IntColumn get readBy => integer().references(Users, #id).nullable()();
  TextColumn get readingMethod => text().withLength(max: 20)();
  BoolColumn get isValidated => boolean().withDefault(const Constant(false))();
  BoolColumn get isEstimated => boolean().withDefault(const Constant(false))();
  TextColumn get validationNotes => text().nullable()();
  TextColumn get imagePath => text().withLength(max: 255).nullable()();
  RealColumn get gpsLatitude => real().nullable()();
  RealColumn get gpsLongitude => real().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول شرائح التسعير
class PricingTiers extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get subscriptionType => text().withLength(max: 20)();
  IntColumn get tierNumber => integer()();
  RealColumn get minConsumption => real()();
  RealColumn get maxConsumption => real().nullable()();
  RealColumn get pricePerUnit => real()();
  RealColumn get fixedCharge => real().withDefault(const Constant(0))();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get effectiveFrom => dateTime()();
  DateTimeColumn get effectiveTo => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول الفواتير
class Bills extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get billNumber => text().withLength(max: 20).unique()();
  IntColumn get subscriberId => integer().references(Subscribers, #id)();
  IntColumn get meterId => integer().references(Meters, #id)();
  IntColumn get readingId => integer().references(Readings, #id).nullable()();
  TextColumn get billingPeriod => text().withLength(max: 7)(); // YYYY-MM
  DateTimeColumn get issueDate => dateTime()();
  DateTimeColumn get dueDate => dateTime()();
  
  // تفاصيل الاستهلاك
  RealColumn get previousReading => real()();
  RealColumn get currentReading => real()();
  RealColumn get consumption => real()();
  
  // تفاصيل المبالغ
  RealColumn get waterCharges => real()();
  RealColumn get fixedCharges => real().withDefault(const Constant(0))();
  RealColumn get serviceCharges => real().withDefault(const Constant(0))();
  RealColumn get penalties => real().withDefault(const Constant(0))();
  RealColumn get discountAmount => real().withDefault(const Constant(0))();
  RealColumn get taxAmount => real().withDefault(const Constant(0))();
  RealColumn get totalAmount => real()();
  
  // الحالة والدفع
  TextColumn get status => text().withLength(max: 20).withDefault(const Constant('pending'))();
  RealColumn get paidAmount => real().withDefault(const Constant(0))();
  RealColumn get remainingAmount => real()();
  
  // معلومات إضافية
  TextColumn get notes => text().nullable()();
  BoolColumn get isEstimated => boolean().withDefault(const Constant(false))();
  IntColumn get generatedBy => integer().references(Users, #id).nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول المدفوعات
class Payments extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get paymentNumber => text().withLength(max: 20).unique()();
  IntColumn get subscriberId => integer().references(Subscribers, #id)();
  IntColumn get billId => integer().references(Bills, #id).nullable()();
  
  // تفاصيل الدفع
  RealColumn get amount => real()();
  DateTimeColumn get paymentDate => dateTime()();
  TextColumn get paymentMethod => text().withLength(max: 20)();
  TextColumn get currencyCode => text().withLength(max: 3).withDefault(const Constant('SAR'))();
  RealColumn get exchangeRate => real().withDefault(const Constant(1.0))();
  
  // معلومات السند
  TextColumn get receiptNumber => text().withLength(max: 50).nullable()();
  TextColumn get bankReference => text().withLength(max: 100).nullable()();
  TextColumn get checkNumber => text().withLength(max: 50).nullable()();
  
  // معلومات المحصل
  IntColumn get collectedBy => integer().references(Users, #id).nullable()();
  TextColumn get collectionLocation => text().withLength(max: 100).nullable()();
  
  // الحالة والتأكيد
  TextColumn get status => text().withLength(max: 20).withDefault(const Constant('confirmed'))();
  IntColumn get confirmedBy => integer().references(Users, #id).nullable()();
  DateTimeColumn get confirmationDate => dateTime().nullable()();
  
  // معلومات إضافية
  TextColumn get notes => text().nullable()();
  TextColumn get attachmentPath => text().withLength(max: 255).nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول إعدادات التطبيق
class AppSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get key => text().withLength(max: 50).unique()();
  TextColumn get value => text()();
  TextColumn get description => text().nullable()();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول سجل العمليات
class AuditLogs extends Table {
  @override
  String get tableName => 'audit_logs';

  IntColumn get id => integer().autoIncrement()();
  TextColumn get tableNameField => text().withLength(max: 50)();
  IntColumn get recordId => integer()();
  TextColumn get operation => text().withLength(max: 20)();
  
  // المستخدم والتوقيت
  IntColumn get userId => integer().references(Users, #id).nullable()();
  DateTimeColumn get timestamp => dateTime().withDefault(currentDateAndTime)();
  TextColumn get ipAddress => text().withLength(max: 45).nullable()();
  TextColumn get userAgent => text().nullable()();
  
  // البيانات
  TextColumn get oldValues => text().nullable()();
  TextColumn get newValues => text().nullable()();
  TextColumn get changedFields => text().nullable()();
  
  // معلومات إضافية
  TextColumn get reason => text().nullable()();
  TextColumn get sessionId => text().withLength(max: 100).nullable()();
}

@DriftDatabase(tables: [
  Users,
  Villages,
  Subscribers,
  Meters,
  Readings,
  PricingTiers,
  Bills,
  Payments,
  AppSettings,
  AuditLogs,
])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
      await _insertDefaultData();
      await _createIndexes();
    },
    onUpgrade: (Migrator m, int from, int to) async {
      // منطق ترقية قاعدة البيانات
    },
  );

  // إدراج البيانات الافتراضية
  Future<void> _insertDefaultData() async {
    // إنشاء مستخدم المدير الافتراضي
    await into(users).insert(UsersCompanion.insert(
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: _hashPassword('admin123'),
      fullName: 'مدير النظام',
      role: 'admin',
    ));

    // إنشاء قرية افتراضية
    await into(villages).insert(VillagesCompanion.insert(
      name: 'القرية الرئيسية',
      code: 'MAIN001',
    ));

    // إدراج الإعدادات الافتراضية
    final defaultSettings = [
      {'key': 'currency', 'value': 'SAR', 'description': 'العملة الافتراضية'},
      {'key': 'language', 'value': 'ar', 'description': 'اللغة الافتراضية'},
      {'key': 'theme_mode', 'value': 'system', 'description': 'وضع الثيم'},
      {'key': 'auto_backup', 'value': 'true', 'description': 'النسخ الاحتياطي التلقائي'},
      {'key': 'session_timeout', 'value': '480', 'description': 'مهلة انتهاء الجلسة بالدقائق'},
    ];

    for (final setting in defaultSettings) {
      await into(appSettings).insert(AppSettingsCompanion.insert(
        key: setting['key']!,
        value: setting['value']!,
        description: Value(setting['description']),
      ));
    }

    // إدراج شرائح التسعير الافتراضية للاشتراك السكني
    final pricingTiers = [
      {'tier': 1, 'min': 0.0, 'max': 10.0, 'price': 0.5},
      {'tier': 2, 'min': 10.0, 'max': 20.0, 'price': 1.0},
      {'tier': 3, 'min': 20.0, 'max': null, 'price': 1.5},
    ];

    for (final tier in pricingTiers) {
      await into(this.pricingTiers).insert(PricingTiersCompanion.insert(
        subscriptionType: 'residential',
        tierNumber: tier['tier'] as int,
        minConsumption: tier['min'] as double,
        maxConsumption: Value(tier['max'] as double?),
        pricePerUnit: tier['price'] as double,
        effectiveFrom: DateTime.now(),
      ));
    }
  }

  // إنشاء الفهارس
  Future<void> _createIndexes() async {
    // سيتم إضافة الفهارس هنا إذا لزم الأمر
  }

  // تشفير كلمة المرور
  String _hashPassword(String password) {
    final bytes = utf8.encode(password + 'water_salt_2024');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // تشفير كلمة المرور (دالة عامة)
  String hashPassword(String password) {
    return _hashPassword(password);
  }

  // التحقق من كلمة المرور
  bool verifyPassword(String password, String hashedPassword) {
    return _hashPassword(password) == hashedPassword;
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'water_management.db'));
    return NativeDatabase(file);
  });
}
