import 'package:drift/drift.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/services/database_service.dart';
import '../../domain/entities/report_entity.dart';

/// مستودع التقارير
class ReportRepository {
  final AppDatabase _database = DatabaseService.instance.database;

  /// إنشاء تقرير الإحصائيات العامة
  Future<ReportEntity> generateStatisticsReport() async {
    try {
      final data = <String, dynamic>{};
      
      // إحصائيات المشتركين
      final subscribersCount = await _database.select(_database.subscribers).get()
          .then((list) => list.length);
      final activeSubscribers = await (_database.select(_database.subscribers)
          ..where((s) => s.status.equals('active'))).get()
          .then((list) => list.length);
      
      data['subscribers'] = {
        'total': subscribersCount,
        'active': activeSubscribers,
        'inactive': subscribersCount - activeSubscribers,
      };

      // إحصائيات العدادات
      final metersCount = await _database.select(_database.meters).get()
          .then((list) => list.length);
      final activeMeters = await (_database.select(_database.meters)
          ..where((m) => m.status.equals('active'))).get()
          .then((list) => list.length);
      
      data['meters'] = {
        'total': metersCount,
        'active': activeMeters,
        'inactive': metersCount - activeMeters,
      };

      // إحصائيات القراءات
      final readingsCount = await _database.select(_database.readings).get()
          .then((list) => list.length);
      final validatedReadings = await (_database.select(_database.readings)
          ..where((r) => r.isValidated.equals(true))).get()
          .then((list) => list.length);
      
      data['readings'] = {
        'total': readingsCount,
        'validated': validatedReadings,
        'pending': readingsCount - validatedReadings,
      };

      // إحصائيات الفواتير
      final billsCount = await _database.select(_database.bills).get()
          .then((list) => list.length);
      final paidBills = await (_database.select(_database.bills)
          ..where((b) => b.status.equals('paid'))).get()
          .then((list) => list.length);
      
      data['bills'] = {
        'total': billsCount,
        'paid': paidBills,
        'unpaid': billsCount - paidBills,
      };

      // إحصائيات المدفوعات
      final paymentsCount = await _database.select(_database.payments).get()
          .then((list) => list.length);
      final completedPayments = await (_database.select(_database.payments)
          ..where((p) => p.status.equals('completed'))).get()
          .then((list) => list.length);
      
      data['payments'] = {
        'total': paymentsCount,
        'completed': completedPayments,
        'pending': paymentsCount - completedPayments,
      };

      // إحصائيات القرى
      final villagesCount = await _database.select(_database.villages).get()
          .then((list) => list.length);
      final activeVillages = await (_database.select(_database.villages)
          ..where((v) => v.isActive.equals(true))).get()
          .then((list) => list.length);
      
      data['villages'] = {
        'total': villagesCount,
        'active': activeVillages,
        'inactive': villagesCount - activeVillages,
      };

      return ReportEntity.quickStats(
        generatedBy: 'النظام',
        data: data,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير الإحصائيات: $e');
    }
  }

  /// إنشاء التقرير المالي
  Future<ReportEntity> generateFinancialReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      // إجمالي الفواتير في الفترة
      final billsInPeriod = await (_database.select(_database.bills)
          ..where((b) => b.issueDate.isBetweenValues(startDate, endDate)))
          .get();
      
      final totalBillsAmount = billsInPeriod.fold<double>(
        0.0,
        (sum, bill) => sum + bill.totalAmount,
      );
      
      data['bills'] = {
        'count': billsInPeriod.length,
        'total_amount': totalBillsAmount,
        'by_status': _groupBillsByStatus(billsInPeriod),
      };

      // إجمالي المدفوعات في الفترة
      final paymentsInPeriod = await (_database.select(_database.payments)
          ..where((p) => p.paymentDate.isBetweenValues(startDate, endDate)))
          .get();
      
      final totalPaymentsAmount = paymentsInPeriod.fold<double>(
        0.0,
        (sum, payment) => sum + payment.amount,
      );
      
      data['payments'] = {
        'count': paymentsInPeriod.length,
        'total_amount': totalPaymentsAmount,
        'by_method': _groupPaymentsByMethod(paymentsInPeriod),
        'by_status': _groupPaymentsByStatus(paymentsInPeriod),
      };

      // المتأخرات
      final overdueBills = await (_database.select(_database.bills)
          ..where((b) => b.dueDate.isSmallerThanValue(DateTime.now()) &
                        b.status.isNotIn(['paid', 'cancelled'])))
          .get();
      
      final overdueAmount = overdueBills.fold<double>(
        0.0,
        (sum, bill) => sum + bill.remainingAmount,
      );
      
      data['overdue'] = {
        'count': overdueBills.length,
        'amount': overdueAmount,
      };

      // الملخص المالي
      data['summary'] = {
        'total_revenue': totalPaymentsAmount,
        'total_billed': totalBillsAmount,
        'collection_rate': totalBillsAmount > 0 ? (totalPaymentsAmount / totalBillsAmount) * 100 : 0,
        'outstanding_amount': totalBillsAmount - totalPaymentsAmount,
      };

      return ReportEntity.financial(
        startDate: startDate,
        endDate: endDate,
        generatedBy: 'النظام',
        data: data,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء التقرير المالي: $e');
    }
  }

  /// إنشاء تقرير المتأخرات
  Future<ReportEntity> generateOverdueReport() async {
    try {
      final data = <String, dynamic>{};
      
      // الفواتير المتأخرة
      final overdueBills = await (_database.select(_database.bills).join([
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.bills.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(
        _database.bills.dueDate.isSmallerThanValue(DateTime.now()) &
        _database.bills.status.isNotIn(['paid', 'cancelled'])
      )).get();

      final overdueList = overdueBills.map((row) {
        final bill = row.readTable(_database.bills);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return {
          'bill_number': bill.billNumber,
          'subscriber_name': subscriber?.fullName ?? 'غير محدد',
          'village_name': village?.name ?? 'غير محدد',
          'due_date': bill.dueDate.toIso8601String(),
          'amount': bill.remainingAmount,
          'days_overdue': DateTime.now().difference(bill.dueDate).inDays,
        };
      }).toList();

      final totalOverdueAmount = overdueList.fold<double>(
        0.0,
        (sum, item) => sum + (item['amount'] as double),
      );

      data['overdue_bills'] = overdueList;
      data['summary'] = {
        'total_count': overdueList.length,
        'total_amount': totalOverdueAmount,
        'average_days_overdue': overdueList.isNotEmpty
            ? overdueList.fold<int>(0, (sum, item) => sum + (item['days_overdue'] as int)) / overdueList.length
            : 0,
      };

      // تجميع حسب القرى
      final byVillage = <String, Map<String, dynamic>>{};
      for (final item in overdueList) {
        final villageName = item['village_name'] as String;
        if (!byVillage.containsKey(villageName)) {
          byVillage[villageName] = {'count': 0, 'amount': 0.0};
        }
        byVillage[villageName]!['count'] = (byVillage[villageName]!['count'] as int) + 1;
        byVillage[villageName]!['amount'] = (byVillage[villageName]!['amount'] as double) + (item['amount'] as double);
      }
      
      data['by_village'] = byVillage;

      return ReportEntity.overdue(
        generatedBy: 'النظام',
        data: data,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير المتأخرات: $e');
    }
  }

  /// إنشاء تقرير الاستهلاك
  Future<ReportEntity> generateConsumptionReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      // القراءات في الفترة
      final readingsInPeriod = await (_database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(
        _database.readings.readingDate.isBetweenValues(startDate, endDate)
      )).get();

      final consumptionList = readingsInPeriod.map((row) {
        final reading = row.readTable(_database.readings);
        final meter = row.readTableOrNull(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return {
          'meter_number': meter?.serialNumber ?? 'غير محدد',
          'subscriber_name': subscriber?.fullName ?? 'غير محدد',
          'village_name': village?.name ?? 'غير محدد',
          'consumption': reading.consumption,
          'reading_date': reading.readingDate.toIso8601String(),
          'subscription_type': subscriber?.subscriptionType ?? 'غير محدد',
        };
      }).toList();

      final totalConsumption = consumptionList.fold<double>(
        0.0,
        (sum, item) => sum + (item['consumption'] as double),
      );

      data['consumption_details'] = consumptionList;
      data['summary'] = {
        'total_consumption': totalConsumption,
        'average_consumption': consumptionList.isNotEmpty ? totalConsumption / consumptionList.length : 0,
        'readings_count': consumptionList.length,
      };

      // تجميع حسب القرى
      final byVillage = <String, Map<String, dynamic>>{};
      for (final item in consumptionList) {
        final villageName = item['village_name'] as String;
        if (!byVillage.containsKey(villageName)) {
          byVillage[villageName] = {'count': 0, 'consumption': 0.0};
        }
        byVillage[villageName]!['count'] = (byVillage[villageName]!['count'] as int) + 1;
        byVillage[villageName]!['consumption'] = (byVillage[villageName]!['consumption'] as double) + (item['consumption'] as double);
      }
      
      data['by_village'] = byVillage;

      // تجميع حسب نوع الاشتراك
      final byType = <String, Map<String, dynamic>>{};
      for (final item in consumptionList) {
        final type = item['subscription_type'] as String;
        if (!byType.containsKey(type)) {
          byType[type] = {'count': 0, 'consumption': 0.0};
        }
        byType[type]!['count'] = (byType[type]!['count'] as int) + 1;
        byType[type]!['consumption'] = (byType[type]!['consumption'] as double) + (item['consumption'] as double);
      }
      
      data['by_subscription_type'] = byType;

      return ReportEntity.create(
        title: 'تقرير الاستهلاك',
        description: 'تقرير تفصيلي لاستهلاك المياه في الفترة المحددة',
        type: ReportType.consumption,
        period: ReportPeriod.custom,
        startDate: startDate,
        endDate: endDate,
        generatedBy: 'النظام',
        data: data,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء تقرير الاستهلاك: $e');
    }
  }

  /// تجميع الفواتير حسب الحالة
  Map<String, dynamic> _groupBillsByStatus(List<Bill> bills) {
    final grouped = <String, int>{};
    for (final bill in bills) {
      final status = bill.status ?? 'غير محدد';
      grouped[status] = (grouped[status] ?? 0) + 1;
    }
    return grouped;
  }

  /// تجميع المدفوعات حسب الطريقة
  Map<String, dynamic> _groupPaymentsByMethod(List<Payment> payments) {
    final grouped = <String, int>{};
    for (final payment in payments) {
      final method = payment.paymentMethod;
      grouped[method] = (grouped[method] ?? 0) + 1;
    }
    return grouped;
  }

  /// تجميع المدفوعات حسب الحالة
  Map<String, dynamic> _groupPaymentsByStatus(List<Payment> payments) {
    final grouped = <String, int>{};
    for (final payment in payments) {
      final status = payment.status ?? 'غير محدد';
      grouped[status] = (grouped[status] ?? 0) + 1;
    }
    return grouped;
  }
}
