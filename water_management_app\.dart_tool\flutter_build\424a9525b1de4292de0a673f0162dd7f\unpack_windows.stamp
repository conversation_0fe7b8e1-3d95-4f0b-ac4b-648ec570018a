{"inputs": ["C:\\dev\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "C:\\dev\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_windows.dll", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_export.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_messenger.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\flutter_windows.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\icudtl.dat", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "J:\\projects3\\water\\water_management_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}