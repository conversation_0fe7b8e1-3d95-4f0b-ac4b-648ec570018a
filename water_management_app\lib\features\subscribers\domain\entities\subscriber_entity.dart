import 'package:equatable/equatable.dart';

/// كيان المشترك
class SubscriberEntity extends Equatable {
  final int id;
  final String subscriberNumber;
  final String fullName;
  final int villageId;
  final String villageName;
  final SubscriptionType subscriptionType;
  final SubscriptionStatus status;
  final String? phone;
  final String? email;
  final String? address;
  final String? nationalId;
  final DateTime? connectionDate;
  final DateTime? disconnectionDate;
  final String? notes;
  final double discountPercentage;
  final bool isCharitable;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SubscriberEntity({
    required this.id,
    required this.subscriberNumber,
    required this.fullName,
    required this.villageId,
    required this.villageName,
    required this.subscriptionType,
    required this.status,
    this.phone,
    this.email,
    this.address,
    this.nationalId,
    this.connectionDate,
    this.disconnectionDate,
    this.notes,
    required this.discountPercentage,
    required this.isCharitable,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        subscriberNumber,
        fullName,
        villageId,
        villageName,
        subscriptionType,
        status,
        phone,
        email,
        address,
        nationalId,
        connectionDate,
        disconnectionDate,
        notes,
        discountPercentage,
        isCharitable,
        createdAt,
        updatedAt,
      ];

  /// نسخ الكيان مع تعديل بعض الخصائص
  SubscriberEntity copyWith({
    int? id,
    String? subscriberNumber,
    String? fullName,
    int? villageId,
    String? villageName,
    SubscriptionType? subscriptionType,
    SubscriptionStatus? status,
    String? phone,
    String? email,
    String? address,
    String? nationalId,
    DateTime? connectionDate,
    DateTime? disconnectionDate,
    String? notes,
    double? discountPercentage,
    bool? isCharitable,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SubscriberEntity(
      id: id ?? this.id,
      subscriberNumber: subscriberNumber ?? this.subscriberNumber,
      fullName: fullName ?? this.fullName,
      villageId: villageId ?? this.villageId,
      villageName: villageName ?? this.villageName,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      status: status ?? this.status,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      nationalId: nationalId ?? this.nationalId,
      connectionDate: connectionDate ?? this.connectionDate,
      disconnectionDate: disconnectionDate ?? this.disconnectionDate,
      notes: notes ?? this.notes,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      isCharitable: isCharitable ?? this.isCharitable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'subscriber_number': subscriberNumber,
      'full_name': fullName,
      'village_id': villageId,
      'village_name': villageName,
      'subscription_type': subscriptionType.name,
      'status': status.name,
      'phone': phone,
      'email': email,
      'address': address,
      'national_id': nationalId,
      'connection_date': connectionDate?.toIso8601String(),
      'disconnection_date': disconnectionDate?.toIso8601String(),
      'notes': notes,
      'discount_percentage': discountPercentage,
      'is_charitable': isCharitable,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory SubscriberEntity.fromMap(Map<String, dynamic> map) {
    return SubscriberEntity(
      id: map['id'] as int,
      subscriberNumber: map['subscriber_number'] as String,
      fullName: map['full_name'] as String,
      villageId: map['village_id'] as int,
      villageName: map['village_name'] as String? ?? '',
      subscriptionType: SubscriptionType.values.firstWhere(
        (e) => e.name == map['subscription_type'],
        orElse: () => SubscriptionType.residential,
      ),
      status: SubscriptionStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => SubscriptionStatus.active,
      ),
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      address: map['address'] as String?,
      nationalId: map['national_id'] as String?,
      connectionDate: map['connection_date'] != null
          ? DateTime.parse(map['connection_date'] as String)
          : null,
      disconnectionDate: map['disconnection_date'] != null
          ? DateTime.parse(map['disconnection_date'] as String)
          : null,
      notes: map['notes'] as String?,
      discountPercentage: (map['discount_percentage'] as num?)?.toDouble() ?? 0.0,
      isCharitable: map['is_charitable'] as bool? ?? false,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// الحصول على لون الحالة
  Color get statusColor {
    switch (status) {
      case SubscriptionStatus.active:
        return const Color(0xFF4CAF50); // أخضر
      case SubscriptionStatus.suspended:
        return const Color(0xFFFF9800); // برتقالي
      case SubscriptionStatus.overdue:
        return const Color(0xFFF44336); // أحمر
      case SubscriptionStatus.disconnected:
        return const Color(0xFF9E9E9E); // رمادي
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get statusIcon {
    switch (status) {
      case SubscriptionStatus.active:
        return Icons.check_circle;
      case SubscriptionStatus.suspended:
        return Icons.pause_circle;
      case SubscriptionStatus.overdue:
        return Icons.warning;
      case SubscriptionStatus.disconnected:
        return Icons.cancel;
    }
  }

  /// الحصول على نص الحالة
  String get statusText {
    switch (status) {
      case SubscriptionStatus.active:
        return 'نشط';
      case SubscriptionStatus.suspended:
        return 'معلق';
      case SubscriptionStatus.overdue:
        return 'متأخر';
      case SubscriptionStatus.disconnected:
        return 'مقطوع';
    }
  }

  /// الحصول على نص نوع الاشتراك
  String get subscriptionTypeText {
    switch (subscriptionType) {
      case SubscriptionType.residential:
        return 'سكني';
      case SubscriptionType.commercial:
        return 'تجاري';
      case SubscriptionType.industrial:
        return 'صناعي';
      case SubscriptionType.charitable:
        return 'خيري';
      case SubscriptionType.governmental:
        return 'حكومي';
    }
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return subscriberNumber.isNotEmpty &&
        fullName.isNotEmpty &&
        villageId > 0;
  }
}

/// أنواع الاشتراك
enum SubscriptionType {
  residential,
  commercial,
  industrial,
  charitable,
  governmental,
}

/// حالات الاشتراك
enum SubscriptionStatus {
  active,
  suspended,
  overdue,
  disconnected,
}

// إضافة الاستيرادات المطلوبة
import 'package:flutter/material.dart';
