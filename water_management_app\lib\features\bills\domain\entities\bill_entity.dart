import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// حالات الفاتورة
enum BillStatus {
  draft,
  issued,
  paid,
  overdue,
  cancelled,
}

/// أنواع الفاتورة
enum BillType {
  regular,
  estimated,
  final_bill,
  adjustment,
}

/// كيان الفاتورة
class BillEntity extends Equatable {
  final int id;
  final String billNumber;
  final int readingId;
  final int subscriberId;
  final String subscriberName;
  final String villageName;
  final String meterNumber;
  final double previousReading;
  final double currentReading;
  final double consumption;
  final DateTime billingPeriodStart;
  final DateTime billingPeriodEnd;
  final DateTime issueDate;
  final DateTime dueDate;
  final BillType type;
  final BillStatus status;
  final double unitPrice;
  final double waterAmount;
  final double sewerageAmount;
  final double maintenanceAmount;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BillEntity({
    required this.id,
    required this.billNumber,
    required this.readingId,
    required this.subscriberId,
    required this.subscriberName,
    required this.villageName,
    required this.meterNumber,
    required this.previousReading,
    required this.currentReading,
    required this.consumption,
    required this.billingPeriodStart,
    required this.billingPeriodEnd,
    required this.issueDate,
    required this.dueDate,
    required this.type,
    required this.status,
    required this.unitPrice,
    required this.waterAmount,
    required this.sewerageAmount,
    required this.maintenanceAmount,
    required this.taxAmount,
    required this.discountAmount,
    required this.totalAmount,
    required this.paidAmount,
    required this.remainingAmount,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        billNumber,
        readingId,
        subscriberId,
        subscriberName,
        villageName,
        meterNumber,
        previousReading,
        currentReading,
        consumption,
        billingPeriodStart,
        billingPeriodEnd,
        issueDate,
        dueDate,
        type,
        status,
        unitPrice,
        waterAmount,
        sewerageAmount,
        maintenanceAmount,
        taxAmount,
        discountAmount,
        totalAmount,
        paidAmount,
        remainingAmount,
        notes,
        createdAt,
        updatedAt,
      ];

  /// نسخ الكيان مع تعديل بعض الخصائص
  BillEntity copyWith({
    int? id,
    String? billNumber,
    int? readingId,
    int? subscriberId,
    String? subscriberName,
    String? villageName,
    String? meterNumber,
    double? previousReading,
    double? currentReading,
    double? consumption,
    DateTime? billingPeriodStart,
    DateTime? billingPeriodEnd,
    DateTime? issueDate,
    DateTime? dueDate,
    BillType? type,
    BillStatus? status,
    double? unitPrice,
    double? waterAmount,
    double? sewerageAmount,
    double? maintenanceAmount,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BillEntity(
      id: id ?? this.id,
      billNumber: billNumber ?? this.billNumber,
      readingId: readingId ?? this.readingId,
      subscriberId: subscriberId ?? this.subscriberId,
      subscriberName: subscriberName ?? this.subscriberName,
      villageName: villageName ?? this.villageName,
      meterNumber: meterNumber ?? this.meterNumber,
      previousReading: previousReading ?? this.previousReading,
      currentReading: currentReading ?? this.currentReading,
      consumption: consumption ?? this.consumption,
      billingPeriodStart: billingPeriodStart ?? this.billingPeriodStart,
      billingPeriodEnd: billingPeriodEnd ?? this.billingPeriodEnd,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      type: type ?? this.type,
      status: status ?? this.status,
      unitPrice: unitPrice ?? this.unitPrice,
      waterAmount: waterAmount ?? this.waterAmount,
      sewerageAmount: sewerageAmount ?? this.sewerageAmount,
      maintenanceAmount: maintenanceAmount ?? this.maintenanceAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bill_number': billNumber,
      'reading_id': readingId,
      'subscriber_id': subscriberId,
      'subscriber_name': subscriberName,
      'village_name': villageName,
      'meter_number': meterNumber,
      'previous_reading': previousReading,
      'current_reading': currentReading,
      'consumption': consumption,
      'billing_period_start': billingPeriodStart.toIso8601String(),
      'billing_period_end': billingPeriodEnd.toIso8601String(),
      'issue_date': issueDate.toIso8601String(),
      'due_date': dueDate.toIso8601String(),
      'type': type.name,
      'status': status.name,
      'unit_price': unitPrice,
      'water_amount': waterAmount,
      'sewerage_amount': sewerageAmount,
      'maintenance_amount': maintenanceAmount,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory BillEntity.fromMap(Map<String, dynamic> map) {
    return BillEntity(
      id: map['id'] as int,
      billNumber: map['bill_number'] as String,
      readingId: map['reading_id'] as int,
      subscriberId: map['subscriber_id'] as int,
      subscriberName: map['subscriber_name'] as String? ?? '',
      villageName: map['village_name'] as String? ?? '',
      meterNumber: map['meter_number'] as String? ?? '',
      previousReading: (map['previous_reading'] as num?)?.toDouble() ?? 0.0,
      currentReading: (map['current_reading'] as num?)?.toDouble() ?? 0.0,
      consumption: (map['consumption'] as num?)?.toDouble() ?? 0.0,
      billingPeriodStart: DateTime.parse(map['billing_period_start'] as String),
      billingPeriodEnd: DateTime.parse(map['billing_period_end'] as String),
      issueDate: DateTime.parse(map['issue_date'] as String),
      dueDate: DateTime.parse(map['due_date'] as String),
      type: BillType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => BillType.regular,
      ),
      status: BillStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => BillStatus.draft,
      ),
      unitPrice: (map['unit_price'] as num?)?.toDouble() ?? 0.0,
      waterAmount: (map['water_amount'] as num?)?.toDouble() ?? 0.0,
      sewerageAmount: (map['sewerage_amount'] as num?)?.toDouble() ?? 0.0,
      maintenanceAmount: (map['maintenance_amount'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (map['tax_amount'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (map['discount_amount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (map['total_amount'] as num?)?.toDouble() ?? 0.0,
      paidAmount: (map['paid_amount'] as num?)?.toDouble() ?? 0.0,
      remainingAmount: (map['remaining_amount'] as num?)?.toDouble() ?? 0.0,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// الحصول على نص نوع الفاتورة
  String get typeText {
    switch (type) {
      case BillType.regular:
        return 'عادية';
      case BillType.estimated:
        return 'تقديرية';
      case BillType.final_bill:
        return 'نهائية';
      case BillType.adjustment:
        return 'تعديل';
    }
  }

  /// الحصول على نص حالة الفاتورة
  String get statusText {
    switch (status) {
      case BillStatus.draft:
        return 'مسودة';
      case BillStatus.issued:
        return 'صادرة';
      case BillStatus.paid:
        return 'مدفوعة';
      case BillStatus.overdue:
        return 'متأخرة';
      case BillStatus.cancelled:
        return 'ملغية';
    }
  }

  /// الحصول على لون الحالة
  Color get statusColor {
    switch (status) {
      case BillStatus.draft:
        return Colors.grey;
      case BillStatus.issued:
        return Colors.blue;
      case BillStatus.paid:
        return Colors.green;
      case BillStatus.overdue:
        return Colors.red;
      case BillStatus.cancelled:
        return Colors.orange;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get statusIcon {
    switch (status) {
      case BillStatus.draft:
        return Icons.drafts;
      case BillStatus.issued:
        return Icons.receipt;
      case BillStatus.paid:
        return Icons.check_circle;
      case BillStatus.overdue:
        return Icons.warning;
      case BillStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// التحقق من انتهاء موعد الاستحقاق
  bool get isOverdue {
    return DateTime.now().isAfter(dueDate) && status != BillStatus.paid;
  }

  /// الحصول على عدد الأيام المتبقية للاستحقاق
  int get daysUntilDue {
    return dueDate.difference(DateTime.now()).inDays;
  }

  /// التحقق من إمكانية الدفع
  bool get canBePaid {
    return status == BillStatus.issued || status == BillStatus.overdue;
  }

  /// التحقق من إمكانية الإلغاء
  bool get canBeCancelled {
    return status == BillStatus.draft || status == BillStatus.issued;
  }

  /// الحصول على نسبة المبلغ المدفوع
  double get paymentPercentage {
    if (totalAmount <= 0) return 0.0;
    return (paidAmount / totalAmount) * 100;
  }

  /// التحقق من الدفع الجزئي
  bool get isPartiallyPaid {
    return paidAmount > 0 && paidAmount < totalAmount;
  }

  /// الحصول على ملخص الفاتورة
  String get summary {
    return '$billNumber - $subscriberName: ${totalAmount.toStringAsFixed(2)} ريال';
  }

  /// تحديث حالة الفاتورة
  BillEntity updateStatus(BillStatus newStatus) {
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  /// تسجيل دفعة
  BillEntity recordPayment(double amount) {
    final newPaidAmount = paidAmount + amount;
    final newRemainingAmount = totalAmount - newPaidAmount;
    final newStatus = newRemainingAmount <= 0 ? BillStatus.paid : status;

    return copyWith(
      paidAmount: newPaidAmount,
      remainingAmount: newRemainingAmount,
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء فاتورة من قراءة
  factory BillEntity.fromReading({
    required int readingId,
    required int subscriberId,
    required String subscriberName,
    required String villageName,
    required String meterNumber,
    required double previousReading,
    required double currentReading,
    required double consumption,
    required DateTime billingPeriodStart,
    required DateTime billingPeriodEnd,
    required double unitPrice,
    double sewerageRate = 0.1,
    double maintenanceRate = 0.05,
    double taxRate = 0.15,
    double discountAmount = 0.0,
    String? notes,
  }) {
    final waterAmount = consumption * unitPrice;
    final sewerageAmount = waterAmount * sewerageRate;
    final maintenanceAmount = waterAmount * maintenanceRate;
    final subtotal = waterAmount + sewerageAmount + maintenanceAmount;
    final taxAmount = subtotal * taxRate;
    final totalAmount = subtotal + taxAmount - discountAmount;

    return BillEntity(
      id: 0,
      billNumber: '', // سيتم إنشاؤه تلقائياً
      readingId: readingId,
      subscriberId: subscriberId,
      subscriberName: subscriberName,
      villageName: villageName,
      meterNumber: meterNumber,
      previousReading: previousReading,
      currentReading: currentReading,
      consumption: consumption,
      billingPeriodStart: billingPeriodStart,
      billingPeriodEnd: billingPeriodEnd,
      issueDate: DateTime.now(),
      dueDate: DateTime.now().add(const Duration(days: 30)),
      type: BillType.regular,
      status: BillStatus.draft,
      unitPrice: unitPrice,
      waterAmount: waterAmount,
      sewerageAmount: sewerageAmount,
      maintenanceAmount: maintenanceAmount,
      taxAmount: taxAmount,
      discountAmount: discountAmount,
      totalAmount: totalAmount,
      paidAmount: 0.0,
      remainingAmount: totalAmount,
      notes: notes,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
