import 'package:flutter/material.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../domain/entities/subscriber_entity.dart';

class AddSubscriberDialog extends StatefulWidget {
  final SubscriberEntity? subscriber;

  const AddSubscriberDialog({
    super.key,
    this.subscriber,
  });

  @override
  State<AddSubscriberDialog> createState() => _AddSubscriberDialogState();
}

class _AddSubscriberDialogState extends State<AddSubscriberDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _notesController = TextEditingController();

  SubscriptionType _selectedType = SubscriptionType.residential;
  SubscriptionStatus _selectedStatus = SubscriptionStatus.active;
  int _selectedVillageId = 1;
  bool _isCharitable = false;
  double _discountPercentage = 0.0;

  @override
  void initState() {
    super.initState();
    if (widget.subscriber != null) {
      _initializeWithSubscriber();
    }
  }

  void _initializeWithSubscriber() {
    final subscriber = widget.subscriber!;
    _nameController.text = subscriber.fullName;
    _phoneController.text = subscriber.phone ?? '';
    _emailController.text = subscriber.email ?? '';
    _addressController.text = subscriber.address ?? '';
    _nationalIdController.text = subscriber.nationalId ?? '';
    _notesController.text = subscriber.notes ?? '';
    _selectedType = subscriber.subscriptionType;
    _selectedStatus = subscriber.status;
    _selectedVillageId = subscriber.villageId;
    _isCharitable = subscriber.isCharitable;
    _discountPercentage = subscriber.discountPercentage;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _nationalIdController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      title: widget.subscriber == null ? 'إضافة مشترك جديد' : 'تعديل المشترك',
      scrollable: true,
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildNameField(),
            const SizedBox(height: 16),
            _buildContactFields(),
            const SizedBox(height: 16),
            _buildSubscriptionFields(),
            const SizedBox(height: 16),
            _buildAdditionalFields(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveSubscriber,
          child: Text(widget.subscriber == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: 'الاسم الكامل *',
        border: OutlineInputBorder(),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'الاسم الكامل مطلوب';
        }
        return null;
      },
    );
  }

  Widget _buildContactFields() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _addressController,
          decoration: const InputDecoration(
            labelText: 'العنوان',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _nationalIdController,
          decoration: const InputDecoration(
            labelText: 'رقم الهوية',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionFields() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<SubscriptionType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الاشتراك',
                  border: OutlineInputBorder(),
                ),
                items: SubscriptionType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getTypeText(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<SubscriptionStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'حالة الاشتراك',
                  border: OutlineInputBorder(),
                ),
                items: SubscriptionStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<int>(
          value: _selectedVillageId,
          decoration: const InputDecoration(
            labelText: 'القرية',
            border: OutlineInputBorder(),
          ),
          items: const [
            DropdownMenuItem(value: 1, child: Text('القرية الأولى')),
            DropdownMenuItem(value: 2, child: Text('القرية الثانية')),
            DropdownMenuItem(value: 3, child: Text('القرية الثالثة')),
          ],
          onChanged: (value) {
            setState(() {
              _selectedVillageId = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildAdditionalFields() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: _discountPercentage.toString(),
                decoration: const InputDecoration(
                  labelText: 'نسبة الخصم (%)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _discountPercentage = double.tryParse(value) ?? 0.0;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: CheckboxListTile(
                title: const Text('اشتراك خيري'),
                value: _isCharitable,
                onChanged: (value) {
                  setState(() {
                    _isCharitable = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  String _getTypeText(SubscriptionType type) {
    switch (type) {
      case SubscriptionType.residential:
        return 'سكني';
      case SubscriptionType.commercial:
        return 'تجاري';
      case SubscriptionType.industrial:
        return 'صناعي';
      case SubscriptionType.charitable:
        return 'خيري';
      case SubscriptionType.governmental:
        return 'حكومي';
    }
  }

  String _getStatusText(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return 'نشط';
      case SubscriptionStatus.suspended:
        return 'معلق';
      case SubscriptionStatus.overdue:
        return 'متأخر';
      case SubscriptionStatus.disconnected:
        return 'مقطوع';
    }
  }

  void _saveSubscriber() {
    if (_formKey.currentState!.validate()) {
      // TODO: حفظ المشترك
      Navigator.of(context).pop(true);
    }
  }
}
