{"valid_import": true, "imports": [{"uri": "package:drift/drift.dart", "transitive": false}, {"uri": "package:drift/native.dart", "transitive": false}, {"uri": "package:path_provider/path_provider.dart", "transitive": false}, {"uri": "package:path/path.dart", "transitive": false}, {"uri": "package:crypto/crypto.dart", "transitive": false}], "elements": [{"kind": "database", "name": "AppDatabase", "dart_name": "AppDatabase"}, {"kind": "table", "name": "users", "dart_name": "Users"}, {"kind": "table", "name": "villages", "dart_name": "Villages"}, {"kind": "table", "name": "subscribers", "dart_name": "Subscribers"}, {"kind": "table", "name": "meters", "dart_name": "Meters"}, {"kind": "table", "name": "readings", "dart_name": "Readings"}, {"kind": "table", "name": "pricing_tiers", "dart_name": "PricingTiers"}, {"kind": "table", "name": "bills", "dart_name": "Bills"}, {"kind": "table", "name": "payments", "dart_name": "Payments"}, {"kind": "table", "name": "app_settings", "dart_name": "AppSettings"}, {"kind": "table", "name": "audit_logs", "dart_name": "AuditLogs"}]}