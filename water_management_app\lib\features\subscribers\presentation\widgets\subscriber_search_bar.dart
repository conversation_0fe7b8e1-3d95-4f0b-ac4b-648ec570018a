import 'package:flutter/material.dart';

class SubscriberSearchBar extends StatefulWidget {
  final String? initialValue;
  final Function(String) onChanged;
  final VoidCallback? onClear;
  final String hintText;

  const SubscriberSearchBar({
    super.key,
    this.initialValue,
    required this.onChanged,
    this.onClear,
    this.hintText = 'البحث في المشتركين...',
  });

  @override
  State<SubscriberSearchBar> createState() => _SubscriberSearchBarState();
}

class _SubscriberSearchBarState extends State<SubscriberSearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      decoration: InputDecoration(
        hintText: widget.hintText,
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _controller.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _controller.clear();
                  widget.onChanged('');
                  widget.onClear?.call();
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Theme.of(context).scaffoldBackgroundColor,
      ),
      onChanged: (value) {
        setState(() {});
        widget.onChanged(value);
      },
    );
  }
}
