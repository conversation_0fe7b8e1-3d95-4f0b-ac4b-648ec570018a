{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:flutter_localizations/flutter_localizations.dart", "transitive": false}, {"uri": "package:flutter_screenutil/flutter_screenutil.dart", "transitive": false}, {"uri": "package:hive_flutter/hive_flutter.dart", "transitive": false}, {"uri": "package:water_management_app/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:water_management_app/core/services/database_service.dart", "transitive": false}, {"uri": "package:water_management_app/core/services/local_auth_service.dart", "transitive": false}, {"uri": "package:water_management_app/core/constants/app_constants.dart", "transitive": false}, {"uri": "package:water_management_app/features/auth/presentation/pages/login_page.dart", "transitive": false}], "elements": []}