import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../core/services/local_auth_service.dart';
import '../../../../core/services/database_service.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../../../../core/adaptive/adaptive_navigation.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../auth/presentation/pages/login_page.dart';
import '../../../subscribers/presentation/pages/subscribers_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  Map<String, int> _statistics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  /// تحميل الإحصائيات
  Future<void> _loadStatistics() async {
    try {
      final stats = await DatabaseService.instance.getGeneralStatistics();
      setState(() {
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage('خطأ في تحميل الإحصائيات');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildBody(),
      drawer: _buildDrawer(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(AppConstants.appName),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadStatistics,
          tooltip: 'تحديث',
        ),
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            // TODO: عرض الإشعارات
          },
          tooltip: 'الإشعارات',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: ListTile(
                leading: Icon(Icons.person),
                title: Text('الملف الشخصي'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('الإعدادات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: ListTile(
                leading: Icon(Icons.logout, color: Colors.red),
                title: Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء القائمة الجانبية
  Widget _buildDrawer() {
    final currentUser = LocalAuthService.instance.currentUserName ?? 'مستخدم';
    final currentRole = AppConstants.userRoles[LocalAuthService.instance.currentUserRole] ?? 'غير محدد';

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Icons.person,
                    size: 30,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  currentUser,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  currentRole,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            icon: Icons.dashboard,
            title: 'لوحة التحكم',
            onTap: () => Navigator.pop(context),
          ),
          _buildDrawerItem(
            icon: Icons.people,
            title: 'إدارة المشتركين',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة المشتركين
            },
          ),
          _buildDrawerItem(
            icon: Icons.location_city,
            title: 'إدارة القرى',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة القرى
            },
          ),
          _buildDrawerItem(
            icon: Icons.speed,
            title: 'إدارة العدادات',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة العدادات
            },
          ),
          _buildDrawerItem(
            icon: Icons.receipt,
            title: 'الفوترة',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة الفوترة
            },
          ),
          _buildDrawerItem(
            icon: Icons.payment,
            title: 'المدفوعات',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة المدفوعات
            },
          ),
          _buildDrawerItem(
            icon: Icons.build,
            title: 'الصيانة',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة الصيانة
            },
          ),
          _buildDrawerItem(
            icon: Icons.inventory,
            title: 'المخزون',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة المخزون
            },
          ),
          _buildDrawerItem(
            icon: Icons.assessment,
            title: 'التقارير',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة التقارير
            },
          ),
          const Divider(),
          _buildDrawerItem(
            icon: Icons.backup,
            title: 'النسخ الاحتياطية',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة النسخ الاحتياطية
            },
          ),
          _buildDrawerItem(
            icon: Icons.settings,
            title: 'الإعدادات',
            onTap: () {
              Navigator.pop(context);
              // TODO: الانتقال لصفحة الإعدادات
            },
          ),
        ],
      ),
    );
  }

  /// بناء عنصر في القائمة الجانبية
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: onTap,
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          _buildStatisticsGrid(),
          const SizedBox(height: 16),
          _buildQuickActions(),
        ],
      ),
    );
  }

  /// بناء بطاقة الترحيب
  Widget _buildWelcomeCard() {
    final currentUser = LocalAuthService.instance.currentUserName ?? 'مستخدم';
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(
              Icons.waving_hand,
              size: 32,
              color: Colors.orange,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً، $currentUser',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'أهلاً بك في نظام إدارة مشروع المياه',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة الإحصائيات
  Widget _buildStatisticsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات العامة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              title: 'إجمالي المشتركين',
              value: _statistics['total_subscribers']?.toString() ?? '0',
              icon: Icons.people,
              color: Colors.blue,
            ),
            _buildStatCard(
              title: 'المشتركين النشطين',
              value: _statistics['active_subscribers']?.toString() ?? '0',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            _buildStatCard(
              title: 'عدد القرى',
              value: _statistics['total_villages']?.toString() ?? '0',
              icon: Icons.location_city,
              color: Colors.orange,
            ),
            _buildStatCard(
              title: 'عدد العدادات',
              value: _statistics['total_meters']?.toString() ?? '0',
              icon: Icons.speed,
              color: Colors.purple,
            ),
            _buildStatCard(
              title: 'الفواتير المعلقة',
              value: _statistics['pending_bills']?.toString() ?? '0',
              icon: Icons.receipt,
              color: Colors.amber,
            ),
            _buildStatCard(
              title: 'الفواتير المتأخرة',
              value: _statistics['overdue_bills']?.toString() ?? '0',
              icon: Icons.warning,
              color: Colors.red,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionButton(
              title: 'إضافة مشترك',
              icon: Icons.person_add,
              onTap: () {
                // TODO: إضافة مشترك جديد
              },
            ),
            _buildActionButton(
              title: 'تسجيل قراءة',
              icon: Icons.add_chart,
              onTap: () {
                // TODO: تسجيل قراءة جديدة
              },
            ),
            _buildActionButton(
              title: 'إنشاء فاتورة',
              icon: Icons.receipt_long,
              onTap: () {
                // TODO: إنشاء فاتورة جديدة
              },
            ),
            _buildActionButton(
              title: 'تسجيل دفعة',
              icon: Icons.payment,
              onTap: () {
                // TODO: تسجيل دفعة جديدة
              },
            ),
          ],
        ),
      ],
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      width: (MediaQuery.of(context).size.width - 56) / 2,
      child: ElevatedButton.icon(
        onPressed: onTap,
        icon: Icon(icon),
        label: Text(title),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'profile':
        // TODO: عرض الملف الشخصي
        break;
      case 'settings':
        // TODO: عرض الإعدادات
        break;
      case 'logout':
        _logout();
        break;
    }
  }

  /// تسجيل الخروج
  Future<void> _logout() async {
    final confirmed = await _showLogoutConfirmation();
    if (confirmed) {
      await LocalAuthService.instance.logout();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const LoginPage(),
          ),
        );
      }
    }
  }

  /// عرض تأكيد تسجيل الخروج
  Future<bool> _showLogoutConfirmation() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
