import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../subscribers/presentation/providers/subscriber_provider.dart';
import '../../domain/entities/meter_entity.dart';

class AddMeterDialog extends ConsumerStatefulWidget {
  final MeterEntity? meter;

  const AddMeterDialog({
    super.key,
    this.meter,
  });

  @override
  ConsumerState<AddMeterDialog> createState() => _AddMeterDialogState();
}

class _AddMeterDialogState extends ConsumerState<AddMeterDialog> {
  final _formKey = GlobalKey<FormState>();
  final _meterNumberController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _diameterController = TextEditingController();
  final _initialReadingController = TextEditingController();
  final _currentReadingController = TextEditingController();
  final _maxCapacityController = TextEditingController();
  final _locationController = TextEditingController();
  final _serialNumberController = TextEditingController();
  final _notesController = TextEditingController();

  int? _selectedSubscriberId;
  MeterType _selectedType = MeterType.mechanical;
  MeterStatus _selectedStatus = MeterStatus.active;
  DateTime _installationDate = DateTime.now();
  DateTime? _lastMaintenanceDate;
  DateTime? _nextMaintenanceDate;
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    if (widget.meter != null) {
      _initializeWithMeter();
    }
  }

  void _initializeWithMeter() {
    final meter = widget.meter!;
    _meterNumberController.text = meter.meterNumber;
    _selectedSubscriberId = meter.subscriberId;
    _selectedType = meter.type;
    _selectedStatus = meter.status;
    _brandController.text = meter.brand ?? '';
    _modelController.text = meter.model ?? '';
    _diameterController.text = meter.diameter?.toString() ?? '';
    _initialReadingController.text = meter.initialReading.toString();
    _currentReadingController.text = meter.currentReading.toString();
    _maxCapacityController.text = meter.maxCapacity?.toString() ?? '';
    _installationDate = meter.installationDate;
    _lastMaintenanceDate = meter.lastMaintenanceDate;
    _nextMaintenanceDate = meter.nextMaintenanceDate;
    _locationController.text = meter.location ?? '';
    _serialNumberController.text = meter.serialNumber ?? '';
    _notesController.text = meter.notes ?? '';
    _isActive = meter.isActive;
  }

  @override
  void dispose() {
    _meterNumberController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _diameterController.dispose();
    _initialReadingController.dispose();
    _currentReadingController.dispose();
    _maxCapacityController.dispose();
    _locationController.dispose();
    _serialNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      title: widget.meter == null ? 'إضافة عداد جديد' : 'تعديل العداد',
      scrollable: true,
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildBasicInfo(),
            const SizedBox(height: 16),
            _buildTechnicalInfo(),
            const SizedBox(height: 16),
            _buildReadingInfo(),
            const SizedBox(height: 16),
            _buildMaintenanceInfo(),
            const SizedBox(height: 16),
            _buildAdditionalInfo(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveMeter,
          child: Text(widget.meter == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }

  /// بناء المعلومات الأساسية
  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _meterNumberController,
          decoration: const InputDecoration(
            labelText: 'رقم العداد *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.speed),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'رقم العداد مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),
        _buildSubscriberDropdown(),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<MeterType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع العداد',
                  border: OutlineInputBorder(),
                ),
                items: MeterType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getTypeText(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<MeterStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'حالة العداد',
                  border: OutlineInputBorder(),
                ),
                items: MeterStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قائمة المشتركين
  Widget _buildSubscriberDropdown() {
    final subscribersAsync = ref.watch(subscribersProvider);
    
    return subscribersAsync.when(
      data: (subscribers) => DropdownButtonFormField<int>(
        value: _selectedSubscriberId,
        decoration: const InputDecoration(
          labelText: 'المشترك *',
          border: OutlineInputBorder(),
        ),
        items: subscribers.map((subscriber) {
          return DropdownMenuItem(
            value: subscriber.id,
            child: Text('${subscriber.fullName} - ${subscriber.subscriberNumber}'),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedSubscriberId = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return 'يجب اختيار المشترك';
          }
          return null;
        },
      ),
      loading: () => const LinearProgressIndicator(),
      error: (error, stack) => Text('خطأ في تحميل المشتركين: $error'),
    );
  }

  /// بناء المعلومات التقنية
  Widget _buildTechnicalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات التقنية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _brandController,
                decoration: const InputDecoration(
                  labelText: 'الماركة',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _modelController,
                decoration: const InputDecoration(
                  labelText: 'الموديل',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _diameterController,
                decoration: const InputDecoration(
                  labelText: 'القطر (مم)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _serialNumberController,
                decoration: const InputDecoration(
                  labelText: 'الرقم التسلسلي',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _maxCapacityController,
          decoration: const InputDecoration(
            labelText: 'السعة القصوى',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }

  /// بناء معلومات القراءة
  Widget _buildReadingInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات القراءة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _initialReadingController,
                decoration: const InputDecoration(
                  labelText: 'القراءة الأولية *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'القراءة الأولية مطلوبة';
                  }
                  if (double.tryParse(value) == null) {
                    return 'قيمة غير صحيحة';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _currentReadingController,
                decoration: const InputDecoration(
                  labelText: 'القراءة الحالية *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'القراءة الحالية مطلوبة';
                  }
                  final currentReading = double.tryParse(value);
                  final initialReading = double.tryParse(_initialReadingController.text);
                  
                  if (currentReading == null) {
                    return 'قيمة غير صحيحة';
                  }
                  
                  if (initialReading != null && currentReading < initialReading) {
                    return 'لا يمكن أن تكون أقل من القراءة الأولية';
                  }
                  
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildInstallationDatePicker(),
      ],
    );
  }

  /// بناء اختيار تاريخ التركيب
  Widget _buildInstallationDatePicker() {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _installationDate,
          firstDate: DateTime(2000),
          lastDate: DateTime.now(),
        );
        if (date != null) {
          setState(() {
            _installationDate = date;
          });
        }
      },
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'تاريخ التركيب',
          border: OutlineInputBorder(),
          suffixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          '${_installationDate.day}/${_installationDate.month}/${_installationDate.year}',
        ),
      ),
    );
  }

  /// بناء معلومات الصيانة
  Widget _buildMaintenanceInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الصيانة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildMaintenanceDatePicker(
                'آخر صيانة',
                _lastMaintenanceDate,
                (date) => setState(() => _lastMaintenanceDate = date),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMaintenanceDatePicker(
                'الصيانة التالية',
                _nextMaintenanceDate,
                (date) => setState(() => _nextMaintenanceDate = date),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء اختيار تاريخ الصيانة
  Widget _buildMaintenanceDatePicker(
    String label,
    DateTime? date,
    Function(DateTime?) onDateChanged,
  ) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime(2000),
          lastDate: DateTime(2030),
        );
        onDateChanged(selectedDate);
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.calendar_today),
              if (date != null)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => onDateChanged(null),
                ),
            ],
          ),
        ),
        child: Text(
          date != null
              ? '${date.day}/${date.month}/${date.year}'
              : 'لم يتم تحديده',
        ),
      ),
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات إضافية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _locationController,
          decoration: const InputDecoration(
            labelText: 'الموقع',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Text(
                'حالة العداد',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
            ),
            Text(
              _isActive ? 'نشط' : 'غير نشط',
              style: TextStyle(
                color: _isActive ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// الحصول على نص النوع
  String _getTypeText(MeterType type) {
    switch (type) {
      case MeterType.mechanical:
        return 'ميكانيكي';
      case MeterType.digital:
        return 'رقمي';
      case MeterType.smart:
        return 'ذكي';
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(MeterStatus status) {
    switch (status) {
      case MeterStatus.active:
        return 'نشط';
      case MeterStatus.inactive:
        return 'غير نشط';
      case MeterStatus.maintenance:
        return 'صيانة';
      case MeterStatus.damaged:
        return 'معطل';
      case MeterStatus.replaced:
        return 'مستبدل';
    }
  }

  /// حفظ العداد
  void _saveMeter() {
    if (_formKey.currentState!.validate()) {
      final meter = MeterEntity(
        id: widget.meter?.id ?? 0,
        meterNumber: _meterNumberController.text.trim(),
        subscriberId: _selectedSubscriberId!,
        subscriberName: '', // سيتم تحديثه من قاعدة البيانات
        villageName: '', // سيتم تحديثه من قاعدة البيانات
        type: _selectedType,
        status: _selectedStatus,
        brand: _brandController.text.trim().isEmpty ? null : _brandController.text.trim(),
        model: _modelController.text.trim().isEmpty ? null : _modelController.text.trim(),
        diameter: _diameterController.text.trim().isEmpty ? null : double.tryParse(_diameterController.text.trim()),
        initialReading: double.parse(_initialReadingController.text.trim()),
        currentReading: double.parse(_currentReadingController.text.trim()),
        maxCapacity: _maxCapacityController.text.trim().isEmpty ? null : double.tryParse(_maxCapacityController.text.trim()),
        installationDate: _installationDate,
        lastMaintenanceDate: _lastMaintenanceDate,
        nextMaintenanceDate: _nextMaintenanceDate,
        location: _locationController.text.trim().isEmpty ? null : _locationController.text.trim(),
        serialNumber: _serialNumberController.text.trim().isEmpty ? null : _serialNumberController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        isActive: _isActive,
        createdAt: widget.meter?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // TODO: حفظ العداد باستخدام المزود
      Navigator.of(context).pop(true);
    }
  }
}
