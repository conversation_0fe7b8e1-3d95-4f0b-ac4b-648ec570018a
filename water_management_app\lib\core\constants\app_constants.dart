class AppConstants {
  // معلومات التطبيق
  static const String appName = 'نظام إدارة مشروع المياه';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'نظام شامل لإدارة مشروع المياه التعاوني';
  
  // إعدادات قاعدة البيانات
  static const String databaseName = 'water_management.db';
  static const int databaseVersion = 1;
  
  // مفاتيح التخزين المحلي
  static const String userSessionKey = 'user_session';
  static const String settingsKey = 'app_settings';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String lastBackupKey = 'last_backup';
  
  // أدوار المستخدمين
  static const Map<String, String> userRoles = {
    'admin': 'مدير النظام',
    'manager': 'مدير',
    'accountant': 'محاسب',
    'collector': 'محصل',
    'technician': 'فني',
    'financial_reviewer': 'مراجع مالي',
    'warehouse_keeper': 'أمين مخزن',
    'subscriber': 'مشترك',
  };
  
  // أنواع الاشتراكات
  static const Map<String, String> subscriptionTypes = {
    'residential': 'سكني',
    'commercial': 'تجاري',
    'industrial': 'صناعي',
    'charitable': 'خيري',
    'governmental': 'حكومي',
  };
  
  // حالات الاشتراك
  static const Map<String, String> subscriptionStatuses = {
    'active': 'نشط',
    'suspended': 'معلق',
    'overdue': 'متأخر',
    'disconnected': 'مقطوع',
  };
  
  // حالات العدادات
  static const Map<String, String> meterStatuses = {
    'active': 'نشط',
    'inactive': 'غير نشط',
    'damaged': 'تالف',
    'under_maintenance': 'تحت الصيانة',
    'replaced': 'مستبدل',
  };
  
  // طرق القراءة
  static const Map<String, String> readingMethods = {
    'manual': 'يدوي',
    'photo': 'تصوير',
    'estimated': 'تقديري',
    'automatic': 'تلقائي',
  };
  
  // حالات الفواتير
  static const Map<String, String> billStatuses = {
    'draft': 'مسودة',
    'pending': 'معلقة',
    'paid': 'مدفوعة',
    'partially_paid': 'مدفوعة جزئياً',
    'overdue': 'متأخرة',
    'cancelled': 'ملغية',
  };
  
  // طرق الدفع
  static const Map<String, String> paymentMethods = {
    'cash': 'نقدي',
    'bank_transfer': 'تحويل بنكي',
    'check': 'شيك',
    'credit_card': 'بطاقة ائتمان',
    'mobile_payment': 'دفع محمول',
  };
  
  // حالات الدفع
  static const Map<String, String> paymentStatuses = {
    'pending': 'معلق',
    'confirmed': 'مؤكد',
    'cancelled': 'ملغي',
    'refunded': 'مسترد',
  };
  
  // أنواع الصيانة
  static const Map<String, String> maintenanceTypes = {
    'repair': 'إصلاح',
    'replacement': 'استبدال',
    'installation': 'تركيب',
    'inspection': 'فحص',
    'cleaning': 'تنظيف',
    'calibration': 'معايرة',
  };
  
  // مستويات الأولوية
  static const Map<String, String> priorityLevels = {
    'low': 'منخفضة',
    'medium': 'متوسطة',
    'high': 'عالية',
    'urgent': 'عاجلة',
  };
  
  // حالات الصيانة
  static const Map<String, String> maintenanceStatuses = {
    'pending': 'معلقة',
    'assigned': 'مخصصة',
    'in_progress': 'قيد التنفيذ',
    'completed': 'مكتملة',
    'cancelled': 'ملغية',
    'on_hold': 'معلقة مؤقتاً',
  };
  
  // حالات المصروفات
  static const Map<String, String> expenseStatuses = {
    'draft': 'مسودة',
    'pending': 'معلقة',
    'approved': 'موافق عليها',
    'rejected': 'مرفوضة',
    'paid': 'مدفوعة',
  };
  
  // أنواع حركات المخزون
  static const Map<String, String> movementTypes = {
    'purchase': 'شراء',
    'sale': 'بيع',
    'usage': 'استخدام',
    'adjustment': 'تعديل',
    'transfer': 'نقل',
    'return': 'إرجاع',
    'damage': 'تلف',
    'expired': 'منتهي الصلاحية',
  };
  
  // العملات المدعومة
  static const Map<String, String> currencies = {
    'SAR': 'ريال سعودي',
    'USD': 'دولار أمريكي',
    'EUR': 'يورو',
    'EGP': 'جنيه مصري',
    'AED': 'درهم إماراتي',
  };
  
  // إعدادات التطبيق الافتراضية
  static const Map<String, dynamic> defaultSettings = {
    'currency': 'SAR',
    'language': 'ar',
    'theme_mode': 'system',
    'auto_backup': true,
    'backup_frequency': 'daily',
    'session_timeout': 480, // 8 ساعات بالدقائق
    'max_login_attempts': 3,
    'password_min_length': 6,
    'enable_2fa': false,
    'default_bill_due_days': 30,
    'low_stock_threshold': 10,
    'enable_notifications': true,
    'auto_generate_bills': true,
    'bill_generation_day': 1, // أول يوم في الشهر
  };
  
  // حدود النظام
  static const Map<String, int> systemLimits = {
    'max_subscribers_per_village': 1000,
    'max_meters_per_subscriber': 5,
    'max_readings_per_month': 50000,
    'max_file_size_mb': 10,
    'max_backup_files': 10,
    'session_timeout_minutes': 480,
    'max_failed_login_attempts': 3,
    'password_history_count': 5,
  };
  
  // رسائل الخطأ
  static const Map<String, String> errorMessages = {
    'network_error': 'خطأ في الاتصال',
    'database_error': 'خطأ في قاعدة البيانات',
    'validation_error': 'خطأ في التحقق من البيانات',
    'permission_denied': 'ليس لديك صلاحية للوصول',
    'session_expired': 'انتهت صلاحية الجلسة',
    'invalid_credentials': 'بيانات الدخول غير صحيحة',
    'user_locked': 'تم قفل الحساب مؤقتاً',
    'file_not_found': 'الملف غير موجود',
    'backup_failed': 'فشل في إنشاء النسخة الاحتياطية',
    'restore_failed': 'فشل في استعادة النسخة الاحتياطية',
  };
  
  // رسائل النجاح
  static const Map<String, String> successMessages = {
    'login_success': 'تم تسجيل الدخول بنجاح',
    'logout_success': 'تم تسجيل الخروج بنجاح',
    'save_success': 'تم الحفظ بنجاح',
    'update_success': 'تم التحديث بنجاح',
    'delete_success': 'تم الحذف بنجاح',
    'backup_success': 'تم إنشاء النسخة الاحتياطية بنجاح',
    'restore_success': 'تم استعادة النسخة الاحتياطية بنجاح',
    'payment_success': 'تم تسجيل الدفعة بنجاح',
    'bill_generated': 'تم إنشاء الفاتورة بنجاح',
  };
  
  // مسارات الملفات
  static const Map<String, String> filePaths = {
    'database': 'database/',
    'backups': 'backups/',
    'attachments': 'attachments/',
    'reports': 'reports/',
    'temp': 'temp/',
    'logs': 'logs/',
  };
  
  // تنسيقات التاريخ
  static const Map<String, String> dateFormats = {
    'display': 'dd/MM/yyyy',
    'display_with_time': 'dd/MM/yyyy HH:mm',
    'file_name': 'yyyyMMdd_HHmmss',
    'month_year': 'MM/yyyy',
    'year_month': 'yyyy-MM',
  };
  
  // أحجام الصفحات للتقارير
  static const Map<String, int> pageSizes = {
    'small': 10,
    'medium': 25,
    'large': 50,
    'xlarge': 100,
  };
  
  // أنواع التقارير
  static const Map<String, String> reportTypes = {
    'subscriber_statement': 'كشف حساب مشترك',
    'village_consumption': 'استهلاك القرية',
    'financial_summary': 'الملخص المالي',
    'maintenance_log': 'سجل الصيانة',
    'collection_report': 'تقرير التحصيل',
    'inventory_status': 'حالة المخزون',
    'audit_trail': 'سجل المراجعة',
  };
  
  // الصلاحيات
  static const Map<String, List<String>> rolePermissions = {
    'admin': ['*'], // جميع الصلاحيات
    'manager': [
      'view_reports',
      'manage_users',
      'manage_subscribers',
      'manage_billing',
      'manage_maintenance',
      'manage_inventory',
      'view_financial',
    ],
    'accountant': [
      'view_reports',
      'manage_billing',
      'manage_payments',
      'manage_expenses',
      'view_financial',
      'manage_subscribers',
    ],
    'collector': [
      'view_subscribers',
      'record_payments',
      'view_bills',
      'view_readings',
    ],
    'technician': [
      'view_maintenance',
      'update_maintenance',
      'view_meters',
      'record_readings',
    ],
    'financial_reviewer': [
      'view_reports',
      'view_financial',
      'view_audit_logs',
    ],
    'warehouse_keeper': [
      'manage_inventory',
      'view_inventory_reports',
    ],
    'subscriber': [
      'view_own_bills',
      'view_own_payments',
      'submit_maintenance_request',
    ],
  };
}
