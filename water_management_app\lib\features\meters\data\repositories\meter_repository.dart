import 'package:drift/drift.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/services/database_service.dart';
import '../../domain/entities/meter_entity.dart';

/// مستودع العدادات
class MeterRepository {
  final AppDatabase _database = DatabaseService.instance.database;

  /// الحصول على جميع العدادات
  Future<List<MeterEntity>> getAllMeters() async {
    try {
      final query = _database.select(_database.meters).join([
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ]);

      final results = await query.get();
      
      return results.map((row) {
        final meter = row.readTable(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(meter, subscriber?.fullName ?? 'غير محدد', village?.name ?? 'غير محدد');
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على العدادات: $e');
    }
  }

  /// الحصول على عداد بالمعرف
  Future<MeterEntity?> getMeterById(int id) async {
    try {
      final query = _database.select(_database.meters).join([
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.meters.id.equals(id));

      final result = await query.getSingleOrNull();
      
      if (result == null) return null;
      
      final meter = result.readTable(_database.meters);
      final subscriber = result.readTableOrNull(_database.subscribers);
      final village = result.readTableOrNull(_database.villages);
      
      return _mapToEntity(meter, subscriber?.fullName ?? 'غير محدد', village?.name ?? 'غير محدد');
    } catch (e) {
      throw Exception('خطأ في الحصول على العداد: $e');
    }
  }

  /// البحث في العدادات
  Future<List<MeterEntity>> searchMeters(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllMeters();
      }

      final searchQuery = _database.select(_database.meters).join([
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(
        _database.meters.serialNumber.like('%$query%') |
        _database.subscribers.fullName.like('%$query%') |
        _database.villages.name.like('%$query%')
      );

      final results = await searchQuery.get();
      
      return results.map((row) {
        final meter = row.readTable(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(meter, subscriber?.fullName ?? 'غير محدد', village?.name ?? 'غير محدد');
      }).toList();
    } catch (e) {
      throw Exception('خطأ في البحث عن العدادات: $e');
    }
  }

  /// الحصول على العدادات حسب المشترك
  Future<List<MeterEntity>> getMetersBySubscriber(int subscriberId) async {
    try {
      final query = _database.select(_database.meters).join([
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.meters.subscriberId.equals(subscriberId));

      final results = await query.get();
      
      return results.map((row) {
        final meter = row.readTable(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(meter, subscriber?.fullName ?? 'غير محدد', village?.name ?? 'غير محدد');
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على عدادات المشترك: $e');
    }
  }

  /// الحصول على العدادات حسب القرية
  Future<List<MeterEntity>> getMetersByVillage(int villageId) async {
    try {
      final query = _database.select(_database.meters).join([
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.subscribers.villageId.equals(villageId));

      final results = await query.get();
      
      return results.map((row) {
        final meter = row.readTable(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(meter, subscriber?.fullName ?? 'غير محدد', village?.name ?? 'غير محدد');
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على عدادات القرية: $e');
    }
  }

  /// إضافة عداد جديد
  Future<MeterEntity> createMeter(MeterEntity meter) async {
    try {
      // التحقق من عدم تكرار الرقم التسلسلي
      final existingMeter = await (_database.select(_database.meters)
        ..where((m) => m.serialNumber.equals(meter.serialNumber ?? meter.meterNumber)))
        .getSingleOrNull();

      if (existingMeter != null) {
        throw Exception('الرقم التسلسلي موجود مسبقاً');
      }

      final id = await _database.into(_database.meters).insert(
        MetersCompanion.insert(
          serialNumber: meter.serialNumber ?? meter.meterNumber,
          subscriberId: meter.subscriberId,
          meterType: Value(meter.type.name),
          status: Value(meter.status.name),
          brand: Value(meter.brand),
          model: Value(meter.model),
          diameter: Value(meter.diameter?.toString()),
          initialReading: Value(meter.initialReading),
          currentReading: Value(meter.currentReading),
          installationDate: Value(meter.installationDate),
          lastMaintenance: Value(meter.lastMaintenanceDate),
          nextMaintenance: Value(meter.nextMaintenanceDate),
        ),
      );

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'meters',
        recordId: id,
        operation: 'INSERT',
        newValues: meter.toMap(),
      );

      return meter.copyWith(id: id);
    } catch (e) {
      throw Exception('خطأ في إضافة العداد: $e');
    }
  }

  /// تحديث عداد
  Future<MeterEntity> updateMeter(MeterEntity meter) async {
    try {
      // الحصول على البيانات القديمة
      final oldMeter = await getMeterById(meter.id);
      
      await (_database.update(_database.meters)
        ..where((m) => m.id.equals(meter.id)))
        .write(MetersCompanion(
          serialNumber: Value(meter.serialNumber ?? meter.meterNumber),
          subscriberId: Value(meter.subscriberId),
          meterType: Value(meter.type.name),
          status: Value(meter.status.name),
          brand: Value(meter.brand),
          model: Value(meter.model),
          diameter: Value(meter.diameter?.toString()),
          initialReading: Value(meter.initialReading),
          currentReading: Value(meter.currentReading),
          installationDate: Value(meter.installationDate),
          lastMaintenance: Value(meter.lastMaintenanceDate),
          nextMaintenance: Value(meter.nextMaintenanceDate),
          updatedAt: Value(DateTime.now()),
        ));

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'meters',
        recordId: meter.id,
        operation: 'UPDATE',
        oldValues: oldMeter?.toMap(),
        newValues: meter.toMap(),
      );

      return meter.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      throw Exception('خطأ في تحديث العداد: $e');
    }
  }

  /// حذف عداد
  Future<void> deleteMeter(int id) async {
    try {
      // التحقق من وجود قراءات مرتبطة
      final readingsCount = await (_database.select(_database.readings)
        ..where((r) => r.meterId.equals(id)))
        .get()
        .then((readings) => readings.length);

      if (readingsCount > 0) {
        throw Exception('لا يمكن حذف العداد لوجود قراءات مرتبطة به');
      }

      // الحصول على البيانات قبل الحذف
      final meter = await getMeterById(id);

      await (_database.delete(_database.meters)
        ..where((m) => m.id.equals(id)))
        .go();

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'meters',
        recordId: id,
        operation: 'DELETE',
        oldValues: meter?.toMap(),
      );
    } catch (e) {
      throw Exception('خطأ في حذف العداد: $e');
    }
  }

  /// تحديث القراءة الحالية
  Future<MeterEntity> updateCurrentReading(int meterId, double newReading) async {
    try {
      final meter = await getMeterById(meterId);
      if (meter == null) {
        throw Exception('العداد غير موجود');
      }

      if (newReading < meter.currentReading) {
        throw Exception('القراءة الجديدة لا يمكن أن تكون أقل من القراءة الحالية');
      }

      final updatedMeter = meter.updateCurrentReading(newReading);
      return await updateMeter(updatedMeter);
    } catch (e) {
      throw Exception('خطأ في تحديث القراءة: $e');
    }
  }

  /// تحديث حالة العداد
  Future<MeterEntity> updateMeterStatus(int meterId, MeterStatus newStatus) async {
    try {
      final meter = await getMeterById(meterId);
      if (meter == null) {
        throw Exception('العداد غير موجود');
      }

      final updatedMeter = meter.updateStatus(newStatus);
      return await updateMeter(updatedMeter);
    } catch (e) {
      throw Exception('خطأ في تحديث حالة العداد: $e');
    }
  }

  /// الحصول على إحصائيات العدادات
  Future<Map<String, int>> getMetersStatistics() async {
    try {
      final stats = <String, int>{};
      
      // إجمالي العدادات
      stats['total'] = await _database.select(_database.meters).get()
        .then((list) => list.length);
      
      // العدادات النشطة
      stats['active'] = await (_database.select(_database.meters)
        ..where((m) => m.status.equals('active'))).get()
        .then((list) => list.length);
      
      // العدادات في الصيانة
      stats['maintenance'] = await (_database.select(_database.meters)
        ..where((m) => m.status.equals('maintenance'))).get()
        .then((list) => list.length);
      
      // العدادات المعطلة
      stats['damaged'] = await (_database.select(_database.meters)
        ..where((m) => m.status.equals('damaged'))).get()
        .then((list) => list.length);
      
      // العدادات غير النشطة
      stats['inactive'] = await (_database.select(_database.meters)
        ..where((m) => m.status.equals('inactive'))).get()
        .then((list) => list.length);

      return stats;
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات العدادات: $e');
    }
  }

  /// تحويل من نموذج قاعدة البيانات إلى كيان
  MeterEntity _mapToEntity(Meter meter, String subscriberName, String villageName) {
    return MeterEntity(
      id: meter.id,
      meterNumber: meter.serialNumber, // استخدام الرقم التسلسلي كرقم العداد
      subscriberId: meter.subscriberId,
      subscriberName: subscriberName,
      villageName: villageName,
      type: MeterType.values.firstWhere(
        (e) => e.name == meter.meterType,
        orElse: () => MeterType.mechanical,
      ),
      status: MeterStatus.values.firstWhere(
        (e) => e.name == meter.status,
        orElse: () => MeterStatus.active,
      ),
      brand: meter.brand,
      model: meter.model,
      diameter: meter.diameter != null ? double.tryParse(meter.diameter!) : null,
      initialReading: meter.initialReading,
      currentReading: meter.currentReading,
      maxCapacity: null, // غير متوفر في الجدول الحالي
      installationDate: meter.installationDate ?? DateTime.now(),
      lastMaintenanceDate: meter.lastMaintenance,
      nextMaintenanceDate: meter.nextMaintenance,
      location: null, // غير متوفر في الجدول الحالي
      serialNumber: meter.serialNumber,
      notes: null, // غير متوفر في الجدول الحالي
      isActive: meter.status == 'active',
      createdAt: meter.createdAt,
      updatedAt: meter.updatedAt,
    );
  }

  /// إنشاء رقم عداد تلقائي
  Future<String> generateMeterNumber() async {
    try {
      final lastMeter = await (_database.select(_database.meters)
        ..orderBy([(m) => OrderingTerm.desc(m.id)]))
        .getSingleOrNull();

      if (lastMeter == null) {
        return 'MTR001';
      }

      // استخراج الرقم من آخر عداد وزيادته
      final lastNumber = lastMeter.serialNumber;
      final numberPart = lastNumber.replaceAll(RegExp(r'[^0-9]'), '');
      final nextNumber = (int.tryParse(numberPart) ?? 0) + 1;
      
      return 'MTR${nextNumber.toString().padLeft(3, '0')}';
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم العداد: $e');
    }
  }
}
