import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/reading_repository.dart';
import '../../domain/entities/reading_entity.dart';

/// مزود مستودع القراءات
final readingRepositoryProvider = Provider<ReadingRepository>((ref) {
  return ReadingRepository();
});

/// مزود قائمة القراءات
final readingsProvider = FutureProvider<List<ReadingEntity>>((ref) async {
  final repository = ref.read(readingRepositoryProvider);
  return await repository.getAllReadings();
});

/// مزود البحث في القراءات
final readingSearchProvider = FutureProvider.family<List<ReadingEntity>, String>((ref, query) async {
  final repository = ref.read(readingRepositoryProvider);
  return await repository.searchReadings(query);
});

/// مزود القراءة الواحدة
final readingProvider = FutureProvider.family<ReadingEntity?, int>((ref, id) async {
  final repository = ref.read(readingRepositoryProvider);
  return await repository.getReadingById(id);
});

/// مزود القراءات حسب العداد
final readingsByMeterProvider = FutureProvider.family<List<ReadingEntity>, int>((ref, meterId) async {
  final repository = ref.read(readingRepositoryProvider);
  return await repository.getReadingsByMeter(meterId);
});

/// مزود القراءات حسب المشترك
final readingsBySubscriberProvider = FutureProvider.family<List<ReadingEntity>, int>((ref, subscriberId) async {
  final repository = ref.read(readingRepositoryProvider);
  return await repository.getReadingsBySubscriber(subscriberId);
});

/// مزود القراءات حسب التاريخ
final readingsByDateRangeProvider = FutureProvider.family<List<ReadingEntity>, DateRange>((ref, dateRange) async {
  final repository = ref.read(readingRepositoryProvider);
  return await repository.getReadingsByDateRange(dateRange.start, dateRange.end);
});

/// مزود إحصائيات القراءات
final readingStatisticsProvider = FutureProvider<Map<String, int>>((ref) async {
  final repository = ref.read(readingRepositoryProvider);
  return await repository.getReadingsStatistics();
});

/// مزود حالة نموذج القراءة
final readingFormProvider = StateNotifierProvider<ReadingFormNotifier, ReadingFormState>((ref) {
  final repository = ref.read(readingRepositoryProvider);
  return ReadingFormNotifier(repository);
});

/// نطاق التاريخ
class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange(this.start, this.end);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DateRange &&
          runtimeType == other.runtimeType &&
          start == other.start &&
          end == other.end;

  @override
  int get hashCode => start.hashCode ^ end.hashCode;
}

/// حالة نموذج القراءة
class ReadingFormState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final ReadingEntity? reading;

  const ReadingFormState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
    this.reading,
  });

  factory ReadingFormState.initial() {
    return const ReadingFormState(
      isLoading: false,
      isSuccess: false,
      error: null,
      reading: null,
    );
  }

  ReadingFormState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    ReadingEntity? reading,
  }) {
    return ReadingFormState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      reading: reading ?? this.reading,
    );
  }
}

/// مدير حالة نموذج القراءة
class ReadingFormNotifier extends StateNotifier<ReadingFormState> {
  final ReadingRepository _repository;

  ReadingFormNotifier(this._repository) : super(ReadingFormState.initial());

  /// حفظ قراءة (إضافة أو تحديث)
  Future<void> saveReading(ReadingEntity reading) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      ReadingEntity savedReading;
      
      if (reading.id == 0) {
        // إضافة قراءة جديدة
        savedReading = await _repository.createReading(reading);
      } else {
        // تحديث قراءة موجودة
        savedReading = await _repository.updateReading(reading);
      }
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        reading: savedReading,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// حذف قراءة
  Future<void> deleteReading(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteReading(id);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// تأكيد قراءة
  Future<void> confirmReading(int readingId, {String? readerName}) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final confirmedReading = await _repository.confirmReading(readingId, readerName: readerName);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        reading: confirmedReading,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = ReadingFormState.initial();
  }

  /// تحديث القراءة في الحالة
  void updateReading(ReadingEntity reading) {
    state = state.copyWith(reading: reading);
  }

  /// تحديث حالة التحميل
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// تحديث حالة النجاح
  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }
}

/// مزود حالة البحث في القراءات
final readingSearchStateProvider = StateNotifierProvider<ReadingSearchNotifier, ReadingSearchState>((ref) {
  return ReadingSearchNotifier();
});

/// حالة البحث في القراءات
class ReadingSearchState {
  final String query;
  final bool isSearching;
  final List<ReadingEntity> results;
  final String? error;

  const ReadingSearchState({
    required this.query,
    required this.isSearching,
    required this.results,
    this.error,
  });

  factory ReadingSearchState.initial() {
    return const ReadingSearchState(
      query: '',
      isSearching: false,
      results: [],
      error: null,
    );
  }

  ReadingSearchState copyWith({
    String? query,
    bool? isSearching,
    List<ReadingEntity>? results,
    String? error,
  }) {
    return ReadingSearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      results: results ?? this.results,
      error: error,
    );
  }
}

/// مدير حالة البحث في القراءات
class ReadingSearchNotifier extends StateNotifier<ReadingSearchState> {
  ReadingSearchNotifier() : super(ReadingSearchState.initial());

  /// تحديث نص البحث
  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  /// تحديث نتائج البحث
  void updateResults(List<ReadingEntity> results) {
    state = state.copyWith(results: results, isSearching: false);
  }

  /// تحديث حالة البحث
  void setSearching(bool isSearching) {
    state = state.copyWith(isSearching: isSearching);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error, isSearching: false);
  }

  /// مسح البحث
  void clearSearch() {
    state = ReadingSearchState.initial();
  }
}

/// مزود حالة التصفية للقراءات
final readingFilterProvider = StateNotifierProvider<ReadingFilterNotifier, ReadingFilterState>((ref) {
  return ReadingFilterNotifier();
});

/// حالة التصفية للقراءات
class ReadingFilterState {
  final ReadingStatus? statusFilter;
  final ReadingType? typeFilter;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool showEstimatedOnly;
  final bool showUnconfirmedOnly;

  const ReadingFilterState({
    this.statusFilter,
    this.typeFilter,
    this.startDate,
    this.endDate,
    required this.showEstimatedOnly,
    required this.showUnconfirmedOnly,
  });

  factory ReadingFilterState.initial() {
    return const ReadingFilterState(
      statusFilter: null,
      typeFilter: null,
      startDate: null,
      endDate: null,
      showEstimatedOnly: false,
      showUnconfirmedOnly: false,
    );
  }

  ReadingFilterState copyWith({
    ReadingStatus? statusFilter,
    ReadingType? typeFilter,
    DateTime? startDate,
    DateTime? endDate,
    bool? showEstimatedOnly,
    bool? showUnconfirmedOnly,
  }) {
    return ReadingFilterState(
      statusFilter: statusFilter ?? this.statusFilter,
      typeFilter: typeFilter ?? this.typeFilter,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      showEstimatedOnly: showEstimatedOnly ?? this.showEstimatedOnly,
      showUnconfirmedOnly: showUnconfirmedOnly ?? this.showUnconfirmedOnly,
    );
  }

  /// التحقق من وجود مرشحات نشطة
  bool get hasActiveFilters {
    return statusFilter != null ||
        typeFilter != null ||
        startDate != null ||
        endDate != null ||
        showEstimatedOnly ||
        showUnconfirmedOnly;
  }
}

/// مدير حالة التصفية للقراءات
class ReadingFilterNotifier extends StateNotifier<ReadingFilterState> {
  ReadingFilterNotifier() : super(ReadingFilterState.initial());

  /// تحديث مرشح الحالة
  void updateStatusFilter(ReadingStatus? status) {
    state = state.copyWith(statusFilter: status);
  }

  /// تحديث مرشح النوع
  void updateTypeFilter(ReadingType? type) {
    state = state.copyWith(typeFilter: type);
  }

  /// تحديث تاريخ البداية
  void updateStartDate(DateTime? date) {
    state = state.copyWith(startDate: date);
  }

  /// تحديث تاريخ النهاية
  void updateEndDate(DateTime? date) {
    state = state.copyWith(endDate: date);
  }

  /// تحديث مرشح القراءات التقديرية
  void updateEstimatedFilter(bool showEstimatedOnly) {
    state = state.copyWith(showEstimatedOnly: showEstimatedOnly);
  }

  /// تحديث مرشح القراءات غير المؤكدة
  void updateUnconfirmedFilter(bool showUnconfirmedOnly) {
    state = state.copyWith(showUnconfirmedOnly: showUnconfirmedOnly);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    state = ReadingFilterState.initial();
  }
}
