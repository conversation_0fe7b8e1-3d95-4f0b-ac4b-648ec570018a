import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../providers/meter_provider.dart';
import '../widgets/meter_card.dart';
import '../widgets/add_meter_dialog.dart';
import '../../domain/entities/meter_entity.dart';

class MetersPage extends ConsumerStatefulWidget {
  const MetersPage({super.key});

  @override
  ConsumerState<MetersPage> createState() => _MetersPageState();
}

class _MetersPageState extends ConsumerState<MetersPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildSearchAndFilters(),
          Expanded(child: _buildMetersList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return AdaptiveCard(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            Icons.speed,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة العدادات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'إدارة وتتبع عدادات المياه والصيانة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildStatisticsChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة الإحصائيات
  Widget _buildStatisticsChip() {
    final statisticsAsync = ref.watch(meterStatisticsProvider);
    
    return statisticsAsync.when(
      data: (stats) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          '${stats['total'] ?? 0} عداد',
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      loading: () => const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
      error: (_, __) => const Icon(Icons.error, color: Colors.red),
    );
  }

  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في العدادات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          
          // مرشحات سريعة
          _buildQuickFilters(),
        ],
      ),
    );
  }

  /// بناء المرشحات السريعة
  Widget _buildQuickFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildStatusFilterChip('الكل', null),
          const SizedBox(width: 8),
          _buildStatusFilterChip('نشط', MeterStatus.active),
          const SizedBox(width: 8),
          _buildStatusFilterChip('صيانة', MeterStatus.maintenance),
          const SizedBox(width: 8),
          _buildStatusFilterChip('معطل', MeterStatus.damaged),
          const SizedBox(width: 8),
          _buildTypeFilterChip('ميكانيكي', MeterType.mechanical),
          const SizedBox(width: 8),
          _buildTypeFilterChip('رقمي', MeterType.digital),
          const SizedBox(width: 8),
          _buildTypeFilterChip('ذكي', MeterType.smart),
          const SizedBox(width: 8),
          _buildMaintenanceFilterChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة مرشح الحالة
  Widget _buildStatusFilterChip(String label, MeterStatus? status) {
    final filterState = ref.watch(meterFilterProvider);
    final isSelected = filterState.statusFilter == status;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(meterFilterProvider.notifier)
            .updateStatusFilter(selected ? status : null);
      },
    );
  }

  /// بناء رقاقة مرشح النوع
  Widget _buildTypeFilterChip(String label, MeterType type) {
    final filterState = ref.watch(meterFilterProvider);
    final isSelected = filterState.typeFilter == type;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(meterFilterProvider.notifier)
            .updateTypeFilter(selected ? type : null);
      },
    );
  }

  /// بناء رقاقة مرشح الصيانة
  Widget _buildMaintenanceFilterChip() {
    final filterState = ref.watch(meterFilterProvider);

    return FilterChip(
      label: const Text('يحتاج صيانة'),
      selected: filterState.showMaintenanceNeeded,
      onSelected: (selected) {
        ref.read(meterFilterProvider.notifier)
            .updateMaintenanceFilter(selected);
      },
    );
  }

  /// بناء قائمة العدادات
  Widget _buildMetersList() {
    final metersAsync = _searchQuery.isEmpty
        ? ref.watch(metersProvider)
        : ref.watch(meterSearchProvider(_searchQuery));

    return metersAsync.when(
      data: (meters) => _buildMetersListView(meters),
      loading: () => const AdaptiveLoadingIndicator(
        message: 'جاري تحميل العدادات...',
      ),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  /// بناء عرض قائمة العدادات
  Widget _buildMetersListView(List<MeterEntity> meters) {
    final filterState = ref.watch(meterFilterProvider);
    
    // تطبيق المرشحات
    final filteredMeters = _applyFilters(meters, filterState);

    if (filteredMeters.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(metersProvider);
      },
      child: AdaptiveLayoutBuilder(
        builder: (context, layoutType) {
          if (layoutType == LayoutType.mobile) {
            return _buildMobileList(filteredMeters);
          } else {
            return _buildDesktopGrid(filteredMeters);
          }
        },
      ),
    );
  }

  /// بناء قائمة للهاتف المحمول
  Widget _buildMobileList(List<MeterEntity> meters) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: meters.length,
      itemBuilder: (context, index) {
        return MeterCard(
          meter: meters[index],
          onTap: () => _navigateToMeterDetails(meters[index]),
          onEdit: () => _editMeter(meters[index]),
          onDelete: () => _deleteMeter(meters[index]),
          onUpdateReading: () => _updateReading(meters[index]),
          onUpdateStatus: (status) => _updateStatus(meters[index], status),
        );
      },
    );
  }

  /// بناء شبكة لسطح المكتب
  Widget _buildDesktopGrid(List<MeterEntity> meters) {
    final columns = AdaptiveLayout.getGridColumns(context);
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: meters.length,
      itemBuilder: (context, index) {
        return MeterCard(
          meter: meters[index],
          onTap: () => _navigateToMeterDetails(meters[index]),
          onEdit: () => _editMeter(meters[index]),
          onDelete: () => _deleteMeter(meters[index]),
          onUpdateReading: () => _updateReading(meters[index]),
          onUpdateStatus: (status) => _updateStatus(meters[index], status),
        );
      },
    );
  }

  /// تطبيق المرشحات
  List<MeterEntity> _applyFilters(
    List<MeterEntity> meters,
    MeterFilterState filterState,
  ) {
    var filtered = meters;

    if (filterState.statusFilter != null) {
      filtered = filtered.where((m) => m.status == filterState.statusFilter).toList();
    }

    if (filterState.typeFilter != null) {
      filtered = filtered.where((m) => m.type == filterState.typeFilter).toList();
    }

    if (filterState.villageFilter != null) {
      // سيتم تطبيقه عند إضافة مرشح القرية
    }

    if (filterState.showMaintenanceNeeded) {
      filtered = filtered.where((m) => m.needsMaintenance).toList();
    }

    return filtered;
  }

  /// بناء واجهة فارغة
  Widget _buildEmptyWidget() {
    return AdaptiveEmptyState(
      icon: Icons.speed,
      title: 'لا توجد عدادات',
      subtitle: 'اضغط على زر + لإضافة عداد جديد',
      action: ElevatedButton.icon(
        onPressed: _addNewMeter,
        icon: const Icon(Icons.add),
        label: const Text('إضافة عداد'),
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget(String error) {
    return AdaptiveEmptyState(
      icon: Icons.error_outline,
      title: 'حدث خطأ في تحميل البيانات',
      subtitle: error,
      action: ElevatedButton(
        onPressed: () => ref.refresh(metersProvider),
        child: const Text('إعادة المحاولة'),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewMeter,
      tooltip: 'إضافة عداد جديد',
      child: const Icon(Icons.add),
    );
  }

  /// تغيير نص البحث
  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });
  }

  /// مسح البحث
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  /// إضافة عداد جديد
  void _addNewMeter() {
    showDialog(
      context: context,
      builder: (context) => const AddMeterDialog(),
    ).then((result) {
      if (result == true) {
        ref.refresh(metersProvider);
        _showSuccessMessage('تم إضافة العداد بنجاح');
      }
    });
  }

  /// تعديل عداد
  void _editMeter(MeterEntity meter) {
    showDialog(
      context: context,
      builder: (context) => AddMeterDialog(meter: meter),
    ).then((result) {
      if (result == true) {
        ref.refresh(metersProvider);
        _showSuccessMessage('تم تحديث العداد بنجاح');
      }
    });
  }

  /// حذف عداد
  void _deleteMeter(MeterEntity meter) {
    AdaptiveDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      content: Text('هل أنت متأكد من حذف العداد "${meter.meterNumber}"؟'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            await ref.read(meterFormProvider.notifier)
                .deleteMeter(meter.id);
            
            final state = ref.read(meterFormProvider);
            if (state.isSuccess) {
              ref.refresh(metersProvider);
              _showSuccessMessage('تم حذف العداد بنجاح');
            } else if (state.error != null) {
              _showErrorMessage(state.error!);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('حذف'),
        ),
      ],
    );
  }

  /// تحديث القراءة
  void _updateReading(MeterEntity meter) {
    final controller = TextEditingController(text: meter.currentReading.toString());
    
    AdaptiveDialog.show(
      context: context,
      title: 'تحديث القراءة',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('العداد: ${meter.meterNumber}'),
          Text('القراءة الحالية: ${meter.currentReading}'),
          const SizedBox(height: 16),
          TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'القراءة الجديدة',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            final newReading = double.tryParse(controller.text);
            if (newReading != null) {
              Navigator.of(context).pop();
              await ref.read(meterFormProvider.notifier)
                  .updateCurrentReading(meter.id, newReading);
              
              final state = ref.read(meterFormProvider);
              if (state.isSuccess) {
                ref.refresh(metersProvider);
                _showSuccessMessage('تم تحديث القراءة بنجاح');
              } else if (state.error != null) {
                _showErrorMessage(state.error!);
              }
            }
          },
          child: const Text('حفظ'),
        ),
      ],
    );
  }

  /// تحديث الحالة
  void _updateStatus(MeterEntity meter, MeterStatus newStatus) async {
    await ref.read(meterFormProvider.notifier)
        .updateMeterStatus(meter.id, newStatus);
    
    final state = ref.read(meterFormProvider);
    if (state.isSuccess) {
      ref.refresh(metersProvider);
      _showSuccessMessage('تم تحديث حالة العداد بنجاح');
    } else if (state.error != null) {
      _showErrorMessage(state.error!);
    }
  }

  /// الانتقال لتفاصيل العداد
  void _navigateToMeterDetails(MeterEntity meter) {
    AdaptiveDialog.show(
      context: context,
      title: 'تفاصيل العداد',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow('رقم العداد', meter.meterNumber),
          _buildDetailRow('المشترك', meter.subscriberName),
          _buildDetailRow('القرية', meter.villageName),
          _buildDetailRow('النوع', meter.typeText),
          _buildDetailRow('الحالة', meter.statusText),
          _buildDetailRow('القراءة الحالية', meter.currentReading.toString()),
          _buildDetailRow('إجمالي الاستهلاك', meter.totalConsumption.toString()),
          if (meter.brand != null) _buildDetailRow('الماركة', meter.brand!),
          if (meter.model != null) _buildDetailRow('الموديل', meter.model!),
          if (meter.serialNumber != null) _buildDetailRow('الرقم التسلسلي', meter.serialNumber!),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            _editMeter(meter);
          },
          child: const Text('تعديل'),
        ),
      ],
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
