import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/meter_repository.dart';
import '../../domain/entities/meter_entity.dart';

/// مزود مستودع العدادات
final meterRepositoryProvider = Provider<MeterRepository>((ref) {
  return MeterRepository();
});

/// مزود قائمة العدادات
final metersProvider = FutureProvider<List<MeterEntity>>((ref) async {
  final repository = ref.read(meterRepositoryProvider);
  return await repository.getAllMeters();
});

/// مزود البحث في العدادات
final meterSearchProvider = FutureProvider.family<List<MeterEntity>, String>((ref, query) async {
  final repository = ref.read(meterRepositoryProvider);
  return await repository.searchMeters(query);
});

/// مزود العداد الواحد
final meterProvider = FutureProvider.family<MeterEntity?, int>((ref, id) async {
  final repository = ref.read(meterRepositoryProvider);
  return await repository.getMeterById(id);
});

/// مزود العدادات حسب المشترك
final metersBySubscriberProvider = FutureProvider.family<List<MeterEntity>, int>((ref, subscriberId) async {
  final repository = ref.read(meterRepositoryProvider);
  return await repository.getMetersBySubscriber(subscriberId);
});

/// مزود العدادات حسب القرية
final metersByVillageProvider = FutureProvider.family<List<MeterEntity>, int>((ref, villageId) async {
  final repository = ref.read(meterRepositoryProvider);
  return await repository.getMetersByVillage(villageId);
});

/// مزود إحصائيات العدادات
final meterStatisticsProvider = FutureProvider<Map<String, int>>((ref) async {
  final repository = ref.read(meterRepositoryProvider);
  return await repository.getMetersStatistics();
});

/// مزود حالة نموذج العداد
final meterFormProvider = StateNotifierProvider<MeterFormNotifier, MeterFormState>((ref) {
  final repository = ref.read(meterRepositoryProvider);
  return MeterFormNotifier(repository);
});

/// حالة نموذج العداد
class MeterFormState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final MeterEntity? meter;

  const MeterFormState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
    this.meter,
  });

  factory MeterFormState.initial() {
    return const MeterFormState(
      isLoading: false,
      isSuccess: false,
      error: null,
      meter: null,
    );
  }

  MeterFormState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    MeterEntity? meter,
  }) {
    return MeterFormState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      meter: meter ?? this.meter,
    );
  }
}

/// مدير حالة نموذج العداد
class MeterFormNotifier extends StateNotifier<MeterFormState> {
  final MeterRepository _repository;

  MeterFormNotifier(this._repository) : super(MeterFormState.initial());

  /// حفظ عداد (إضافة أو تحديث)
  Future<void> saveMeter(MeterEntity meter) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      MeterEntity savedMeter;
      
      if (meter.id == 0) {
        // إضافة عداد جديد
        savedMeter = await _repository.createMeter(meter);
      } else {
        // تحديث عداد موجود
        savedMeter = await _repository.updateMeter(meter);
      }
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        meter: savedMeter,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// حذف عداد
  Future<void> deleteMeter(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteMeter(id);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// تحديث القراءة الحالية
  Future<void> updateCurrentReading(int meterId, double newReading) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final updatedMeter = await _repository.updateCurrentReading(meterId, newReading);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        meter: updatedMeter,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// تحديث حالة العداد
  Future<void> updateMeterStatus(int meterId, MeterStatus newStatus) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final updatedMeter = await _repository.updateMeterStatus(meterId, newStatus);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        meter: updatedMeter,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// إنشاء رقم عداد تلقائي
  Future<String> generateMeterNumber() async {
    try {
      return await _repository.generateMeterNumber();
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم العداد: $e');
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = MeterFormState.initial();
  }

  /// تحديث العداد في الحالة
  void updateMeter(MeterEntity meter) {
    state = state.copyWith(meter: meter);
  }

  /// تحديث حالة التحميل
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// تحديث حالة النجاح
  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }
}

/// مزود حالة البحث في العدادات
final meterSearchStateProvider = StateNotifierProvider<MeterSearchNotifier, MeterSearchState>((ref) {
  return MeterSearchNotifier();
});

/// حالة البحث في العدادات
class MeterSearchState {
  final String query;
  final bool isSearching;
  final List<MeterEntity> results;
  final String? error;

  const MeterSearchState({
    required this.query,
    required this.isSearching,
    required this.results,
    this.error,
  });

  factory MeterSearchState.initial() {
    return const MeterSearchState(
      query: '',
      isSearching: false,
      results: [],
      error: null,
    );
  }

  MeterSearchState copyWith({
    String? query,
    bool? isSearching,
    List<MeterEntity>? results,
    String? error,
  }) {
    return MeterSearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      results: results ?? this.results,
      error: error,
    );
  }
}

/// مدير حالة البحث في العدادات
class MeterSearchNotifier extends StateNotifier<MeterSearchState> {
  MeterSearchNotifier() : super(MeterSearchState.initial());

  /// تحديث نص البحث
  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  /// تحديث نتائج البحث
  void updateResults(List<MeterEntity> results) {
    state = state.copyWith(results: results, isSearching: false);
  }

  /// تحديث حالة البحث
  void setSearching(bool isSearching) {
    state = state.copyWith(isSearching: isSearching);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error, isSearching: false);
  }

  /// مسح البحث
  void clearSearch() {
    state = MeterSearchState.initial();
  }
}

/// مزود حالة التصفية للعدادات
final meterFilterProvider = StateNotifierProvider<MeterFilterNotifier, MeterFilterState>((ref) {
  return MeterFilterNotifier();
});

/// حالة التصفية للعدادات
class MeterFilterState {
  final MeterStatus? statusFilter;
  final MeterType? typeFilter;
  final int? villageFilter;
  final bool showMaintenanceNeeded;

  const MeterFilterState({
    this.statusFilter,
    this.typeFilter,
    this.villageFilter,
    required this.showMaintenanceNeeded,
  });

  factory MeterFilterState.initial() {
    return const MeterFilterState(
      statusFilter: null,
      typeFilter: null,
      villageFilter: null,
      showMaintenanceNeeded: false,
    );
  }

  MeterFilterState copyWith({
    MeterStatus? statusFilter,
    MeterType? typeFilter,
    int? villageFilter,
    bool? showMaintenanceNeeded,
  }) {
    return MeterFilterState(
      statusFilter: statusFilter ?? this.statusFilter,
      typeFilter: typeFilter ?? this.typeFilter,
      villageFilter: villageFilter ?? this.villageFilter,
      showMaintenanceNeeded: showMaintenanceNeeded ?? this.showMaintenanceNeeded,
    );
  }

  /// التحقق من وجود مرشحات نشطة
  bool get hasActiveFilters {
    return statusFilter != null ||
        typeFilter != null ||
        villageFilter != null ||
        showMaintenanceNeeded;
  }
}

/// مدير حالة التصفية للعدادات
class MeterFilterNotifier extends StateNotifier<MeterFilterState> {
  MeterFilterNotifier() : super(MeterFilterState.initial());

  /// تحديث مرشح الحالة
  void updateStatusFilter(MeterStatus? status) {
    state = state.copyWith(statusFilter: status);
  }

  /// تحديث مرشح النوع
  void updateTypeFilter(MeterType? type) {
    state = state.copyWith(typeFilter: type);
  }

  /// تحديث مرشح القرية
  void updateVillageFilter(int? villageId) {
    state = state.copyWith(villageFilter: villageId);
  }

  /// تحديث مرشح الصيانة المطلوبة
  void updateMaintenanceFilter(bool showMaintenanceNeeded) {
    state = state.copyWith(showMaintenanceNeeded: showMaintenanceNeeded);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    state = MeterFilterState.initial();
  }
}
