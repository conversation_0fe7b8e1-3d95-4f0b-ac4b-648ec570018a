# 🚀 خطوات البدء السريع - النظام المحلي

## ✅ المتطلبات المسبقة

### البرامج المطلوبة
- ✅ **Flutter SDK** (مثبت مسبقاً)
- **Git** لإدارة الكود
- **VS Code** أو **Android Studio** كمحرر
- **SQLite Browser** لفحص قاعدة البيانات (اختياري)

### التحقق من التثبيت
```bash
# التحقق من Flutter
flutter doctor

# التحقق من دعم المنصات
flutter config --list

# تفعيل دعم Desktop إذا لم يكن مفعلاً
flutter config --enable-windows-desktop
flutter config --enable-linux-desktop
flutter config --enable-macos-desktop
```

---

## 📁 إنشاء المشروع

### 1. إنشاء مشروع جديد
```bash
# إنشاء المشروع
flutter create --org com.waterproject water_management_app
cd water_management_app

# إضافة دعم جميع المنصات
flutter create --platforms=windows,macos,linux,android .

# إنشاء المجلدات المطلوبة
mkdir assets/database
mkdir assets/config
mkdir assets/images
mkdir assets/icons
```

### 2. تحديث pubspec.yaml
```yaml
name: water_management_app
description: نظام إدارة مشروع المياه التعاوني - النسخة المحلية
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Database (Local)
  sqflite: ^2.3.0
  drift: ^2.14.0
  sqlite3_flutter_libs: ^0.5.0
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1
  
  # UI & Navigation
  go_router: ^12.1.3
  flutter_screenutil: ^5.9.0
  google_fonts: ^6.1.0
  
  # Forms & Validation
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^9.1.0
  
  # File Handling
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  path: ^1.8.3
  
  # PDF & Documents
  pdf: ^3.10.4
  printing: ^5.11.0
  
  # QR & NFC
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  nfc_manager: ^3.3.0
  
  # Security & Encryption
  crypto: ^3.0.3
  encrypt: ^5.0.1
  
  # Charts
  fl_chart: ^0.65.0
  
  # Utils
  uuid: ^4.2.1
  intl: ^0.18.1
  logger: ^2.0.2+1
  
  # Backup & Archive
  archive: ^3.4.9

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  drift_dev: ^2.14.0
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  generate: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/config/
    - assets/database/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
```

### 3. تثبيت الحزم
```bash
flutter pub get
flutter pub run build_runner build
```

---

## 🏗️ إعداد الهيكل الأساسي

### 1. إنشاء هيكل المجلدات
```bash
# إنشاء هيكل lib
mkdir -p lib/core/{constants,services,utils,theme}
mkdir -p lib/features/{auth,subscribers,meters,billing,payments,reports}/{data,domain,presentation}
mkdir -p lib/shared/{widgets,models,repositories}

# إنشاء ملفات الإعدادات
touch lib/core/constants/app_constants.dart
touch lib/core/services/database_service.dart
touch lib/core/services/local_auth_service.dart
touch lib/core/theme/app_theme.dart
```

### 2. إعداد الثيم الأساسي
```dart
// lib/core/theme/app_theme.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static const Color primaryColor = Color(0xFF1976D2);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color errorColor = Color(0xFFB00020);
  
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
      ),
      textTheme: GoogleFonts.cairoTextTheme(),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
      ),
      textTheme: GoogleFonts.cairoTextTheme(ThemeData.dark().textTheme),
    );
  }
}
```

### 3. إعداد التطبيق الرئيسي
```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'core/theme/app_theme.dart';
import 'core/services/database_service.dart';
import 'features/auth/presentation/pages/login_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Hive
  await Hive.initFlutter();
  
  // تهيئة قاعدة البيانات
  await DatabaseService.instance.initialize();
  
  runApp(
    const ProviderScope(
      child: WaterManagementApp(),
    ),
  );
}

class WaterManagementApp extends StatelessWidget {
  const WaterManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'نظام إدارة مشروع المياه',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      
      locale: const Locale('ar', 'SA'),
      supportedLocales: const [
        Locale('ar', 'SA'),
        Locale('en', 'US'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      
      home: const LoginPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}
```

---

## 🗄️ إعداد قاعدة البيانات

### 1. إنشاء ملف قاعدة البيانات
```dart
// lib/core/database/app_database.dart
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'dart:io';

part 'app_database.g.dart';

// جدول المستخدمين
class Users extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get username => text().withLength(min: 3, max: 50).unique()();
  TextColumn get email => text().withLength(max: 100).unique()();
  TextColumn get passwordHash => text().withLength(max: 255)();
  TextColumn get fullName => text().withLength(max: 100)();
  TextColumn get role => text().withLength(max: 20)();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول القرى
class Villages extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(max: 100)();
  TextColumn get code => text().withLength(max: 20).unique()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}

@DriftDatabase(tables: [Users, Villages])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
      await _insertDefaultData();
    },
  );

  Future<void> _insertDefaultData() async {
    // إدراج مستخدم المدير الافتراضي
    await into(users).insert(UsersCompanion.insert(
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: 'admin123', // يجب تشفيرها
      fullName: 'مدير النظام',
      role: 'admin',
    ));
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'water_management.db'));
    return NativeDatabase(file);
  });
}
```

### 2. إنشاء خدمة قاعدة البيانات
```dart
// lib/core/services/database_service.dart
import '../database/app_database.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._init();
  DatabaseService._init();

  late AppDatabase _database;
  AppDatabase get database => _database;

  Future<void> initialize() async {
    _database = AppDatabase();
  }

  Future<void> close() async {
    await _database.close();
  }
}
```

---

## 🔐 إعداد نظام المصادقة

### 1. صفحة تسجيل الدخول
```dart
// lib/features/auth/presentation/pages/login_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          padding: const EdgeInsets.all(24),
          child: FormBuilder(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // شعار التطبيق
                Icon(
                  Icons.water_drop,
                  size: 80,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(height: 24),
                
                Text(
                  'نظام إدارة مشروع المياه',
                  style: Theme.of(context).textTheme.headlineMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                
                // حقل اسم المستخدم
                FormBuilderTextField(
                  name: 'username',
                  decoration: const InputDecoration(
                    labelText: 'اسم المستخدم',
                    prefixIcon: Icon(Icons.person),
                  ),
                  validator: FormBuilderValidators.required(),
                ),
                const SizedBox(height: 16),
                
                // حقل كلمة المرور
                FormBuilderTextField(
                  name: 'password',
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'كلمة المرور',
                    prefixIcon: Icon(Icons.lock),
                  ),
                  validator: FormBuilderValidators.required(),
                ),
                const SizedBox(height: 24),
                
                // زر تسجيل الدخول
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _login,
                    child: _isLoading
                        ? const CircularProgressIndicator()
                        : const Text('تسجيل الدخول'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _login() async {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      setState(() => _isLoading = true);
      
      final values = _formKey.currentState!.value;
      final username = values['username'] as String;
      final password = values['password'] as String;
      
      // تنفيذ منطق تسجيل الدخول
      await Future.delayed(const Duration(seconds: 1)); // محاكاة
      
      setState(() => _isLoading = false);
      
      // الانتقال للصفحة الرئيسية
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    }
  }
}
```

---

## 🧪 اختبار التطبيق

### 1. تشغيل التطبيق
```bash
# للـ Desktop (Windows)
flutter run -d windows

# للـ Android
flutter run -d android

# للـ Web (للاختبار)
flutter run -d chrome
```

### 2. بناء التطبيق
```bash
# بناء للـ Windows
flutter build windows --release

# بناء للـ Android
flutter build apk --release
```

---

## 📋 قائمة التحقق

### ✅ المهام المكتملة
- [ ] إنشاء المشروع
- [ ] إعداد pubspec.yaml
- [ ] تثبيت الحزم
- [ ] إنشاء هيكل المجلدات
- [ ] إعداد الثيم
- [ ] إعداد قاعدة البيانات
- [ ] إنشاء صفحة تسجيل الدخول
- [ ] اختبار التطبيق

### 🔄 الخطوات التالية
1. تطوير نظام المصادقة الكامل
2. إنشاء الصفحة الرئيسية
3. تطوير وحدة إدارة المشتركين
4. إضافة نظام النسخ الاحتياطية
5. تطوير باقي الوحدات

---

## 🆘 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**مشكلة**: خطأ في بناء قاعدة البيانات
```bash
# الحل
flutter packages pub run build_runner clean
flutter packages pub run build_runner build --delete-conflicting-outputs
```

**مشكلة**: خطأ في الخطوط العربية
```bash
# التأكد من وجود ملفات الخطوط في assets/fonts/
# وإضافتها في pubspec.yaml
```

**مشكلة**: خطأ في دعم Desktop
```bash
# تفعيل دعم Desktop
flutter config --enable-windows-desktop
flutter config --enable-linux-desktop
flutter config --enable-macos-desktop
```

---

*هذا الدليل يوفر نقطة انطلاق سريعة وفعالة لبدء تطوير النظام المحلي.*
