import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/village_repository.dart';
import '../../domain/entities/village_entity.dart';

/// مزود مستودع القرى
final villageRepositoryProvider = Provider<VillageRepository>((ref) {
  return VillageRepository();
});

/// مزود قائمة القرى
final villagesProvider = FutureProvider<List<VillageEntity>>((ref) async {
  final repository = ref.read(villageRepositoryProvider);
  return await repository.getAllVillages();
});

/// مزود البحث في القرى
final villageSearchProvider = FutureProvider.family<List<VillageEntity>, String>((ref, query) async {
  final repository = ref.read(villageRepositoryProvider);
  return await repository.searchVillages(query);
});

/// مزود القرية الواحدة
final villageProvider = FutureProvider.family<VillageEntity?, int>((ref, id) async {
  final repository = ref.read(villageRepositoryProvider);
  return await repository.getVillageById(id);
});

/// مزود القرى النشطة
final activeVillagesProvider = FutureProvider<List<VillageEntity>>((ref) async {
  final repository = ref.read(villageRepositoryProvider);
  return await repository.getActiveVillages();
});

/// مزود إحصائيات القرى
final villageStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(villageRepositoryProvider);
  return await repository.getVillagesStatistics();
});

/// مزود قائمة القرى للاختيار
final villageDropdownProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final repository = ref.read(villageRepositoryProvider);
  return await repository.getVillagesForDropdown();
});

/// مزود حالة نموذج القرية
final villageFormProvider = StateNotifierProvider<VillageFormNotifier, VillageFormState>((ref) {
  final repository = ref.read(villageRepositoryProvider);
  return VillageFormNotifier(repository);
});

/// حالة نموذج القرية
class VillageFormState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final VillageEntity? village;

  const VillageFormState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
    this.village,
  });

  factory VillageFormState.initial() {
    return const VillageFormState(
      isLoading: false,
      isSuccess: false,
      error: null,
      village: null,
    );
  }

  VillageFormState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    VillageEntity? village,
  }) {
    return VillageFormState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      village: village ?? this.village,
    );
  }
}

/// مدير حالة نموذج القرية
class VillageFormNotifier extends StateNotifier<VillageFormState> {
  final VillageRepository _repository;

  VillageFormNotifier(this._repository) : super(VillageFormState.initial());

  /// حفظ قرية (إضافة أو تحديث)
  Future<void> saveVillage(VillageEntity village) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      VillageEntity savedVillage;
      
      if (village.id == 0) {
        // إضافة قرية جديدة
        savedVillage = await _repository.createVillage(village);
      } else {
        // تحديث قرية موجودة
        savedVillage = await _repository.updateVillage(village);
      }
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        village: savedVillage,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// حذف قرية
  Future<void> deleteVillage(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteVillage(id);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// تفعيل/إلغاء تفعيل قرية
  Future<void> toggleVillageStatus(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final updatedVillage = await _repository.toggleVillageStatus(id);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        village: updatedVillage,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// تحديث إحصائيات جميع القرى
  Future<void> updateAllStatistics() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.updateAllVillagesStatistics();
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = VillageFormState.initial();
  }

  /// تحديث القرية في الحالة
  void updateVillage(VillageEntity village) {
    state = state.copyWith(village: village);
  }

  /// تحديث حالة التحميل
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// تحديث حالة النجاح
  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }
}

/// مزود حالة البحث في القرى
final villageSearchStateProvider = StateNotifierProvider<VillageSearchNotifier, VillageSearchState>((ref) {
  return VillageSearchNotifier();
});

/// حالة البحث في القرى
class VillageSearchState {
  final String query;
  final bool isSearching;
  final List<VillageEntity> results;
  final String? error;

  const VillageSearchState({
    required this.query,
    required this.isSearching,
    required this.results,
    this.error,
  });

  factory VillageSearchState.initial() {
    return const VillageSearchState(
      query: '',
      isSearching: false,
      results: [],
      error: null,
    );
  }

  VillageSearchState copyWith({
    String? query,
    bool? isSearching,
    List<VillageEntity>? results,
    String? error,
  }) {
    return VillageSearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      results: results ?? this.results,
      error: error,
    );
  }
}

/// مدير حالة البحث في القرى
class VillageSearchNotifier extends StateNotifier<VillageSearchState> {
  VillageSearchNotifier() : super(VillageSearchState.initial());

  /// تحديث نص البحث
  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  /// تحديث نتائج البحث
  void updateResults(List<VillageEntity> results) {
    state = state.copyWith(results: results, isSearching: false);
  }

  /// تحديث حالة البحث
  void setSearching(bool isSearching) {
    state = state.copyWith(isSearching: isSearching);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error, isSearching: false);
  }

  /// مسح البحث
  void clearSearch() {
    state = VillageSearchState.initial();
  }
}

/// مزود حالة التصفية للقرى
final villageFilterProvider = StateNotifierProvider<VillageFilterNotifier, VillageFilterState>((ref) {
  return VillageFilterNotifier();
});

/// حالة التصفية للقرى
class VillageFilterState {
  final bool? isActiveFilter;
  final bool showOnlyWithSubscribers;
  final bool showOnlyWithMeters;

  const VillageFilterState({
    this.isActiveFilter,
    required this.showOnlyWithSubscribers,
    required this.showOnlyWithMeters,
  });

  factory VillageFilterState.initial() {
    return const VillageFilterState(
      isActiveFilter: null,
      showOnlyWithSubscribers: false,
      showOnlyWithMeters: false,
    );
  }

  VillageFilterState copyWith({
    bool? isActiveFilter,
    bool? showOnlyWithSubscribers,
    bool? showOnlyWithMeters,
  }) {
    return VillageFilterState(
      isActiveFilter: isActiveFilter ?? this.isActiveFilter,
      showOnlyWithSubscribers: showOnlyWithSubscribers ?? this.showOnlyWithSubscribers,
      showOnlyWithMeters: showOnlyWithMeters ?? this.showOnlyWithMeters,
    );
  }

  /// التحقق من وجود مرشحات نشطة
  bool get hasActiveFilters {
    return isActiveFilter != null ||
        showOnlyWithSubscribers ||
        showOnlyWithMeters;
  }
}

/// مدير حالة التصفية للقرى
class VillageFilterNotifier extends StateNotifier<VillageFilterState> {
  VillageFilterNotifier() : super(VillageFilterState.initial());

  /// تحديث مرشح الحالة النشطة
  void updateActiveFilter(bool? isActive) {
    state = state.copyWith(isActiveFilter: isActive);
  }

  /// تحديث مرشح القرى التي بها مشتركين
  void updateSubscribersFilter(bool showOnly) {
    state = state.copyWith(showOnlyWithSubscribers: showOnly);
  }

  /// تحديث مرشح القرى التي بها عدادات
  void updateMetersFilter(bool showOnly) {
    state = state.copyWith(showOnlyWithMeters: showOnly);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    state = VillageFilterState.initial();
  }
}
