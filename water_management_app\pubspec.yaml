name: water_management_app
description: "نظام إدارة مشروع المياه التعاوني - النسخة المحلية"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Database (Local)
  sqflite: ^2.3.0
  drift: ^2.14.0
  sqlite3_flutter_libs: ^0.5.0

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1

  # UI & Navigation
  go_router: ^12.1.3
  flutter_screenutil: ^5.9.0
  google_fonts: ^6.1.0

  # Forms & Validation
  flutter_form_builder: ^10.0.1
  form_builder_validators: ^11.1.2

  # File Handling
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  path: ^1.8.3

  # PDF & Documents
  pdf: ^3.10.4
  printing: ^5.11.0

  # QR & NFC
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  nfc_manager: ^3.3.0

  # Security & Encryption
  crypto: ^3.0.3
  encrypt: ^5.0.1

  # Charts
  fl_chart: ^0.65.0

  # Utils
  uuid: ^4.2.1
  logger: ^2.0.2+1

  # Backup & Archive
  archive: ^3.4.9

  # Icons
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.7
  drift_dev: ^2.14.0
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/config/
    - assets/database/
