import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/bill_repository.dart';
import '../../domain/entities/bill_entity.dart';

/// مزود مستودع الفواتير
final billRepositoryProvider = Provider<BillRepository>((ref) {
  return BillRepository();
});

/// مزود قائمة الفواتير
final billsProvider = FutureProvider<List<BillEntity>>((ref) async {
  final repository = ref.read(billRepositoryProvider);
  return await repository.getAllBills();
});

/// مزود البحث في الفواتير
final billSearchProvider = FutureProvider.family<List<BillEntity>, String>((ref, query) async {
  final repository = ref.read(billRepositoryProvider);
  return await repository.searchBills(query);
});

/// مزود الفاتورة الواحدة
final billProvider = FutureProvider.family<BillEntity?, int>((ref, id) async {
  final repository = ref.read(billRepositoryProvider);
  return await repository.getBillById(id);
});

/// مزود الفواتير حسب المشترك
final billsBySubscriberProvider = FutureProvider.family<List<BillEntity>, int>((ref, subscriberId) async {
  final repository = ref.read(billRepositoryProvider);
  return await repository.getBillsBySubscriber(subscriberId);
});

/// مزود الفواتير المتأخرة
final overdueBillsProvider = FutureProvider<List<BillEntity>>((ref) async {
  final repository = ref.read(billRepositoryProvider);
  return await repository.getOverdueBills();
});

/// مزود إحصائيات الفواتير
final billStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(billRepositoryProvider);
  return await repository.getBillsStatistics();
});

/// مزود حالة نموذج الفاتورة
final billFormProvider = StateNotifierProvider<BillFormNotifier, BillFormState>((ref) {
  final repository = ref.read(billRepositoryProvider);
  return BillFormNotifier(repository);
});

/// حالة نموذج الفاتورة
class BillFormState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final BillEntity? bill;

  const BillFormState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
    this.bill,
  });

  factory BillFormState.initial() {
    return const BillFormState(
      isLoading: false,
      isSuccess: false,
      error: null,
      bill: null,
    );
  }

  BillFormState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    BillEntity? bill,
  }) {
    return BillFormState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      bill: bill ?? this.bill,
    );
  }
}

/// مدير حالة نموذج الفاتورة
class BillFormNotifier extends StateNotifier<BillFormState> {
  final BillRepository _repository;

  BillFormNotifier(this._repository) : super(BillFormState.initial());

  /// حفظ فاتورة (إضافة أو تحديث)
  Future<void> saveBill(BillEntity bill) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      BillEntity savedBill;
      
      if (bill.id == 0) {
        // إضافة فاتورة جديدة
        savedBill = await _repository.createBill(bill);
      } else {
        // تحديث فاتورة موجودة
        savedBill = await _repository.updateBill(bill);
      }
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        bill: savedBill,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// حذف فاتورة
  Future<void> deleteBill(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteBill(id);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// تحديث حالة الفاتورة
  Future<void> updateBillStatus(int billId, BillStatus newStatus) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final updatedBill = await _repository.updateBillStatus(billId, newStatus);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        bill: updatedBill,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// إنشاء رقم فاتورة تلقائي
  Future<String> generateBillNumber() async {
    try {
      return await _repository.generateBillNumber();
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم الفاتورة: $e');
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = BillFormState.initial();
  }

  /// تحديث الفاتورة في الحالة
  void updateBill(BillEntity bill) {
    state = state.copyWith(bill: bill);
  }

  /// تحديث حالة التحميل
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// تحديث حالة النجاح
  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }
}

/// مزود حالة البحث في الفواتير
final billSearchStateProvider = StateNotifierProvider<BillSearchNotifier, BillSearchState>((ref) {
  return BillSearchNotifier();
});

/// حالة البحث في الفواتير
class BillSearchState {
  final String query;
  final bool isSearching;
  final List<BillEntity> results;
  final String? error;

  const BillSearchState({
    required this.query,
    required this.isSearching,
    required this.results,
    this.error,
  });

  factory BillSearchState.initial() {
    return const BillSearchState(
      query: '',
      isSearching: false,
      results: [],
      error: null,
    );
  }

  BillSearchState copyWith({
    String? query,
    bool? isSearching,
    List<BillEntity>? results,
    String? error,
  }) {
    return BillSearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      results: results ?? this.results,
      error: error,
    );
  }
}

/// مدير حالة البحث في الفواتير
class BillSearchNotifier extends StateNotifier<BillSearchState> {
  BillSearchNotifier() : super(BillSearchState.initial());

  /// تحديث نص البحث
  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  /// تحديث نتائج البحث
  void updateResults(List<BillEntity> results) {
    state = state.copyWith(results: results, isSearching: false);
  }

  /// تحديث حالة البحث
  void setSearching(bool isSearching) {
    state = state.copyWith(isSearching: isSearching);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error, isSearching: false);
  }

  /// مسح البحث
  void clearSearch() {
    state = BillSearchState.initial();
  }
}

/// مزود حالة التصفية للفواتير
final billFilterProvider = StateNotifierProvider<BillFilterNotifier, BillFilterState>((ref) {
  return BillFilterNotifier();
});

/// حالة التصفية للفواتير
class BillFilterState {
  final BillStatus? statusFilter;
  final BillType? typeFilter;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool showOverdueOnly;
  final bool showUnpaidOnly;

  const BillFilterState({
    this.statusFilter,
    this.typeFilter,
    this.startDate,
    this.endDate,
    required this.showOverdueOnly,
    required this.showUnpaidOnly,
  });

  factory BillFilterState.initial() {
    return const BillFilterState(
      statusFilter: null,
      typeFilter: null,
      startDate: null,
      endDate: null,
      showOverdueOnly: false,
      showUnpaidOnly: false,
    );
  }

  BillFilterState copyWith({
    BillStatus? statusFilter,
    BillType? typeFilter,
    DateTime? startDate,
    DateTime? endDate,
    bool? showOverdueOnly,
    bool? showUnpaidOnly,
  }) {
    return BillFilterState(
      statusFilter: statusFilter ?? this.statusFilter,
      typeFilter: typeFilter ?? this.typeFilter,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      showOverdueOnly: showOverdueOnly ?? this.showOverdueOnly,
      showUnpaidOnly: showUnpaidOnly ?? this.showUnpaidOnly,
    );
  }

  /// التحقق من وجود مرشحات نشطة
  bool get hasActiveFilters {
    return statusFilter != null ||
        typeFilter != null ||
        startDate != null ||
        endDate != null ||
        showOverdueOnly ||
        showUnpaidOnly;
  }
}

/// مدير حالة التصفية للفواتير
class BillFilterNotifier extends StateNotifier<BillFilterState> {
  BillFilterNotifier() : super(BillFilterState.initial());

  /// تحديث مرشح الحالة
  void updateStatusFilter(BillStatus? status) {
    state = state.copyWith(statusFilter: status);
  }

  /// تحديث مرشح النوع
  void updateTypeFilter(BillType? type) {
    state = state.copyWith(typeFilter: type);
  }

  /// تحديث تاريخ البداية
  void updateStartDate(DateTime? date) {
    state = state.copyWith(startDate: date);
  }

  /// تحديث تاريخ النهاية
  void updateEndDate(DateTime? date) {
    state = state.copyWith(endDate: date);
  }

  /// تحديث مرشح الفواتير المتأخرة
  void updateOverdueFilter(bool showOverdueOnly) {
    state = state.copyWith(showOverdueOnly: showOverdueOnly);
  }

  /// تحديث مرشح الفواتير غير المدفوعة
  void updateUnpaidFilter(bool showUnpaidOnly) {
    state = state.copyWith(showUnpaidOnly: showUnpaidOnly);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    state = BillFilterState.initial();
  }
}
