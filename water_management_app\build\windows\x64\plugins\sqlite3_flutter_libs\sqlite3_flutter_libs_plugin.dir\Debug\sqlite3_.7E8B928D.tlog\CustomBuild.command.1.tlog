^J:\PROJECTS3\WATER\WATER_MANAGEMENT_APP\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\SQLITE3_FLUTTER_LIBS\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/projects3/water/water_management_app/windows -BJ:/projects3/water/water_management_app/build/windows/x64 --check-stamp-file J:/projects3/water/water_management_app/build/windows/x64/plugins/sqlite3_flutter_libs/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
