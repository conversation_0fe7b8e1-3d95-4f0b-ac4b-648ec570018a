import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../providers/report_provider.dart';
import '../../domain/entities/report_entity.dart';

class ReportGeneratorDialog extends ConsumerStatefulWidget {
  const ReportGeneratorDialog({super.key});

  @override
  ConsumerState<ReportGeneratorDialog> createState() => _ReportGeneratorDialogState();
}

class _ReportGeneratorDialogState extends ConsumerState<ReportGeneratorDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  ReportType _selectedType = ReportType.statistics;
  ReportPeriod _selectedPeriod = ReportPeriod.monthly;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      title: 'إنشاء تقرير مخصص',
      scrollable: true,
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildBasicInfo(),
            const SizedBox(height: 16),
            _buildReportSettings(),
            const SizedBox(height: 16),
            _buildDateRange(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _generateReport,
          child: const Text('إنشاء التقرير'),
        ),
      ],
    );
  }

  /// بناء المعلومات الأساسية
  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'عنوان التقرير *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.title),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'عنوان التقرير مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'وصف التقرير',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  /// بناء إعدادات التقرير
  Widget _buildReportSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات التقرير',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<ReportType>(
          value: _selectedType,
          decoration: const InputDecoration(
            labelText: 'نوع التقرير',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.category),
          ),
          items: ReportType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Row(
                children: [
                  Icon(
                    _getTypeIcon(type),
                    size: 20,
                    color: _getTypeColor(type),
                  ),
                  const SizedBox(width: 8),
                  Text(_getTypeText(type)),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
              _updateTitleAndDescription();
            });
          },
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<ReportPeriod>(
          value: _selectedPeriod,
          decoration: const InputDecoration(
            labelText: 'فترة التقرير',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.schedule),
          ),
          items: ReportPeriod.values.map((period) {
            return DropdownMenuItem(
              value: period,
              child: Text(_getPeriodText(period)),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPeriod = value!;
              _updateDateRange();
            });
          },
        ),
      ],
    );
  }

  /// بناء نطاق التاريخ
  Widget _buildDateRange() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق التاريخ',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDatePicker(
                'تاريخ البداية',
                _startDate,
                (date) => setState(() => _startDate = date),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDatePicker(
                'تاريخ النهاية',
                _endDate,
                (date) => setState(() => _endDate = date),
              ),
            ),
          ],
        ),
        if (!_startDate.isBefore(_endDate)) ...[
          const SizedBox(height: 8),
          Text(
            'تاريخ البداية يجب أن يكون قبل تاريخ النهاية',
            style: TextStyle(
              color: Colors.red,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  /// بناء منتقي التاريخ
  Widget _buildDatePicker(
    String label,
    DateTime selectedDate,
    Function(DateTime) onDateSelected,
  ) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: selectedDate,
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 30)),
        );
        if (date != null) {
          onDateSelected(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
        ),
      ),
    );
  }

  /// تحديث العنوان والوصف تلقائياً
  void _updateTitleAndDescription() {
    _titleController.text = _getTypeText(_selectedType);
    _descriptionController.text = _getTypeDescription(_selectedType);
  }

  /// تحديث نطاق التاريخ حسب الفترة
  void _updateDateRange() {
    final now = DateTime.now();
    
    switch (_selectedPeriod) {
      case ReportPeriod.daily:
        _startDate = DateTime(now.year, now.month, now.day);
        _endDate = now;
        break;
      case ReportPeriod.weekly:
        _startDate = now.subtract(Duration(days: now.weekday - 1));
        _endDate = now;
        break;
      case ReportPeriod.monthly:
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = now;
        break;
      case ReportPeriod.quarterly:
        final quarter = ((now.month - 1) ~/ 3) + 1;
        _startDate = DateTime(now.year, (quarter - 1) * 3 + 1, 1);
        _endDate = now;
        break;
      case ReportPeriod.yearly:
        _startDate = DateTime(now.year, 1, 1);
        _endDate = now;
        break;
      case ReportPeriod.custom:
        // لا تغيير في التواريخ
        break;
    }
  }

  /// إنشاء التقرير
  void _generateReport() async {
    if (_formKey.currentState!.validate() && _startDate.isBefore(_endDate)) {
      Navigator.of(context).pop(); // إغلاق الحوار
      
      // عرض حوار التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text('جاري إنشاء ${_titleController.text}...'),
            ],
          ),
        ),
      );

      try {
        // إنشاء التقرير حسب النوع
        switch (_selectedType) {
          case ReportType.statistics:
            await ref.read(reportGeneratorProvider.notifier).generateStatisticsReport();
            break;
          case ReportType.financial:
            await ref.read(reportGeneratorProvider.notifier).generateFinancialReport(
              startDate: _startDate,
              endDate: _endDate,
            );
            break;
          case ReportType.overdue:
            await ref.read(reportGeneratorProvider.notifier).generateOverdueReport();
            break;
          case ReportType.consumption:
            await ref.read(reportGeneratorProvider.notifier).generateConsumptionReport(
              startDate: _startDate,
              endDate: _endDate,
            );
            break;
          default:
            throw Exception('نوع التقرير غير مدعوم حالياً');
        }

        final state = ref.read(reportGeneratorProvider);
        if (!mounted) return;
        
        Navigator.of(context).pop(); // إغلاق حوار التحميل
        
        if (state.isSuccess && state.report != null) {
          _showReportDialog(state.report!);
        } else if (state.error != null) {
          _showErrorMessage(state.error!);
        }
      } catch (e) {
        if (!mounted) return;
        Navigator.of(context).pop();
        _showErrorMessage('خطأ في إنشاء التقرير: $e');
      }
    }
  }

  /// عرض حوار التقرير
  void _showReportDialog(ReportEntity report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(report.title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(report.description),
              const SizedBox(height: 16),
              Text(
                'تم إنشاؤه في: ${report.generatedAt.toString().substring(0, 19)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 8),
              Text(
                'عدد البيانات: ${report.dataSize} عنصر',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: تنفيذ تصدير التقرير
              _showSuccessMessage('سيتم تنفيذ تصدير التقرير قريباً');
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// الحصول على نص النوع
  String _getTypeText(ReportType type) {
    switch (type) {
      case ReportType.subscribers:
        return 'تقرير المشتركين';
      case ReportType.meters:
        return 'تقرير العدادات';
      case ReportType.readings:
        return 'تقرير القراءات';
      case ReportType.bills:
        return 'تقرير الفواتير';
      case ReportType.payments:
        return 'تقرير المدفوعات';
      case ReportType.villages:
        return 'تقرير القرى';
      case ReportType.financial:
        return 'التقرير المالي';
      case ReportType.consumption:
        return 'تقرير الاستهلاك';
      case ReportType.overdue:
        return 'تقرير المتأخرات';
      case ReportType.statistics:
        return 'تقرير الإحصائيات';
    }
  }

  /// الحصول على وصف النوع
  String _getTypeDescription(ReportType type) {
    switch (type) {
      case ReportType.subscribers:
        return 'تقرير تفصيلي عن المشتركين وحالاتهم';
      case ReportType.meters:
        return 'تقرير شامل عن العدادات وأنواعها';
      case ReportType.readings:
        return 'تقرير القراءات والاستهلاك';
      case ReportType.bills:
        return 'تقرير الفواتير الصادرة والمدفوعة';
      case ReportType.payments:
        return 'تقرير المدفوعات والتحصيلات';
      case ReportType.villages:
        return 'تقرير القرى والمناطق';
      case ReportType.financial:
        return 'تقرير مالي شامل للإيرادات والمصروفات';
      case ReportType.consumption:
        return 'تقرير تفصيلي لاستهلاك المياه';
      case ReportType.overdue:
        return 'تقرير الفواتير والمدفوعات المتأخرة';
      case ReportType.statistics:
        return 'إحصائيات عامة شاملة للنظام';
    }
  }

  /// الحصول على أيقونة النوع
  IconData _getTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.subscribers:
        return Icons.people;
      case ReportType.meters:
        return Icons.speed;
      case ReportType.readings:
        return Icons.analytics;
      case ReportType.bills:
        return Icons.receipt_long;
      case ReportType.payments:
        return Icons.payment;
      case ReportType.villages:
        return Icons.location_city;
      case ReportType.financial:
        return Icons.account_balance;
      case ReportType.consumption:
        return Icons.water_drop;
      case ReportType.overdue:
        return Icons.warning;
      case ReportType.statistics:
        return Icons.bar_chart;
    }
  }

  /// الحصول على لون النوع
  Color _getTypeColor(ReportType type) {
    switch (type) {
      case ReportType.subscribers:
        return Colors.blue;
      case ReportType.meters:
        return Colors.green;
      case ReportType.readings:
        return Colors.purple;
      case ReportType.bills:
        return Colors.orange;
      case ReportType.payments:
        return Colors.teal;
      case ReportType.villages:
        return Colors.brown;
      case ReportType.financial:
        return Colors.indigo;
      case ReportType.consumption:
        return Colors.cyan;
      case ReportType.overdue:
        return Colors.red;
      case ReportType.statistics:
        return Colors.pink;
    }
  }

  /// الحصول على نص الفترة
  String _getPeriodText(ReportPeriod period) {
    switch (period) {
      case ReportPeriod.daily:
        return 'يومي';
      case ReportPeriod.weekly:
        return 'أسبوعي';
      case ReportPeriod.monthly:
        return 'شهري';
      case ReportPeriod.quarterly:
        return 'ربع سنوي';
      case ReportPeriod.yearly:
        return 'سنوي';
      case ReportPeriod.custom:
        return 'فترة مخصصة';
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
