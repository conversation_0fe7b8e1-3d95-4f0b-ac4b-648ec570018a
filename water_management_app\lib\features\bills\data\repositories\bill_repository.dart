import 'package:drift/drift.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/services/database_service.dart';
import '../../domain/entities/bill_entity.dart';

/// مستودع الفواتير
class BillRepository {
  final AppDatabase _database = DatabaseService.instance.database;

  /// الحصول على جميع الفواتير
  Future<List<BillEntity>> getAllBills() async {
    try {
      final query = _database.select(_database.bills).join([
        leftOuterJoin(
          _database.readings,
          _database.readings.id.equalsExp(_database.bills.readingId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.bills.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
      ])..orderBy([OrderingTerm.desc(_database.bills.issueDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final bill = row.readTable(_database.bills);
        final reading = row.readTableOrNull(_database.readings);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        final meter = row.readTableOrNull(_database.meters);
        
        return _mapToEntity(
          bill,
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد',
          meter?.serialNumber ?? 'غير محدد',
          reading?.previousReading ?? 0.0,
          reading?.currentReading ?? 0.0,
          reading?.consumption ?? 0.0,
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على الفواتير: $e');
    }
  }

  /// الحصول على فاتورة بالمعرف
  Future<BillEntity?> getBillById(int id) async {
    try {
      final query = _database.select(_database.bills).join([
        leftOuterJoin(
          _database.readings,
          _database.readings.id.equalsExp(_database.bills.readingId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.bills.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
      ])..where(_database.bills.id.equals(id));

      final result = await query.getSingleOrNull();
      
      if (result == null) return null;
      
      final bill = result.readTable(_database.bills);
      final reading = result.readTableOrNull(_database.readings);
      final subscriber = result.readTableOrNull(_database.subscribers);
      final village = result.readTableOrNull(_database.villages);
      final meter = result.readTableOrNull(_database.meters);
      
      return _mapToEntity(
        bill,
        subscriber?.fullName ?? 'غير محدد',
        village?.name ?? 'غير محدد',
        meter?.serialNumber ?? 'غير محدد',
        reading?.previousReading ?? 0.0,
        reading?.currentReading ?? 0.0,
        reading?.consumption ?? 0.0,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على الفاتورة: $e');
    }
  }

  /// البحث في الفواتير
  Future<List<BillEntity>> searchBills(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllBills();
      }

      final searchQuery = _database.select(_database.bills).join([
        leftOuterJoin(
          _database.readings,
          _database.readings.id.equalsExp(_database.bills.readingId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.bills.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
      ])..where(
        _database.bills.billNumber.like('%$query%') |
        _database.subscribers.fullName.like('%$query%') |
        _database.villages.name.like('%$query%') |
        _database.meters.serialNumber.like('%$query%')
      );

      final results = await searchQuery.get();
      
      return results.map((row) {
        final bill = row.readTable(_database.bills);
        final reading = row.readTableOrNull(_database.readings);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        final meter = row.readTableOrNull(_database.meters);
        
        return _mapToEntity(
          bill,
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد',
          meter?.serialNumber ?? 'غير محدد',
          reading?.previousReading ?? 0.0,
          reading?.currentReading ?? 0.0,
          reading?.consumption ?? 0.0,
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في البحث عن الفواتير: $e');
    }
  }

  /// الحصول على الفواتير حسب المشترك
  Future<List<BillEntity>> getBillsBySubscriber(int subscriberId) async {
    try {
      final query = _database.select(_database.bills).join([
        leftOuterJoin(
          _database.readings,
          _database.readings.id.equalsExp(_database.bills.readingId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.bills.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
      ])..where(_database.bills.subscriberId.equals(subscriberId))
      ..orderBy([OrderingTerm.desc(_database.bills.issueDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final bill = row.readTable(_database.bills);
        final reading = row.readTableOrNull(_database.readings);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        final meter = row.readTableOrNull(_database.meters);
        
        return _mapToEntity(
          bill,
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد',
          meter?.serialNumber ?? 'غير محدد',
          reading?.previousReading ?? 0.0,
          reading?.currentReading ?? 0.0,
          reading?.consumption ?? 0.0,
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على فواتير المشترك: $e');
    }
  }

  /// الحصول على الفواتير المتأخرة
  Future<List<BillEntity>> getOverdueBills() async {
    try {
      final query = _database.select(_database.bills).join([
        leftOuterJoin(
          _database.readings,
          _database.readings.id.equalsExp(_database.bills.readingId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.bills.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
      ])..where(
        _database.bills.dueDate.isSmallerThanValue(DateTime.now()) &
        _database.bills.status.isNotIn(['paid', 'cancelled'])
      )..orderBy([OrderingTerm.desc(_database.bills.dueDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final bill = row.readTable(_database.bills);
        final reading = row.readTableOrNull(_database.readings);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        final meter = row.readTableOrNull(_database.meters);
        
        return _mapToEntity(
          bill,
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد',
          meter?.serialNumber ?? 'غير محدد',
          reading?.previousReading ?? 0.0,
          reading?.currentReading ?? 0.0,
          reading?.consumption ?? 0.0,
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على الفواتير المتأخرة: $e');
    }
  }

  /// إضافة فاتورة جديدة
  Future<BillEntity> createBill(BillEntity bill) async {
    try {
      // إنشاء رقم فاتورة تلقائي إذا لم يكن موجوداً
      String billNumber = bill.billNumber;
      if (billNumber.isEmpty) {
        billNumber = await generateBillNumber();
      }

      final id = await _database.into(_database.bills).insert(
        BillsCompanion.insert(
          billNumber: billNumber,
          subscriberId: bill.subscriberId,
          meterId: 0, // سيتم تحديثه من القراءة
          readingId: Value(bill.readingId),
          billingPeriod: '${bill.billingPeriodStart.year}-${bill.billingPeriodStart.month.toString().padLeft(2, '0')}',
          issueDate: bill.issueDate,
          dueDate: bill.dueDate,
          previousReading: bill.previousReading,
          currentReading: bill.currentReading,
          consumption: bill.consumption,
          waterCharges: bill.waterAmount,
          serviceCharges: Value(bill.sewerageAmount + bill.maintenanceAmount),
          taxAmount: Value(bill.taxAmount),
          discountAmount: Value(bill.discountAmount),
          totalAmount: bill.totalAmount,
          status: Value(bill.status.name),
        ),
      );

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'bills',
        recordId: id,
        operation: 'INSERT',
        newValues: bill.toMap(),
      );

      return bill.copyWith(id: id, billNumber: billNumber);
    } catch (e) {
      throw Exception('خطأ في إضافة الفاتورة: $e');
    }
  }

  /// تحديث فاتورة
  Future<BillEntity> updateBill(BillEntity bill) async {
    try {
      // الحصول على البيانات القديمة
      final oldBill = await getBillById(bill.id);
      
      await (_database.update(_database.bills)
        ..where((b) => b.id.equals(bill.id)))
        .write(BillsCompanion(
          billNumber: Value(bill.billNumber),
          readingId: Value(bill.readingId),
          subscriberId: Value(bill.subscriberId),
          billingPeriod: Value('${bill.billingPeriodStart.year}-${bill.billingPeriodStart.month.toString().padLeft(2, '0')}'),
          issueDate: Value(bill.issueDate),
          dueDate: Value(bill.dueDate),
          previousReading: Value(bill.previousReading),
          currentReading: Value(bill.currentReading),
          consumption: Value(bill.consumption),
          waterCharges: Value(bill.waterAmount),
          serviceCharges: Value(bill.sewerageAmount + bill.maintenanceAmount),
          taxAmount: Value(bill.taxAmount),
          discountAmount: Value(bill.discountAmount),
          totalAmount: Value(bill.totalAmount),
          status: Value(bill.status.name),
        ));

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'bills',
        recordId: bill.id,
        operation: 'UPDATE',
        oldValues: oldBill?.toMap(),
        newValues: bill.toMap(),
      );

      return bill.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      throw Exception('خطأ في تحديث الفاتورة: $e');
    }
  }

  /// حذف فاتورة
  Future<void> deleteBill(int id) async {
    try {
      // التحقق من وجود مدفوعات مرتبطة
      final paymentsCount = await (_database.select(_database.payments)
        ..where((p) => p.billId.equals(id)))
        .get()
        .then((payments) => payments.length);

      if (paymentsCount > 0) {
        throw Exception('لا يمكن حذف الفاتورة لوجود مدفوعات مرتبطة بها');
      }

      // الحصول على البيانات قبل الحذف
      final bill = await getBillById(id);

      await (_database.delete(_database.bills)
        ..where((b) => b.id.equals(id)))
        .go();

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'bills',
        recordId: id,
        operation: 'DELETE',
        oldValues: bill?.toMap(),
      );
    } catch (e) {
      throw Exception('خطأ في حذف الفاتورة: $e');
    }
  }

  /// تحديث حالة الفاتورة
  Future<BillEntity> updateBillStatus(int billId, BillStatus newStatus) async {
    try {
      final bill = await getBillById(billId);
      if (bill == null) {
        throw Exception('الفاتورة غير موجودة');
      }

      final updatedBill = bill.updateStatus(newStatus);
      return await updateBill(updatedBill);
    } catch (e) {
      throw Exception('خطأ في تحديث حالة الفاتورة: $e');
    }
  }

  /// الحصول على إحصائيات الفواتير
  Future<Map<String, dynamic>> getBillsStatistics() async {
    try {
      final stats = <String, dynamic>{};
      
      // إجمالي الفواتير
      stats['total'] = await _database.select(_database.bills).get()
        .then((list) => list.length);
      
      // الفواتير المدفوعة
      stats['paid'] = await (_database.select(_database.bills)
        ..where((b) => b.status.equals('paid'))).get()
        .then((list) => list.length);
      
      // الفواتير المتأخرة
      stats['overdue'] = await (_database.select(_database.bills)
        ..where((b) => b.dueDate.isSmallerThanValue(DateTime.now()) & 
                      b.status.isNotIn(['paid', 'cancelled']))).get()
        .then((list) => list.length);
      
      // الفواتير الصادرة
      stats['issued'] = await (_database.select(_database.bills)
        ..where((b) => b.status.equals('issued'))).get()
        .then((list) => list.length);
      
      // إجمالي المبالغ
      final totalAmountResult = await _database.customSelect(
        'SELECT SUM(total_amount) as total FROM bills WHERE status != "cancelled"'
      ).getSingleOrNull();
      stats['total_amount'] = totalAmountResult?.data['total'] ?? 0.0;

      // المبالغ المدفوعة
      final paidAmountResult = await _database.customSelect(
        'SELECT SUM(total_amount) as paid FROM bills WHERE status = "paid"'
      ).getSingleOrNull();
      stats['paid_amount'] = paidAmountResult?.data['paid'] ?? 0.0;

      // المبالغ المتأخرة
      final overdueAmountResult = await _database.customSelect(
        'SELECT SUM(total_amount) as overdue FROM bills WHERE due_date < ? AND status NOT IN ("paid", "cancelled")',
        variables: [Variable.withDateTime(DateTime.now())]
      ).getSingleOrNull();
      stats['overdue_amount'] = overdueAmountResult?.data['overdue'] ?? 0.0;

      return stats;
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات الفواتير: $e');
    }
  }

  /// تحويل من نموذج قاعدة البيانات إلى كيان
  BillEntity _mapToEntity(
    Bill bill,
    String subscriberName,
    String villageName,
    String meterNumber,
    double previousReading,
    double currentReading,
    double consumption,
  ) {
    return BillEntity(
      id: bill.id,
      billNumber: bill.billNumber,
      readingId: bill.readingId,
      subscriberId: bill.subscriberId,
      subscriberName: subscriberName,
      villageName: villageName,
      meterNumber: meterNumber,
      previousReading: previousReading,
      currentReading: currentReading,
      consumption: consumption,
      billingPeriodStart: DateTime.parse('${bill.billingPeriod}-01'),
      billingPeriodEnd: DateTime.parse('${bill.billingPeriod}-01').add(const Duration(days: 30)),
      issueDate: bill.issueDate,
      dueDate: bill.dueDate,
      type: BillType.regular, // افتراضي
      status: BillStatus.values.firstWhere(
        (e) => e.name == bill.status,
        orElse: () => BillStatus.draft,
      ),
      unitPrice: consumption > 0 ? bill.waterCharges / consumption : 0.0,
      waterAmount: bill.waterCharges,
      sewerageAmount: bill.serviceCharges,
      maintenanceAmount: 0.0, // مدمج في serviceCharges
      taxAmount: bill.taxAmount,
      discountAmount: bill.discountAmount,
      totalAmount: bill.totalAmount,
      paidAmount: bill.paidAmount,
      remainingAmount: bill.totalAmount - bill.paidAmount,
      notes: bill.notes,
      createdAt: bill.createdAt,
      updatedAt: bill.createdAt, // لا يوجد updatedAt في الجدول
    );
  }

  /// إنشاء رقم فاتورة تلقائي
  Future<String> generateBillNumber() async {
    try {
      final lastBill = await (_database.select(_database.bills)
        ..orderBy([(b) => OrderingTerm.desc(b.id)]))
        .getSingleOrNull();

      if (lastBill == null) {
        return 'BILL001';
      }

      // استخراج الرقم من آخر فاتورة وزيادته
      final lastNumber = lastBill.billNumber;
      final numberPart = lastNumber.replaceAll(RegExp(r'[^0-9]'), '');
      final nextNumber = (int.tryParse(numberPart) ?? 0) + 1;
      
      return 'BILL${nextNumber.toString().padLeft(3, '0')}';
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم الفاتورة: $e');
    }
  }
}
