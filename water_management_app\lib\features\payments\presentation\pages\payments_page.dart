import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../providers/payment_provider.dart';
import '../widgets/payment_card.dart';
import '../widgets/add_payment_dialog.dart';
import '../../domain/entities/payment_entity.dart';

class PaymentsPage extends ConsumerStatefulWidget {
  const PaymentsPage({super.key});

  @override
  ConsumerState<PaymentsPage> createState() => _PaymentsPageState();
}

class _PaymentsPageState extends ConsumerState<PaymentsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildSearchAndFilters(),
          Expanded(child: _buildPaymentsList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return AdaptiveCard(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            Icons.payment,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة المدفوعات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'تسجيل ومتابعة المدفوعات والتحصيلات',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildStatisticsChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة الإحصائيات
  Widget _buildStatisticsChip() {
    final statisticsAsync = ref.watch(paymentStatisticsProvider);
    
    return statisticsAsync.when(
      data: (stats) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          '${stats['total'] ?? 0} دفعة',
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      loading: () => const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
      error: (_, __) => const Icon(Icons.error, color: Colors.red),
    );
  }

  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في المدفوعات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          
          // مرشحات سريعة
          _buildQuickFilters(),
        ],
      ),
    );
  }

  /// بناء المرشحات السريعة
  Widget _buildQuickFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildStatusFilterChip('الكل', null),
          const SizedBox(width: 8),
          _buildStatusFilterChip('مكتمل', PaymentStatus.completed),
          const SizedBox(width: 8),
          _buildStatusFilterChip('معلق', PaymentStatus.pending),
          const SizedBox(width: 8),
          _buildStatusFilterChip('فاشل', PaymentStatus.failed),
          const SizedBox(width: 8),
          _buildMethodFilterChip('نقدي', PaymentMethod.cash),
          const SizedBox(width: 8),
          _buildMethodFilterChip('تحويل', PaymentMethod.bankTransfer),
        ],
      ),
    );
  }

  /// بناء رقاقة مرشح الحالة
  Widget _buildStatusFilterChip(String label, PaymentStatus? status) {
    final filterState = ref.watch(paymentFilterProvider);
    final isSelected = filterState.statusFilter == status;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(paymentFilterProvider.notifier)
            .updateStatusFilter(selected ? status : null);
      },
    );
  }

  /// بناء رقاقة مرشح طريقة الدفع
  Widget _buildMethodFilterChip(String label, PaymentMethod method) {
    final filterState = ref.watch(paymentFilterProvider);
    final isSelected = filterState.methodFilter == method;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(paymentFilterProvider.notifier)
            .updateMethodFilter(selected ? method : null);
      },
    );
  }

  /// بناء قائمة المدفوعات
  Widget _buildPaymentsList() {
    final paymentsAsync = _searchQuery.isEmpty
        ? ref.watch(paymentsProvider)
        : ref.watch(paymentSearchProvider(_searchQuery));

    return paymentsAsync.when(
      data: (payments) => _buildPaymentsListView(payments),
      loading: () => const AdaptiveLoadingIndicator(
        message: 'جاري تحميل المدفوعات...',
      ),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  /// بناء عرض قائمة المدفوعات
  Widget _buildPaymentsListView(List<PaymentEntity> payments) {
    final filterState = ref.watch(paymentFilterProvider);
    
    // تطبيق المرشحات
    final filteredPayments = _applyFilters(payments, filterState);

    if (filteredPayments.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(paymentsProvider);
      },
      child: AdaptiveLayoutBuilder(
        builder: (context, layoutType) {
          if (layoutType == LayoutType.mobile) {
            return _buildMobileList(filteredPayments);
          } else {
            return _buildDesktopGrid(filteredPayments);
          }
        },
      ),
    );
  }

  /// بناء قائمة للهاتف المحمول
  Widget _buildMobileList(List<PaymentEntity> payments) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        return PaymentCard(
          payment: payments[index],
          onTap: () => _navigateToPaymentDetails(payments[index]),
          onEdit: () => _editPayment(payments[index]),
          onDelete: () => _deletePayment(payments[index]),
          onUpdateStatus: (status) => _updateStatus(payments[index], status),
        );
      },
    );
  }

  /// بناء شبكة لسطح المكتب
  Widget _buildDesktopGrid(List<PaymentEntity> payments) {
    final columns = AdaptiveLayout.getGridColumns(context);
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        return PaymentCard(
          payment: payments[index],
          onTap: () => _navigateToPaymentDetails(payments[index]),
          onEdit: () => _editPayment(payments[index]),
          onDelete: () => _deletePayment(payments[index]),
          onUpdateStatus: (status) => _updateStatus(payments[index], status),
        );
      },
    );
  }

  /// تطبيق المرشحات
  List<PaymentEntity> _applyFilters(
    List<PaymentEntity> payments,
    PaymentFilterState filterState,
  ) {
    var filtered = payments;

    if (filterState.statusFilter != null) {
      filtered = filtered.where((p) => p.status == filterState.statusFilter).toList();
    }

    if (filterState.methodFilter != null) {
      filtered = filtered.where((p) => p.method == filterState.methodFilter).toList();
    }

    if (filterState.typeFilter != null) {
      filtered = filtered.where((p) => p.type == filterState.typeFilter).toList();
    }

    if (filterState.startDate != null) {
      filtered = filtered.where((p) => p.paymentDate.isAfter(filterState.startDate!)).toList();
    }

    if (filterState.endDate != null) {
      filtered = filtered.where((p) => p.paymentDate.isBefore(filterState.endDate!)).toList();
    }

    if (filterState.minAmount != null) {
      filtered = filtered.where((p) => p.amount >= filterState.minAmount!).toList();
    }

    if (filterState.maxAmount != null) {
      filtered = filtered.where((p) => p.amount <= filterState.maxAmount!).toList();
    }

    return filtered;
  }

  /// بناء واجهة فارغة
  Widget _buildEmptyWidget() {
    return AdaptiveEmptyState(
      icon: Icons.payment,
      title: 'لا توجد مدفوعات',
      subtitle: 'اضغط على زر + لإضافة دفعة جديدة',
      action: ElevatedButton.icon(
        onPressed: _addNewPayment,
        icon: const Icon(Icons.add),
        label: const Text('إضافة دفعة'),
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget(String error) {
    return AdaptiveEmptyState(
      icon: Icons.error_outline,
      title: 'حدث خطأ في تحميل البيانات',
      subtitle: error,
      action: ElevatedButton(
        onPressed: () => ref.refresh(paymentsProvider),
        child: const Text('إعادة المحاولة'),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewPayment,
      tooltip: 'إضافة دفعة جديدة',
      child: const Icon(Icons.add),
    );
  }

  /// تغيير نص البحث
  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });
  }

  /// مسح البحث
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  /// إضافة دفعة جديدة
  void _addNewPayment() {
    showDialog(
      context: context,
      builder: (context) => const AddPaymentDialog(),
    ).then((result) {
      if (result == true) {
        ref.refresh(paymentsProvider);
        _showSuccessMessage('تم إضافة الدفعة بنجاح');
      }
    });
  }

  /// تعديل دفعة
  void _editPayment(PaymentEntity payment) {
    showDialog(
      context: context,
      builder: (context) => AddPaymentDialog(payment: payment),
    ).then((result) {
      if (result == true) {
        ref.refresh(paymentsProvider);
        _showSuccessMessage('تم تحديث الدفعة بنجاح');
      }
    });
  }

  /// حذف دفعة
  void _deletePayment(PaymentEntity payment) {
    AdaptiveDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      content: Text('هل أنت متأكد من حذف الدفعة "${payment.paymentNumber}"؟'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            await ref.read(paymentFormProvider.notifier)
                .deletePayment(payment.id);
            
            final state = ref.read(paymentFormProvider);
            if (state.isSuccess) {
              ref.refresh(paymentsProvider);
              _showSuccessMessage('تم حذف الدفعة بنجاح');
            } else if (state.error != null) {
              _showErrorMessage(state.error!);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('حذف'),
        ),
      ],
    );
  }

  /// تحديث حالة الدفعة
  void _updateStatus(PaymentEntity payment, PaymentStatus newStatus) async {
    await ref.read(paymentFormProvider.notifier)
        .updatePaymentStatus(payment.id, newStatus);
    
    final state = ref.read(paymentFormProvider);
    if (state.isSuccess) {
      ref.refresh(paymentsProvider);
      _showSuccessMessage('تم تحديث حالة الدفعة بنجاح');
    } else if (state.error != null) {
      _showErrorMessage(state.error!);
    }
  }

  /// الانتقال لتفاصيل الدفعة
  void _navigateToPaymentDetails(PaymentEntity payment) {
    AdaptiveDialog.show(
      context: context,
      title: 'تفاصيل الدفعة',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow('رقم الدفعة', payment.paymentNumber),
          _buildDetailRow('رقم الفاتورة', payment.billNumber),
          _buildDetailRow('المشترك', payment.subscriberName),
          _buildDetailRow('المبلغ', '${payment.amount.toStringAsFixed(2)} ريال'),
          _buildDetailRow('طريقة الدفع', payment.methodText),
          _buildDetailRow('نوع الدفع', payment.typeText),
          _buildDetailRow('تاريخ الدفع', payment.paymentDate.toString().substring(0, 10)),
          _buildDetailRow('الحالة', payment.statusText),
          _buildDetailRow('المحصل', payment.collectedBy),
          if (payment.referenceNumber != null) 
            _buildDetailRow('رقم المرجع', payment.referenceNumber!),
          if (payment.bankName != null) 
            _buildDetailRow('البنك', payment.bankName!),
          if (payment.checkNumber != null) 
            _buildDetailRow('رقم الشيك', payment.checkNumber!),
          if (payment.notes != null) 
            _buildDetailRow('ملاحظات', payment.notes!),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        if (payment.canBeCancelled)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _updateStatus(payment, PaymentStatus.cancelled);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('إلغاء'),
          ),
      ],
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
