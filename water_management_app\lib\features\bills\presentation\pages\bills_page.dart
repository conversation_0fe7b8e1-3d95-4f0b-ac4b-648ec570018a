import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../providers/bill_provider.dart';
import '../widgets/bill_card.dart';
import '../widgets/add_bill_dialog.dart';
import '../../domain/entities/bill_entity.dart';

class BillsPage extends ConsumerStatefulWidget {
  const BillsPage({super.key});

  @override
  ConsumerState<BillsPage> createState() => _BillsPageState();
}

class _BillsPageState extends ConsumerState<BillsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildSearchAndFilters(),
          Expanded(child: _buildBillsList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return AdaptiveCard(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            Icons.receipt_long,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة الفواتير',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'إنشاء ومتابعة الفواتير والمدفوعات',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildStatisticsChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة الإحصائيات
  Widget _buildStatisticsChip() {
    final statisticsAsync = ref.watch(billStatisticsProvider);
    
    return statisticsAsync.when(
      data: (stats) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          '${stats['total'] ?? 0} فاتورة',
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      loading: () => const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
      error: (_, __) => const Icon(Icons.error, color: Colors.red),
    );
  }

  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الفواتير...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          
          // مرشحات سريعة
          _buildQuickFilters(),
        ],
      ),
    );
  }

  /// بناء المرشحات السريعة
  Widget _buildQuickFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildStatusFilterChip('الكل', null),
          const SizedBox(width: 8),
          _buildStatusFilterChip('مسودة', BillStatus.draft),
          const SizedBox(width: 8),
          _buildStatusFilterChip('صادرة', BillStatus.issued),
          const SizedBox(width: 8),
          _buildStatusFilterChip('مدفوعة', BillStatus.paid),
          const SizedBox(width: 8),
          _buildStatusFilterChip('متأخرة', BillStatus.overdue),
          const SizedBox(width: 8),
          _buildOverdueFilterChip(),
          const SizedBox(width: 8),
          _buildUnpaidFilterChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة مرشح الحالة
  Widget _buildStatusFilterChip(String label, BillStatus? status) {
    final filterState = ref.watch(billFilterProvider);
    final isSelected = filterState.statusFilter == status;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(billFilterProvider.notifier)
            .updateStatusFilter(selected ? status : null);
      },
    );
  }

  /// بناء رقاقة مرشح المتأخرة
  Widget _buildOverdueFilterChip() {
    final filterState = ref.watch(billFilterProvider);

    return FilterChip(
      label: const Text('متأخرة فقط'),
      selected: filterState.showOverdueOnly,
      onSelected: (selected) {
        ref.read(billFilterProvider.notifier)
            .updateOverdueFilter(selected);
      },
    );
  }

  /// بناء رقاقة مرشح غير المدفوعة
  Widget _buildUnpaidFilterChip() {
    final filterState = ref.watch(billFilterProvider);

    return FilterChip(
      label: const Text('غير مدفوعة'),
      selected: filterState.showUnpaidOnly,
      onSelected: (selected) {
        ref.read(billFilterProvider.notifier)
            .updateUnpaidFilter(selected);
      },
    );
  }

  /// بناء قائمة الفواتير
  Widget _buildBillsList() {
    final billsAsync = _searchQuery.isEmpty
        ? ref.watch(billsProvider)
        : ref.watch(billSearchProvider(_searchQuery));

    return billsAsync.when(
      data: (bills) => _buildBillsListView(bills),
      loading: () => const AdaptiveLoadingIndicator(
        message: 'جاري تحميل الفواتير...',
      ),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  /// بناء عرض قائمة الفواتير
  Widget _buildBillsListView(List<BillEntity> bills) {
    final filterState = ref.watch(billFilterProvider);
    
    // تطبيق المرشحات
    final filteredBills = _applyFilters(bills, filterState);

    if (filteredBills.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(billsProvider);
      },
      child: AdaptiveLayoutBuilder(
        builder: (context, layoutType) {
          if (layoutType == LayoutType.mobile) {
            return _buildMobileList(filteredBills);
          } else {
            return _buildDesktopGrid(filteredBills);
          }
        },
      ),
    );
  }

  /// بناء قائمة للهاتف المحمول
  Widget _buildMobileList(List<BillEntity> bills) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bills.length,
      itemBuilder: (context, index) {
        return BillCard(
          bill: bills[index],
          onTap: () => _navigateToBillDetails(bills[index]),
          onEdit: () => _editBill(bills[index]),
          onDelete: () => _deleteBill(bills[index]),
          onPay: () => _payBill(bills[index]),
          onUpdateStatus: (status) => _updateStatus(bills[index], status),
        );
      },
    );
  }

  /// بناء شبكة لسطح المكتب
  Widget _buildDesktopGrid(List<BillEntity> bills) {
    final columns = AdaptiveLayout.getGridColumns(context);
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: bills.length,
      itemBuilder: (context, index) {
        return BillCard(
          bill: bills[index],
          onTap: () => _navigateToBillDetails(bills[index]),
          onEdit: () => _editBill(bills[index]),
          onDelete: () => _deleteBill(bills[index]),
          onPay: () => _payBill(bills[index]),
          onUpdateStatus: (status) => _updateStatus(bills[index], status),
        );
      },
    );
  }

  /// تطبيق المرشحات
  List<BillEntity> _applyFilters(
    List<BillEntity> bills,
    BillFilterState filterState,
  ) {
    var filtered = bills;

    if (filterState.statusFilter != null) {
      filtered = filtered.where((b) => b.status == filterState.statusFilter).toList();
    }

    if (filterState.typeFilter != null) {
      filtered = filtered.where((b) => b.type == filterState.typeFilter).toList();
    }

    if (filterState.showOverdueOnly) {
      filtered = filtered.where((b) => b.isOverdue).toList();
    }

    if (filterState.showUnpaidOnly) {
      filtered = filtered.where((b) => b.status != BillStatus.paid).toList();
    }

    if (filterState.startDate != null) {
      filtered = filtered.where((b) => b.issueDate.isAfter(filterState.startDate!)).toList();
    }

    if (filterState.endDate != null) {
      filtered = filtered.where((b) => b.issueDate.isBefore(filterState.endDate!)).toList();
    }

    return filtered;
  }

  /// بناء واجهة فارغة
  Widget _buildEmptyWidget() {
    return AdaptiveEmptyState(
      icon: Icons.receipt_long,
      title: 'لا توجد فواتير',
      subtitle: 'اضغط على زر + لإضافة فاتورة جديدة',
      action: ElevatedButton.icon(
        onPressed: _addNewBill,
        icon: const Icon(Icons.add),
        label: const Text('إضافة فاتورة'),
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget(String error) {
    return AdaptiveEmptyState(
      icon: Icons.error_outline,
      title: 'حدث خطأ في تحميل البيانات',
      subtitle: error,
      action: ElevatedButton(
        onPressed: () => ref.refresh(billsProvider),
        child: const Text('إعادة المحاولة'),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewBill,
      tooltip: 'إضافة فاتورة جديدة',
      child: const Icon(Icons.add),
    );
  }

  /// تغيير نص البحث
  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });
  }

  /// مسح البحث
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  /// إضافة فاتورة جديدة
  void _addNewBill() {
    showDialog(
      context: context,
      builder: (context) => const AddBillDialog(),
    ).then((result) {
      if (result == true) {
        ref.refresh(billsProvider);
        _showSuccessMessage('تم إضافة الفاتورة بنجاح');
      }
    });
  }

  /// تعديل فاتورة
  void _editBill(BillEntity bill) {
    showDialog(
      context: context,
      builder: (context) => AddBillDialog(bill: bill),
    ).then((result) {
      if (result == true) {
        ref.refresh(billsProvider);
        _showSuccessMessage('تم تحديث الفاتورة بنجاح');
      }
    });
  }

  /// حذف فاتورة
  void _deleteBill(BillEntity bill) {
    AdaptiveDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      content: Text('هل أنت متأكد من حذف الفاتورة "${bill.billNumber}"؟'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            await ref.read(billFormProvider.notifier)
                .deleteBill(bill.id);
            
            final state = ref.read(billFormProvider);
            if (state.isSuccess) {
              ref.refresh(billsProvider);
              _showSuccessMessage('تم حذف الفاتورة بنجاح');
            } else if (state.error != null) {
              _showErrorMessage(state.error!);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('حذف'),
        ),
      ],
    );
  }

  /// دفع فاتورة
  void _payBill(BillEntity bill) {
    // TODO: تنفيذ دفع الفاتورة
    _showSuccessMessage('سيتم تنفيذ دفع الفاتورة قريباً');
  }

  /// تحديث حالة الفاتورة
  void _updateStatus(BillEntity bill, BillStatus newStatus) async {
    await ref.read(billFormProvider.notifier)
        .updateBillStatus(bill.id, newStatus);
    
    final state = ref.read(billFormProvider);
    if (state.isSuccess) {
      ref.refresh(billsProvider);
      _showSuccessMessage('تم تحديث حالة الفاتورة بنجاح');
    } else if (state.error != null) {
      _showErrorMessage(state.error!);
    }
  }

  /// الانتقال لتفاصيل الفاتورة
  void _navigateToBillDetails(BillEntity bill) {
    AdaptiveDialog.show(
      context: context,
      title: 'تفاصيل الفاتورة',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow('رقم الفاتورة', bill.billNumber),
          _buildDetailRow('المشترك', bill.subscriberName),
          _buildDetailRow('القرية', bill.villageName),
          _buildDetailRow('العداد', bill.meterNumber),
          _buildDetailRow('الاستهلاك', '${bill.consumption.toStringAsFixed(2)} م³'),
          _buildDetailRow('مبلغ المياه', '${bill.waterAmount.toStringAsFixed(2)} ريال'),
          _buildDetailRow('المجموع', '${bill.totalAmount.toStringAsFixed(2)} ريال'),
          _buildDetailRow('تاريخ الإصدار', bill.issueDate.toString().substring(0, 10)),
          _buildDetailRow('تاريخ الاستحقاق', bill.dueDate.toString().substring(0, 10)),
          _buildDetailRow('الحالة', bill.statusText),
          if (bill.notes != null) _buildDetailRow('ملاحظات', bill.notes!),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        if (bill.canBePaid)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _payBill(bill);
            },
            child: const Text('دفع'),
          ),
      ],
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
