# 🗄️ نماذج قاعدة البيانات وأوامر SQL لتطبيق إدارة المياه

## 📊 هيكل قاعدة البيانات الكامل

### 1. جدول المستخدمين (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role user_role NOT NULL,
    is_active BOOLEAN DEFAULT true,
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret VARCHAR(32),
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> نوع البيانات للأدوار
CREATE TYPE user_role AS ENUM (
    'admin',
    'manager', 
    'accountant',
    'collector',
    'technician',
    'financial_reviewer',
    'warehouse_keeper',
    'subscriber'
);
```

### 2. جدول القرى (villages)
```sql
CREATE TABLE villages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    main_meter_id VARCHAR(50) UNIQUE,
    location POINT,
    population INTEGER,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. جدول المشتركين (subscribers)
```sql
CREATE TABLE subscribers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscriber_number VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    village_id UUID REFERENCES villages(id) ON DELETE RESTRICT,
    subscription_type subscription_type NOT NULL,
    status subscription_status DEFAULT 'active',
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    national_id VARCHAR(20),
    connection_date DATE,
    disconnection_date DATE,
    notes TEXT,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    is_charitable BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- أنواع الاشتراك
CREATE TYPE subscription_type AS ENUM (
    'residential',
    'commercial', 
    'industrial',
    'charitable',
    'governmental'
);

-- حالات الاشتراك
CREATE TYPE subscription_status AS ENUM (
    'active',
    'suspended',
    'overdue',
    'disconnected'
);
```

### 4. جدول العدادات (meters)
```sql
CREATE TABLE meters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    serial_number VARCHAR(50) UNIQUE NOT NULL,
    subscriber_id UUID REFERENCES subscribers(id) ON DELETE CASCADE,
    meter_type meter_type DEFAULT 'residential',
    status meter_status DEFAULT 'active',
    installation_date DATE,
    last_maintenance DATE,
    next_maintenance DATE,
    location POINT,
    initial_reading DECIMAL(10,3) DEFAULT 0,
    current_reading DECIMAL(10,3) DEFAULT 0,
    multiplier DECIMAL(5,2) DEFAULT 1.0,
    brand VARCHAR(50),
    model VARCHAR(50),
    diameter VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- أنواع العدادات
CREATE TYPE meter_type AS ENUM (
    'residential',
    'commercial',
    'industrial',
    'main'
);

-- حالات العدادات
CREATE TYPE meter_status AS ENUM (
    'active',
    'inactive',
    'damaged',
    'under_maintenance',
    'replaced'
);
```

### 5. جدول القراءات (readings)
```sql
CREATE TABLE readings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meter_id UUID REFERENCES meters(id) ON DELETE CASCADE,
    current_reading DECIMAL(10,3) NOT NULL,
    previous_reading DECIMAL(10,3) NOT NULL,
    consumption DECIMAL(10,3) GENERATED ALWAYS AS (current_reading - previous_reading) STORED,
    reading_date DATE NOT NULL,
    reading_period VARCHAR(7) NOT NULL, -- YYYY-MM format
    read_by UUID REFERENCES users(id),
    reading_method reading_method NOT NULL,
    is_validated BOOLEAN DEFAULT false,
    is_estimated BOOLEAN DEFAULT false,
    validation_notes TEXT,
    image_path VARCHAR(255),
    gps_location POINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_reading_positive CHECK (current_reading >= 0),
    CONSTRAINT chk_consumption_reasonable CHECK (consumption >= 0 AND consumption <= 1000),
    UNIQUE(meter_id, reading_period)
);

-- طرق القراءة
CREATE TYPE reading_method AS ENUM (
    'manual',
    'photo',
    'estimated',
    'automatic'
);
```

### 6. جدول شرائح التسعير (pricing_tiers)
```sql
CREATE TABLE pricing_tiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_type subscription_type NOT NULL,
    tier_number INTEGER NOT NULL,
    min_consumption DECIMAL(10,3) NOT NULL,
    max_consumption DECIMAL(10,3),
    price_per_unit DECIMAL(10,2) NOT NULL,
    fixed_charge DECIMAL(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_tier_range CHECK (min_consumption >= 0),
    CONSTRAINT chk_price_positive CHECK (price_per_unit >= 0),
    UNIQUE(subscription_type, tier_number, effective_from)
);
```

### 7. جدول الفواتير (bills)
```sql
CREATE TABLE bills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_number VARCHAR(20) UNIQUE NOT NULL,
    subscriber_id UUID REFERENCES subscribers(id) ON DELETE RESTRICT,
    meter_id UUID REFERENCES meters(id) ON DELETE RESTRICT,
    reading_id UUID REFERENCES readings(id) ON DELETE RESTRICT,
    billing_period VARCHAR(7) NOT NULL, -- YYYY-MM
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    
    -- تفاصيل الاستهلاك
    previous_reading DECIMAL(10,3) NOT NULL,
    current_reading DECIMAL(10,3) NOT NULL,
    consumption DECIMAL(10,3) NOT NULL,
    
    -- تفاصيل المبالغ
    water_charges DECIMAL(10,2) NOT NULL,
    fixed_charges DECIMAL(10,2) DEFAULT 0,
    service_charges DECIMAL(10,2) DEFAULT 0,
    penalties DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    
    -- الحالة والدفع
    status bill_status DEFAULT 'pending',
    paid_amount DECIMAL(10,2) DEFAULT 0,
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    
    -- معلومات إضافية
    notes TEXT,
    is_estimated BOOLEAN DEFAULT false,
    generated_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_amounts_positive CHECK (
        water_charges >= 0 AND 
        fixed_charges >= 0 AND 
        service_charges >= 0 AND 
        penalties >= 0 AND 
        total_amount >= 0 AND 
        paid_amount >= 0
    )
);

-- حالات الفواتير
CREATE TYPE bill_status AS ENUM (
    'draft',
    'pending',
    'paid',
    'partially_paid',
    'overdue',
    'cancelled'
);
```

### 8. جدول المدفوعات (payments)
```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_number VARCHAR(20) UNIQUE NOT NULL,
    subscriber_id UUID REFERENCES subscribers(id) ON DELETE RESTRICT,
    bill_id UUID REFERENCES bills(id) ON DELETE RESTRICT,
    
    -- تفاصيل الدفع
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method payment_method NOT NULL,
    currency_code VARCHAR(3) DEFAULT 'SAR',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0,
    
    -- معلومات السند
    receipt_number VARCHAR(50),
    bank_reference VARCHAR(100),
    check_number VARCHAR(50),
    
    -- معلومات المحصل
    collected_by UUID REFERENCES users(id),
    collection_location VARCHAR(100),
    
    -- الحالة والتأكيد
    status payment_status DEFAULT 'confirmed',
    confirmed_by UUID REFERENCES users(id),
    confirmation_date TIMESTAMP,
    
    -- معلومات إضافية
    notes TEXT,
    attachment_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_payment_amount_positive CHECK (amount > 0)
);

-- طرق الدفع
CREATE TYPE payment_method AS ENUM (
    'cash',
    'bank_transfer',
    'check',
    'credit_card',
    'mobile_payment'
);

-- حالات الدفع
CREATE TYPE payment_status AS ENUM (
    'pending',
    'confirmed',
    'cancelled',
    'refunded'
);
```

### 9. جدول طلبات الصيانة (maintenance_requests)
```sql
CREATE TABLE maintenance_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_number VARCHAR(20) UNIQUE NOT NULL,
    subscriber_id UUID REFERENCES subscribers(id) ON DELETE RESTRICT,
    meter_id UUID REFERENCES meters(id) ON DELETE SET NULL,
    
    -- تفاصيل الطلب
    request_type maintenance_type NOT NULL,
    priority priority_level DEFAULT 'medium',
    description TEXT NOT NULL,
    location_description TEXT,
    gps_location POINT,
    
    -- الحالة والتوقيتات
    status maintenance_status DEFAULT 'pending',
    requested_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scheduled_date TIMESTAMP,
    started_date TIMESTAMP,
    completed_date TIMESTAMP,
    
    -- المسؤولون
    requested_by UUID REFERENCES users(id),
    assigned_to UUID REFERENCES users(id),
    completed_by UUID REFERENCES users(id),
    
    -- التكلفة والمواد
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    materials_used JSONB,
    
    -- معلومات إضافية
    completion_notes TEXT,
    customer_satisfaction INTEGER CHECK (customer_satisfaction BETWEEN 1 AND 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- أنواع الصيانة
CREATE TYPE maintenance_type AS ENUM (
    'repair',
    'replacement',
    'installation',
    'inspection',
    'cleaning',
    'calibration'
);

-- مستويات الأولوية
CREATE TYPE priority_level AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);

-- حالات الصيانة
CREATE TYPE maintenance_status AS ENUM (
    'pending',
    'assigned',
    'in_progress',
    'completed',
    'cancelled',
    'on_hold'
);
```

### 10. جدول المصروفات (expenses)
```sql
CREATE TABLE expenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    expense_number VARCHAR(20) UNIQUE NOT NULL,
    category_id UUID REFERENCES expense_categories(id),
    
    -- تفاصيل المصروف
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency_code VARCHAR(3) DEFAULT 'SAR',
    expense_date DATE NOT NULL,
    
    -- الموافقات
    status expense_status DEFAULT 'pending',
    requested_by UUID REFERENCES users(id) NOT NULL,
    approved_by UUID REFERENCES users(id),
    approval_date TIMESTAMP,
    
    -- معلومات الدفع
    payment_method payment_method,
    vendor_name VARCHAR(100),
    invoice_number VARCHAR(50),
    
    -- المرفقات والملاحظات
    receipt_path VARCHAR(255),
    notes TEXT,
    
    -- ربط بالمشاريع
    project_id UUID,
    maintenance_request_id UUID REFERENCES maintenance_requests(id),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_expense_amount_positive CHECK (amount > 0)
);

-- حالات المصروفات
CREATE TYPE expense_status AS ENUM (
    'draft',
    'pending',
    'approved',
    'rejected',
    'paid'
);
```

### 11. جدول فئات المصروفات (expense_categories)
```sql
CREATE TABLE expense_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES expense_categories(id),
    budget_limit DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 12. جدول المخزون (inventory_items)
```sql
CREATE TABLE inventory_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    item_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    unit_of_measure VARCHAR(20) NOT NULL,
    
    -- الكميات
    current_stock DECIMAL(10,3) NOT NULL DEFAULT 0,
    minimum_stock DECIMAL(10,3) NOT NULL DEFAULT 0,
    maximum_stock DECIMAL(10,3),
    reorder_point DECIMAL(10,3),
    
    -- التكلفة
    unit_cost DECIMAL(10,2),
    last_purchase_price DECIMAL(10,2),
    average_cost DECIMAL(10,2),
    
    -- معلومات إضافية
    supplier_name VARCHAR(100),
    location VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_stock_non_negative CHECK (current_stock >= 0)
);
```

### 13. جدول حركات المخزون (inventory_movements)
```sql
CREATE TABLE inventory_movements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    item_id UUID REFERENCES inventory_items(id) ON DELETE RESTRICT,
    movement_type movement_type NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    
    -- المرجع
    reference_type VARCHAR(50),
    reference_id UUID,
    reference_number VARCHAR(50),
    
    -- التفاصيل
    description TEXT,
    movement_date DATE NOT NULL,
    performed_by UUID REFERENCES users(id) NOT NULL,
    
    -- الأرصدة
    stock_before DECIMAL(10,3) NOT NULL,
    stock_after DECIMAL(10,3) NOT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- أنواع حركات المخزون
CREATE TYPE movement_type AS ENUM (
    'purchase',
    'sale',
    'usage',
    'adjustment',
    'transfer',
    'return',
    'damage',
    'expired'
);
```

### 14. جدول سجل العمليات (audit_logs)
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    operation audit_operation NOT NULL,
    
    -- المستخدم والتوقيت
    user_id UUID REFERENCES users(id),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    
    -- البيانات
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    
    -- معلومات إضافية
    reason TEXT,
    session_id VARCHAR(100)
);

-- أنواع العمليات
CREATE TYPE audit_operation AS ENUM (
    'INSERT',
    'UPDATE', 
    'DELETE',
    'LOGIN',
    'LOGOUT',
    'FAILED_LOGIN'
);
```

---

## 🔍 الفهارس والاستعلامات المحسنة

### الفهارس الأساسية
```sql
-- فهارس الأداء
CREATE INDEX idx_subscribers_village_id ON subscribers(village_id);
CREATE INDEX idx_subscribers_status ON subscribers(status);
CREATE INDEX idx_meters_subscriber_id ON meters(subscriber_id);
CREATE INDEX idx_meters_status ON meters(status);
CREATE INDEX idx_readings_meter_period ON readings(meter_id, reading_period);
CREATE INDEX idx_readings_date ON readings(reading_date);
CREATE INDEX idx_bills_subscriber_period ON bills(subscriber_id, billing_period);
CREATE INDEX idx_bills_status ON bills(status);
CREATE INDEX idx_payments_subscriber_date ON payments(subscriber_id, payment_date);
CREATE INDEX idx_payments_bill_id ON payments(bill_id);
CREATE INDEX idx_maintenance_status ON maintenance_requests(status);
CREATE INDEX idx_maintenance_assigned ON maintenance_requests(assigned_to);
CREATE INDEX idx_expenses_date ON expenses(expense_date);
CREATE INDEX idx_expenses_category ON expenses(category_id);
CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_logs_user_timestamp ON audit_logs(user_id, timestamp);

-- فهارس النصوص للبحث
CREATE INDEX idx_subscribers_name_gin ON subscribers USING gin(to_tsvector('arabic', full_name));
CREATE INDEX idx_villages_name_gin ON villages USING gin(to_tsvector('arabic', name));
```

### استعلامات مفيدة
```sql
-- إجمالي الاستهلاك لكل قرية في شهر معين
CREATE OR REPLACE VIEW village_consumption_summary AS
SELECT 
    v.name as village_name,
    r.reading_period,
    COUNT(r.id) as total_readings,
    SUM(r.consumption) as total_consumption,
    AVG(r.consumption) as average_consumption,
    MAX(r.consumption) as max_consumption,
    MIN(r.consumption) as min_consumption
FROM villages v
JOIN subscribers s ON v.id = s.village_id
JOIN meters m ON s.id = m.subscriber_id
JOIN readings r ON m.id = r.meter_id
WHERE r.is_validated = true
GROUP BY v.id, v.name, r.reading_period
ORDER BY v.name, r.reading_period;

-- كشف حساب المشترك
CREATE OR REPLACE FUNCTION get_subscriber_statement(
    p_subscriber_id UUID,
    p_from_date DATE DEFAULT NULL,
    p_to_date DATE DEFAULT NULL
)
RETURNS TABLE (
    transaction_date DATE,
    transaction_type VARCHAR(20),
    description TEXT,
    debit_amount DECIMAL(10,2),
    credit_amount DECIMAL(10,2),
    balance DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH transactions AS (
        -- الفواتير
        SELECT 
            b.issue_date as trans_date,
            'BILL' as trans_type,
            'فاتورة رقم ' || b.bill_number as description,
            b.total_amount as debit,
            0::DECIMAL(10,2) as credit
        FROM bills b
        WHERE b.subscriber_id = p_subscriber_id
        AND (p_from_date IS NULL OR b.issue_date >= p_from_date)
        AND (p_to_date IS NULL OR b.issue_date <= p_to_date)
        
        UNION ALL
        
        -- المدفوعات
        SELECT 
            p.payment_date as trans_date,
            'PAYMENT' as trans_type,
            'دفعة رقم ' || p.payment_number as description,
            0::DECIMAL(10,2) as debit,
            p.amount as credit
        FROM payments p
        WHERE p.subscriber_id = p_subscriber_id
        AND (p_from_date IS NULL OR p.payment_date >= p_from_date)
        AND (p_to_date IS NULL OR p.payment_date <= p_to_date)
    ),
    running_balance AS (
        SELECT *,
            SUM(debit - credit) OVER (ORDER BY trans_date, trans_type) as running_bal
        FROM transactions
        ORDER BY trans_date, trans_type
    )
    SELECT 
        trans_date,
        trans_type,
        description,
        debit,
        credit,
        running_bal
    FROM running_balance;
END;
$$ LANGUAGE plpgsql;
```

---

## 🔧 إجراءات مخزنة مفيدة

### إجراء إنشاء الفواتير الشهرية
```sql
CREATE OR REPLACE FUNCTION generate_monthly_bills(
    p_billing_period VARCHAR(7), -- YYYY-MM
    p_generated_by UUID
)
RETURNS TABLE (
    subscriber_id UUID,
    bill_id UUID,
    total_amount DECIMAL(10,2),
    status TEXT
) AS $$
DECLARE
    rec RECORD;
    v_bill_id UUID;
    v_consumption DECIMAL(10,3);
    v_water_charges DECIMAL(10,2);
    v_total_amount DECIMAL(10,2);
BEGIN
    -- التكرار عبر جميع المشتركين النشطين
    FOR rec IN 
        SELECT DISTINCT s.id as subscriber_id, m.id as meter_id
        FROM subscribers s
        JOIN meters m ON s.id = m.subscriber_id
        WHERE s.status = 'active' AND m.status = 'active'
    LOOP
        -- الحصول على آخر قراءة للعداد
        SELECT r.consumption INTO v_consumption
        FROM readings r
        WHERE r.meter_id = rec.meter_id 
        AND r.reading_period = p_billing_period
        AND r.is_validated = true
        LIMIT 1;
        
        -- إذا لم توجد قراءة، تخطي هذا المشترك
        IF v_consumption IS NULL THEN
            RETURN QUERY SELECT rec.subscriber_id, NULL::UUID, 0::DECIMAL(10,2), 'NO_READING'::TEXT;
            CONTINUE;
        END IF;
        
        -- حساب رسوم المياه
        SELECT calculate_water_charges(rec.subscriber_id, v_consumption) INTO v_water_charges;
        
        -- حساب المبلغ الإجمالي
        v_total_amount := v_water_charges;
        
        -- إنشاء الفاتورة
        INSERT INTO bills (
            bill_number,
            subscriber_id,
            meter_id,
            billing_period,
            issue_date,
            due_date,
            consumption,
            water_charges,
            total_amount,
            generated_by
        ) VALUES (
            generate_bill_number(),
            rec.subscriber_id,
            rec.meter_id,
            p_billing_period,
            CURRENT_DATE,
            CURRENT_DATE + INTERVAL '30 days',
            v_consumption,
            v_water_charges,
            v_total_amount,
            p_generated_by
        ) RETURNING id INTO v_bill_id;
        
        RETURN QUERY SELECT rec.subscriber_id, v_bill_id, v_total_amount, 'SUCCESS'::TEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

---

*هذا الملف يوفر هيكل قاعدة بيانات شامل ومحسن لتطبيق إدارة المياه مع جميع الجداول والعلاقات والإجراءات المطلوبة.*
