import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../providers/report_provider.dart';
import '../widgets/report_card.dart';
import '../widgets/report_generator_dialog.dart';
import '../../domain/entities/report_entity.dart';

class ReportsPage extends ConsumerStatefulWidget {
  const ReportsPage({super.key});

  @override
  ConsumerState<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends ConsumerState<ReportsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildQuickReports(),
          Expanded(child: _buildReportsList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return AdaptiveCard(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            Icons.bar_chart,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'التقارير والإحصائيات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'إنشاء وعرض التقارير التفصيلية والإحصائيات',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التقارير السريعة
  Widget _buildQuickReports() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التقارير السريعة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildQuickReportButtons(),
        ],
      ),
    );
  }

  /// بناء أزرار التقارير السريعة
  Widget _buildQuickReportButtons() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildQuickReportButton(
            'الإحصائيات العامة',
            Icons.analytics,
            Colors.blue,
            () => _generateStatisticsReport(),
          ),
          const SizedBox(width: 12),
          _buildQuickReportButton(
            'التقرير المالي',
            Icons.account_balance,
            Colors.green,
            () => _generateFinancialReport(),
          ),
          const SizedBox(width: 12),
          _buildQuickReportButton(
            'المتأخرات',
            Icons.warning,
            Colors.red,
            () => _generateOverdueReport(),
          ),
          const SizedBox(width: 12),
          _buildQuickReportButton(
            'الاستهلاك',
            Icons.water_drop,
            Colors.cyan,
            () => _generateConsumptionReport(),
          ),
        ],
      ),
    );
  }

  /// بناء زر تقرير سريع
  Widget _buildQuickReportButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// بناء قائمة التقارير
  Widget _buildReportsList() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التقارير المحفوظة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(child: _buildSavedReports()),
        ],
      ),
    );
  }

  /// بناء التقارير المحفوظة
  Widget _buildSavedReports() {
    // في التطبيق الحقيقي، ستكون هذه قائمة من التقارير المحفوظة
    return _buildEmptyReportsWidget();
  }

  /// بناء واجهة التقارير الفارغة
  Widget _buildEmptyReportsWidget() {
    return AdaptiveEmptyState(
      icon: Icons.bar_chart,
      title: 'لا توجد تقارير محفوظة',
      subtitle: 'استخدم الأزرار أعلاه لإنشاء تقارير جديدة',
      action: ElevatedButton.icon(
        onPressed: _openReportGenerator,
        icon: const Icon(Icons.add),
        label: const Text('إنشاء تقرير مخصص'),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _openReportGenerator,
      tooltip: 'إنشاء تقرير مخصص',
      child: const Icon(Icons.add),
    );
  }

  /// إنشاء تقرير الإحصائيات
  void _generateStatisticsReport() async {
    _showLoadingDialog('جاري إنشاء تقرير الإحصائيات...');
    
    try {
      await ref.read(reportGeneratorProvider.notifier).generateStatisticsReport();
      
      final state = ref.read(reportGeneratorProvider);
      if (!mounted) return;
      
      Navigator.of(context).pop(); // إغلاق حوار التحميل
      
      if (state.isSuccess && state.report != null) {
        _showReportDialog(state.report!);
      } else if (state.error != null) {
        _showErrorMessage(state.error!);
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.of(context).pop();
      _showErrorMessage('خطأ في إنشاء التقرير: $e');
    }
  }

  /// إنشاء التقرير المالي
  void _generateFinancialReport() async {
    final filterState = ref.read(reportFilterProvider);
    
    _showLoadingDialog('جاري إنشاء التقرير المالي...');
    
    try {
      await ref.read(reportGeneratorProvider.notifier).generateFinancialReport(
        startDate: filterState.startDate,
        endDate: filterState.endDate,
      );
      
      final state = ref.read(reportGeneratorProvider);
      if (!mounted) return;
      
      Navigator.of(context).pop();
      
      if (state.isSuccess && state.report != null) {
        _showReportDialog(state.report!);
      } else if (state.error != null) {
        _showErrorMessage(state.error!);
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.of(context).pop();
      _showErrorMessage('خطأ في إنشاء التقرير: $e');
    }
  }

  /// إنشاء تقرير المتأخرات
  void _generateOverdueReport() async {
    _showLoadingDialog('جاري إنشاء تقرير المتأخرات...');
    
    try {
      await ref.read(reportGeneratorProvider.notifier).generateOverdueReport();
      
      final state = ref.read(reportGeneratorProvider);
      if (!mounted) return;
      
      Navigator.of(context).pop();
      
      if (state.isSuccess && state.report != null) {
        _showReportDialog(state.report!);
      } else if (state.error != null) {
        _showErrorMessage(state.error!);
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.of(context).pop();
      _showErrorMessage('خطأ في إنشاء التقرير: $e');
    }
  }

  /// إنشاء تقرير الاستهلاك
  void _generateConsumptionReport() async {
    final filterState = ref.read(reportFilterProvider);
    
    _showLoadingDialog('جاري إنشاء تقرير الاستهلاك...');
    
    try {
      await ref.read(reportGeneratorProvider.notifier).generateConsumptionReport(
        startDate: filterState.startDate,
        endDate: filterState.endDate,
      );
      
      final state = ref.read(reportGeneratorProvider);
      if (!mounted) return;
      
      Navigator.of(context).pop();
      
      if (state.isSuccess && state.report != null) {
        _showReportDialog(state.report!);
      } else if (state.error != null) {
        _showErrorMessage(state.error!);
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.of(context).pop();
      _showErrorMessage('خطأ في إنشاء التقرير: $e');
    }
  }

  /// فتح مولد التقارير المخصص
  void _openReportGenerator() {
    showDialog(
      context: context,
      builder: (context) => const ReportGeneratorDialog(),
    );
  }

  /// عرض حوار التحميل
  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  /// عرض حوار التقرير
  void _showReportDialog(ReportEntity report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(report.title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(report.description),
              const SizedBox(height: 16),
              Text(
                'تم إنشاؤه في: ${report.generatedAt.toString().substring(0, 19)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 8),
              Text(
                'الفترة: ${report.summary}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 16),
              _buildReportSummary(report),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportReport(report);
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// بناء ملخص التقرير
  Widget _buildReportSummary(ReportEntity report) {
    final data = report.data;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص البيانات:',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...data.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Text('${entry.key}: ${entry.value}'),
          );
        }).toList(),
      ],
    );
  }

  /// تصدير التقرير
  void _exportReport(ReportEntity report) {
    // TODO: تنفيذ تصدير التقرير
    _showSuccessMessage('سيتم تنفيذ تصدير التقرير قريباً');
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
