import 'package:equatable/equatable.dart';

/// كيان القرية
class VillageEntity extends Equatable {
  final int id;
  final String name;
  final String? description;
  final String? location;
  final double? latitude;
  final double? longitude;
  final int totalSubscribers;
  final int activeSubscribers;
  final int totalMeters;
  final double totalConsumption;
  final String? contactPerson;
  final String? contactPhone;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const VillageEntity({
    required this.id,
    required this.name,
    this.description,
    this.location,
    this.latitude,
    this.longitude,
    required this.totalSubscribers,
    required this.activeSubscribers,
    required this.totalMeters,
    required this.totalConsumption,
    this.contactPerson,
    this.contactPhone,
    this.notes,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        location,
        latitude,
        longitude,
        totalSubscribers,
        activeSubscribers,
        totalMeters,
        totalConsumption,
        contactPerson,
        contactPhone,
        notes,
        isActive,
        createdAt,
        updatedAt,
      ];

  /// نسخ الكيان مع تعديل بعض الخصائص
  VillageEntity copyWith({
    int? id,
    String? name,
    String? description,
    String? location,
    double? latitude,
    double? longitude,
    int? totalSubscribers,
    int? activeSubscribers,
    int? totalMeters,
    double? totalConsumption,
    String? contactPerson,
    String? contactPhone,
    String? notes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VillageEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      totalSubscribers: totalSubscribers ?? this.totalSubscribers,
      activeSubscribers: activeSubscribers ?? this.activeSubscribers,
      totalMeters: totalMeters ?? this.totalMeters,
      totalConsumption: totalConsumption ?? this.totalConsumption,
      contactPerson: contactPerson ?? this.contactPerson,
      contactPhone: contactPhone ?? this.contactPhone,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'total_subscribers': totalSubscribers,
      'active_subscribers': activeSubscribers,
      'total_meters': totalMeters,
      'total_consumption': totalConsumption,
      'contact_person': contactPerson,
      'contact_phone': contactPhone,
      'notes': notes,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory VillageEntity.fromMap(Map<String, dynamic> map) {
    return VillageEntity(
      id: map['id'] as int,
      name: map['name'] as String,
      description: map['description'] as String?,
      location: map['location'] as String?,
      latitude: (map['latitude'] as num?)?.toDouble(),
      longitude: (map['longitude'] as num?)?.toDouble(),
      totalSubscribers: map['total_subscribers'] as int? ?? 0,
      activeSubscribers: map['active_subscribers'] as int? ?? 0,
      totalMeters: map['total_meters'] as int? ?? 0,
      totalConsumption: (map['total_consumption'] as num?)?.toDouble() ?? 0.0,
      contactPerson: map['contact_person'] as String?,
      contactPhone: map['contact_phone'] as String?,
      notes: map['notes'] as String?,
      isActive: map['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// الحصول على نسبة المشتركين النشطين
  double get activeSubscribersPercentage {
    if (totalSubscribers == 0) return 0.0;
    return (activeSubscribers / totalSubscribers) * 100;
  }

  /// الحصول على متوسط الاستهلاك لكل مشترك
  double get averageConsumptionPerSubscriber {
    if (activeSubscribers == 0) return 0.0;
    return totalConsumption / activeSubscribers;
  }

  /// الحصول على نص الحالة
  String get statusText {
    return isActive ? 'نشطة' : 'غير نشطة';
  }

  /// الحصول على لون الحالة
  String get statusColor {
    return isActive ? 'green' : 'red';
  }

  /// التحقق من وجود إحداثيات
  bool get hasCoordinates {
    return latitude != null && longitude != null;
  }

  /// الحصول على معلومات الموقع
  String get locationInfo {
    if (hasCoordinates) {
      return 'خط العرض: ${latitude!.toStringAsFixed(6)}, خط الطول: ${longitude!.toStringAsFixed(6)}';
    }
    return location ?? 'غير محدد';
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return name.isNotEmpty;
  }

  /// الحصول على ملخص القرية
  String get summary {
    return '$name - $totalSubscribers مشترك ($activeSubscribers نشط)';
  }

  /// الحصول على معلومات الاستهلاك
  String get consumptionInfo {
    return 'إجمالي الاستهلاك: ${totalConsumption.toStringAsFixed(2)} م³';
  }

  /// الحصول على معلومات الاتصال
  String? get contactInfo {
    if (contactPerson != null && contactPhone != null) {
      return '$contactPerson - $contactPhone';
    } else if (contactPerson != null) {
      return contactPerson;
    } else if (contactPhone != null) {
      return contactPhone;
    }
    return null;
  }

  /// تحديث الإحصائيات
  VillageEntity updateStatistics({
    required int totalSubscribers,
    required int activeSubscribers,
    required int totalMeters,
    required double totalConsumption,
  }) {
    return copyWith(
      totalSubscribers: totalSubscribers,
      activeSubscribers: activeSubscribers,
      totalMeters: totalMeters,
      totalConsumption: totalConsumption,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث معلومات الاتصال
  VillageEntity updateContactInfo({
    String? contactPerson,
    String? contactPhone,
  }) {
    return copyWith(
      contactPerson: contactPerson,
      contactPhone: contactPhone,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث الموقع
  VillageEntity updateLocation({
    String? location,
    double? latitude,
    double? longitude,
  }) {
    return copyWith(
      location: location,
      latitude: latitude,
      longitude: longitude,
      updatedAt: DateTime.now(),
    );
  }

  /// تفعيل/إلغاء تفعيل القرية
  VillageEntity toggleActive() {
    return copyWith(
      isActive: !isActive,
      updatedAt: DateTime.now(),
    );
  }
}
