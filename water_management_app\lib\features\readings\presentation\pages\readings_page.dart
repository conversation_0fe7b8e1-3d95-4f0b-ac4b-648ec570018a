import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../providers/reading_provider.dart';
import '../widgets/reading_card.dart';
import '../widgets/add_reading_dialog.dart';
import '../../domain/entities/reading_entity.dart';

class ReadingsPage extends ConsumerStatefulWidget {
  const ReadingsPage({super.key});

  @override
  ConsumerState<ReadingsPage> createState() => _ReadingsPageState();
}

class _ReadingsPageState extends ConsumerState<ReadingsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildSearchAndFilters(),
          Expanded(child: _buildReadingsList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return AdaptiveCard(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            Icons.assessment,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة القراءات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'تسجيل ومتابعة قراءات العدادات الشهرية',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildStatisticsChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة الإحصائيات
  Widget _buildStatisticsChip() {
    final statisticsAsync = ref.watch(readingStatisticsProvider);
    
    return statisticsAsync.when(
      data: (stats) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          '${stats['total'] ?? 0} قراءة',
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      loading: () => const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
      error: (_, __) => const Icon(Icons.error, color: Colors.red),
    );
  }

  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في القراءات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          
          // مرشحات سريعة
          _buildQuickFilters(),
        ],
      ),
    );
  }

  /// بناء المرشحات السريعة
  Widget _buildQuickFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildStatusFilterChip('الكل', null),
          const SizedBox(width: 8),
          _buildStatusFilterChip('في الانتظار', ReadingStatus.pending),
          const SizedBox(width: 8),
          _buildStatusFilterChip('مؤكدة', ReadingStatus.confirmed),
          const SizedBox(width: 8),
          _buildStatusFilterChip('مفوترة', ReadingStatus.billed),
          const SizedBox(width: 8),
          _buildTypeFilterChip('عادية', ReadingType.regular),
          const SizedBox(width: 8),
          _buildTypeFilterChip('تقديرية', ReadingType.estimated),
          const SizedBox(width: 8),
          _buildEstimatedFilterChip(),
          const SizedBox(width: 8),
          _buildUnconfirmedFilterChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة مرشح الحالة
  Widget _buildStatusFilterChip(String label, ReadingStatus? status) {
    final filterState = ref.watch(readingFilterProvider);
    final isSelected = filterState.statusFilter == status;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(readingFilterProvider.notifier)
            .updateStatusFilter(selected ? status : null);
      },
    );
  }

  /// بناء رقاقة مرشح النوع
  Widget _buildTypeFilterChip(String label, ReadingType type) {
    final filterState = ref.watch(readingFilterProvider);
    final isSelected = filterState.typeFilter == type;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(readingFilterProvider.notifier)
            .updateTypeFilter(selected ? type : null);
      },
    );
  }

  /// بناء رقاقة مرشح التقديرية
  Widget _buildEstimatedFilterChip() {
    final filterState = ref.watch(readingFilterProvider);

    return FilterChip(
      label: const Text('تقديرية فقط'),
      selected: filterState.showEstimatedOnly,
      onSelected: (selected) {
        ref.read(readingFilterProvider.notifier)
            .updateEstimatedFilter(selected);
      },
    );
  }

  /// بناء رقاقة مرشح غير المؤكدة
  Widget _buildUnconfirmedFilterChip() {
    final filterState = ref.watch(readingFilterProvider);

    return FilterChip(
      label: const Text('غير مؤكدة'),
      selected: filterState.showUnconfirmedOnly,
      onSelected: (selected) {
        ref.read(readingFilterProvider.notifier)
            .updateUnconfirmedFilter(selected);
      },
    );
  }

  /// بناء قائمة القراءات
  Widget _buildReadingsList() {
    final readingsAsync = _searchQuery.isEmpty
        ? ref.watch(readingsProvider)
        : ref.watch(readingSearchProvider(_searchQuery));

    return readingsAsync.when(
      data: (readings) => _buildReadingsListView(readings),
      loading: () => const AdaptiveLoadingIndicator(
        message: 'جاري تحميل القراءات...',
      ),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  /// بناء عرض قائمة القراءات
  Widget _buildReadingsListView(List<ReadingEntity> readings) {
    final filterState = ref.watch(readingFilterProvider);
    
    // تطبيق المرشحات
    final filteredReadings = _applyFilters(readings, filterState);

    if (filteredReadings.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(readingsProvider);
      },
      child: AdaptiveLayoutBuilder(
        builder: (context, layoutType) {
          if (layoutType == LayoutType.mobile) {
            return _buildMobileList(filteredReadings);
          } else {
            return _buildDesktopGrid(filteredReadings);
          }
        },
      ),
    );
  }

  /// بناء قائمة للهاتف المحمول
  Widget _buildMobileList(List<ReadingEntity> readings) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: readings.length,
      itemBuilder: (context, index) {
        return ReadingCard(
          reading: readings[index],
          onTap: () => _navigateToReadingDetails(readings[index]),
          onEdit: () => _editReading(readings[index]),
          onDelete: () => _deleteReading(readings[index]),
          onConfirm: () => _confirmReading(readings[index]),
        );
      },
    );
  }

  /// بناء شبكة لسطح المكتب
  Widget _buildDesktopGrid(List<ReadingEntity> readings) {
    final columns = AdaptiveLayout.getGridColumns(context);
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: readings.length,
      itemBuilder: (context, index) {
        return ReadingCard(
          reading: readings[index],
          onTap: () => _navigateToReadingDetails(readings[index]),
          onEdit: () => _editReading(readings[index]),
          onDelete: () => _deleteReading(readings[index]),
          onConfirm: () => _confirmReading(readings[index]),
        );
      },
    );
  }

  /// تطبيق المرشحات
  List<ReadingEntity> _applyFilters(
    List<ReadingEntity> readings,
    ReadingFilterState filterState,
  ) {
    var filtered = readings;

    if (filterState.statusFilter != null) {
      filtered = filtered.where((r) => r.status == filterState.statusFilter).toList();
    }

    if (filterState.typeFilter != null) {
      filtered = filtered.where((r) => r.type == filterState.typeFilter).toList();
    }

    if (filterState.showEstimatedOnly) {
      filtered = filtered.where((r) => r.isEstimated).toList();
    }

    if (filterState.showUnconfirmedOnly) {
      filtered = filtered.where((r) => r.status == ReadingStatus.pending).toList();
    }

    if (filterState.startDate != null) {
      filtered = filtered.where((r) => r.readingDate.isAfter(filterState.startDate!)).toList();
    }

    if (filterState.endDate != null) {
      filtered = filtered.where((r) => r.readingDate.isBefore(filterState.endDate!)).toList();
    }

    return filtered;
  }

  /// بناء واجهة فارغة
  Widget _buildEmptyWidget() {
    return AdaptiveEmptyState(
      icon: Icons.assessment,
      title: 'لا توجد قراءات',
      subtitle: 'اضغط على زر + لإضافة قراءة جديدة',
      action: ElevatedButton.icon(
        onPressed: _addNewReading,
        icon: const Icon(Icons.add),
        label: const Text('إضافة قراءة'),
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget(String error) {
    return AdaptiveEmptyState(
      icon: Icons.error_outline,
      title: 'حدث خطأ في تحميل البيانات',
      subtitle: error,
      action: ElevatedButton(
        onPressed: () => ref.refresh(readingsProvider),
        child: const Text('إعادة المحاولة'),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewReading,
      tooltip: 'إضافة قراءة جديدة',
      child: const Icon(Icons.add),
    );
  }

  /// تغيير نص البحث
  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });
  }

  /// مسح البحث
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  /// إضافة قراءة جديدة
  void _addNewReading() {
    showDialog(
      context: context,
      builder: (context) => const AddReadingDialog(),
    ).then((result) {
      if (result == true) {
        ref.refresh(readingsProvider);
        _showSuccessMessage('تم إضافة القراءة بنجاح');
      }
    });
  }

  /// تعديل قراءة
  void _editReading(ReadingEntity reading) {
    showDialog(
      context: context,
      builder: (context) => AddReadingDialog(reading: reading),
    ).then((result) {
      if (result == true) {
        ref.refresh(readingsProvider);
        _showSuccessMessage('تم تحديث القراءة بنجاح');
      }
    });
  }

  /// حذف قراءة
  void _deleteReading(ReadingEntity reading) {
    AdaptiveDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      content: Text('هل أنت متأكد من حذف القراءة للعداد "${reading.meterNumber}"؟'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            await ref.read(readingFormProvider.notifier)
                .deleteReading(reading.id);
            
            final state = ref.read(readingFormProvider);
            if (state.isSuccess) {
              ref.refresh(readingsProvider);
              _showSuccessMessage('تم حذف القراءة بنجاح');
            } else if (state.error != null) {
              _showErrorMessage(state.error!);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('حذف'),
        ),
      ],
    );
  }

  /// تأكيد قراءة
  void _confirmReading(ReadingEntity reading) async {
    await ref.read(readingFormProvider.notifier)
        .confirmReading(reading.id);
    
    final state = ref.read(readingFormProvider);
    if (state.isSuccess) {
      ref.refresh(readingsProvider);
      _showSuccessMessage('تم تأكيد القراءة بنجاح');
    } else if (state.error != null) {
      _showErrorMessage(state.error!);
    }
  }

  /// الانتقال لتفاصيل القراءة
  void _navigateToReadingDetails(ReadingEntity reading) {
    AdaptiveDialog.show(
      context: context,
      title: 'تفاصيل القراءة',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow('العداد', reading.meterNumber),
          _buildDetailRow('المشترك', reading.subscriberName),
          _buildDetailRow('القرية', reading.villageName),
          _buildDetailRow('القراءة السابقة', reading.previousReading.toString()),
          _buildDetailRow('القراءة الحالية', reading.currentReading.toString()),
          _buildDetailRow('الاستهلاك', reading.consumption.toString()),
          _buildDetailRow('تاريخ القراءة', reading.readingDate.toString().substring(0, 10)),
          _buildDetailRow('النوع', reading.typeText),
          _buildDetailRow('الحالة', reading.statusText),
          if (reading.readerName != null) _buildDetailRow('القارئ', reading.readerName!),
          if (reading.notes != null) _buildDetailRow('ملاحظات', reading.notes!),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        if (reading.canBeEdited)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _editReading(reading);
            },
            child: const Text('تعديل'),
          ),
      ],
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
