^J:\PROJECTS3\WATER\WATER_MANAGEMENT_APP\BUILD\WINDOWS\X64\PDFIUM-DOWNLOAD\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SJ:/projects3/water/water_management_app/build/windows/x64/pdfium-download -BJ:/projects3/water/water_management_app/build/windows/x64/pdfium-download --check-stamp-file J:/projects3/water/water_management_app/build/windows/x64/pdfium-download/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
