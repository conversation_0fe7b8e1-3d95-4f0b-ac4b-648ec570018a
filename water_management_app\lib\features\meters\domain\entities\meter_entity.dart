import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// أنواع العدادات
enum MeterType {
  mechanical,
  digital,
  smart,
}

/// حالات العداد
enum MeterStatus {
  active,
  inactive,
  maintenance,
  damaged,
  replaced,
}

/// كيان العداد
class MeterEntity extends Equatable {
  final int id;
  final String meterNumber;
  final int subscriberId;
  final String subscriberName;
  final String villageName;
  final MeterType type;
  final MeterStatus status;
  final String? brand;
  final String? model;
  final double? diameter;
  final double initialReading;
  final double currentReading;
  final double? maxCapacity;
  final DateTime installationDate;
  final DateTime? lastMaintenanceDate;
  final DateTime? nextMaintenanceDate;
  final String? location;
  final String? serialNumber;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MeterEntity({
    required this.id,
    required this.meterNumber,
    required this.subscriberId,
    required this.subscriberName,
    required this.villageName,
    required this.type,
    required this.status,
    this.brand,
    this.model,
    this.diameter,
    required this.initialReading,
    required this.currentReading,
    this.maxCapacity,
    required this.installationDate,
    this.lastMaintenanceDate,
    this.nextMaintenanceDate,
    this.location,
    this.serialNumber,
    this.notes,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        meterNumber,
        subscriberId,
        subscriberName,
        villageName,
        type,
        status,
        brand,
        model,
        diameter,
        initialReading,
        currentReading,
        maxCapacity,
        installationDate,
        lastMaintenanceDate,
        nextMaintenanceDate,
        location,
        serialNumber,
        notes,
        isActive,
        createdAt,
        updatedAt,
      ];

  /// نسخ الكيان مع تعديل بعض الخصائص
  MeterEntity copyWith({
    int? id,
    String? meterNumber,
    int? subscriberId,
    String? subscriberName,
    String? villageName,
    MeterType? type,
    MeterStatus? status,
    String? brand,
    String? model,
    double? diameter,
    double? initialReading,
    double? currentReading,
    double? maxCapacity,
    DateTime? installationDate,
    DateTime? lastMaintenanceDate,
    DateTime? nextMaintenanceDate,
    String? location,
    String? serialNumber,
    String? notes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MeterEntity(
      id: id ?? this.id,
      meterNumber: meterNumber ?? this.meterNumber,
      subscriberId: subscriberId ?? this.subscriberId,
      subscriberName: subscriberName ?? this.subscriberName,
      villageName: villageName ?? this.villageName,
      type: type ?? this.type,
      status: status ?? this.status,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      diameter: diameter ?? this.diameter,
      initialReading: initialReading ?? this.initialReading,
      currentReading: currentReading ?? this.currentReading,
      maxCapacity: maxCapacity ?? this.maxCapacity,
      installationDate: installationDate ?? this.installationDate,
      lastMaintenanceDate: lastMaintenanceDate ?? this.lastMaintenanceDate,
      nextMaintenanceDate: nextMaintenanceDate ?? this.nextMaintenanceDate,
      location: location ?? this.location,
      serialNumber: serialNumber ?? this.serialNumber,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'meter_number': meterNumber,
      'subscriber_id': subscriberId,
      'subscriber_name': subscriberName,
      'village_name': villageName,
      'type': type.name,
      'status': status.name,
      'brand': brand,
      'model': model,
      'diameter': diameter,
      'initial_reading': initialReading,
      'current_reading': currentReading,
      'max_capacity': maxCapacity,
      'installation_date': installationDate.toIso8601String(),
      'last_maintenance_date': lastMaintenanceDate?.toIso8601String(),
      'next_maintenance_date': nextMaintenanceDate?.toIso8601String(),
      'location': location,
      'serial_number': serialNumber,
      'notes': notes,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory MeterEntity.fromMap(Map<String, dynamic> map) {
    return MeterEntity(
      id: map['id'] as int,
      meterNumber: map['meter_number'] as String,
      subscriberId: map['subscriber_id'] as int,
      subscriberName: map['subscriber_name'] as String? ?? '',
      villageName: map['village_name'] as String? ?? '',
      type: MeterType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MeterType.mechanical,
      ),
      status: MeterStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => MeterStatus.active,
      ),
      brand: map['brand'] as String?,
      model: map['model'] as String?,
      diameter: (map['diameter'] as num?)?.toDouble(),
      initialReading: (map['initial_reading'] as num?)?.toDouble() ?? 0.0,
      currentReading: (map['current_reading'] as num?)?.toDouble() ?? 0.0,
      maxCapacity: (map['max_capacity'] as num?)?.toDouble(),
      installationDate: DateTime.parse(map['installation_date'] as String),
      lastMaintenanceDate: map['last_maintenance_date'] != null
          ? DateTime.parse(map['last_maintenance_date'] as String)
          : null,
      nextMaintenanceDate: map['next_maintenance_date'] != null
          ? DateTime.parse(map['next_maintenance_date'] as String)
          : null,
      location: map['location'] as String?,
      serialNumber: map['serial_number'] as String?,
      notes: map['notes'] as String?,
      isActive: map['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// الحصول على إجمالي الاستهلاك
  double get totalConsumption {
    return currentReading - initialReading;
  }

  /// الحصول على نص نوع العداد
  String get typeText {
    switch (type) {
      case MeterType.mechanical:
        return 'ميكانيكي';
      case MeterType.digital:
        return 'رقمي';
      case MeterType.smart:
        return 'ذكي';
    }
  }

  /// الحصول على نص حالة العداد
  String get statusText {
    switch (status) {
      case MeterStatus.active:
        return 'نشط';
      case MeterStatus.inactive:
        return 'غير نشط';
      case MeterStatus.maintenance:
        return 'صيانة';
      case MeterStatus.damaged:
        return 'معطل';
      case MeterStatus.replaced:
        return 'مستبدل';
    }
  }

  /// الحصول على لون الحالة
  Color get statusColor {
    switch (status) {
      case MeterStatus.active:
        return Colors.green;
      case MeterStatus.inactive:
        return Colors.grey;
      case MeterStatus.maintenance:
        return Colors.orange;
      case MeterStatus.damaged:
        return Colors.red;
      case MeterStatus.replaced:
        return Colors.blue;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get statusIcon {
    switch (status) {
      case MeterStatus.active:
        return Icons.check_circle;
      case MeterStatus.inactive:
        return Icons.pause_circle;
      case MeterStatus.maintenance:
        return Icons.build;
      case MeterStatus.damaged:
        return Icons.error;
      case MeterStatus.replaced:
        return Icons.swap_horiz;
    }
  }

  /// الحصول على أيقونة النوع
  IconData get typeIcon {
    switch (type) {
      case MeterType.mechanical:
        return Icons.speed;
      case MeterType.digital:
        return Icons.memory;
      case MeterType.smart:
        return Icons.smart_toy;
    }
  }

  /// التحقق من الحاجة للصيانة
  bool get needsMaintenance {
    if (nextMaintenanceDate == null) return false;
    return DateTime.now().isAfter(nextMaintenanceDate!);
  }

  /// الحصول على عدد الأيام حتى الصيانة التالية
  int? get daysUntilMaintenance {
    if (nextMaintenanceDate == null) return null;
    return nextMaintenanceDate!.difference(DateTime.now()).inDays;
  }

  /// الحصول على معلومات العداد
  String get meterInfo {
    final parts = <String>[];
    if (brand != null) parts.add(brand!);
    if (model != null) parts.add(model!);
    if (diameter != null) parts.add('${diameter}mm');
    return parts.isEmpty ? 'غير محدد' : parts.join(' - ');
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return meterNumber.isNotEmpty &&
        subscriberId > 0 &&
        currentReading >= initialReading;
  }

  /// الحصول على ملخص العداد
  String get summary {
    return '$meterNumber - $subscriberName ($villageName)';
  }

  /// تحديث القراءة الحالية
  MeterEntity updateCurrentReading(double newReading) {
    if (newReading < currentReading) {
      throw Exception('القراءة الجديدة لا يمكن أن تكون أقل من القراءة الحالية');
    }
    
    return copyWith(
      currentReading: newReading,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث حالة العداد
  MeterEntity updateStatus(MeterStatus newStatus) {
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث تاريخ الصيانة
  MeterEntity updateMaintenanceDate({
    DateTime? lastMaintenanceDate,
    DateTime? nextMaintenanceDate,
  }) {
    return copyWith(
      lastMaintenanceDate: lastMaintenanceDate ?? this.lastMaintenanceDate,
      nextMaintenanceDate: nextMaintenanceDate ?? this.nextMaintenanceDate,
      updatedAt: DateTime.now(),
    );
  }

  /// تفعيل/إلغاء تفعيل العداد
  MeterEntity toggleActive() {
    return copyWith(
      isActive: !isActive,
      status: isActive ? MeterStatus.inactive : MeterStatus.active,
      updatedAt: DateTime.now(),
    );
  }
}
