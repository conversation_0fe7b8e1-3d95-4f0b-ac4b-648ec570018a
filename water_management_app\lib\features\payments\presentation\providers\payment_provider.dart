import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/payment_repository.dart';
import '../../domain/entities/payment_entity.dart';

/// مزود مستودع المدفوعات
final paymentRepositoryProvider = Provider<PaymentRepository>((ref) {
  return PaymentRepository();
});

/// مزود قائمة المدفوعات
final paymentsProvider = FutureProvider<List<PaymentEntity>>((ref) async {
  final repository = ref.read(paymentRepositoryProvider);
  return await repository.getAllPayments();
});

/// مزود البحث في المدفوعات
final paymentSearchProvider = FutureProvider.family<List<PaymentEntity>, String>((ref, query) async {
  final repository = ref.read(paymentRepositoryProvider);
  return await repository.searchPayments(query);
});

/// مزود الدفعة الواحدة
final paymentProvider = FutureProvider.family<PaymentEntity?, int>((ref, id) async {
  final repository = ref.read(paymentRepositoryProvider);
  return await repository.getPaymentById(id);
});

/// مزود المدفوعات حسب الفاتورة
final paymentsByBillProvider = FutureProvider.family<List<PaymentEntity>, int>((ref, billId) async {
  final repository = ref.read(paymentRepositoryProvider);
  return await repository.getPaymentsByBill(billId);
});

/// مزود المدفوعات حسب المشترك
final paymentsBySubscriberProvider = FutureProvider.family<List<PaymentEntity>, int>((ref, subscriberId) async {
  final repository = ref.read(paymentRepositoryProvider);
  return await repository.getPaymentsBySubscriber(subscriberId);
});

/// مزود المدفوعات المعلقة
final pendingPaymentsProvider = FutureProvider<List<PaymentEntity>>((ref) async {
  final repository = ref.read(paymentRepositoryProvider);
  return await repository.getPendingPayments();
});

/// مزود إحصائيات المدفوعات
final paymentStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(paymentRepositoryProvider);
  return await repository.getPaymentsStatistics();
});

/// مزود حالة نموذج الدفعة
final paymentFormProvider = StateNotifierProvider<PaymentFormNotifier, PaymentFormState>((ref) {
  final repository = ref.read(paymentRepositoryProvider);
  return PaymentFormNotifier(repository);
});

/// حالة نموذج الدفعة
class PaymentFormState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final PaymentEntity? payment;

  const PaymentFormState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
    this.payment,
  });

  factory PaymentFormState.initial() {
    return const PaymentFormState(
      isLoading: false,
      isSuccess: false,
      error: null,
      payment: null,
    );
  }

  PaymentFormState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    PaymentEntity? payment,
  }) {
    return PaymentFormState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      payment: payment ?? this.payment,
    );
  }
}

/// مدير حالة نموذج الدفعة
class PaymentFormNotifier extends StateNotifier<PaymentFormState> {
  final PaymentRepository _repository;

  PaymentFormNotifier(this._repository) : super(PaymentFormState.initial());

  /// حفظ دفعة (إضافة أو تحديث)
  Future<void> savePayment(PaymentEntity payment) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      PaymentEntity savedPayment;
      
      if (payment.id == 0) {
        // إضافة دفعة جديدة
        savedPayment = await _repository.createPayment(payment);
      } else {
        // تحديث دفعة موجودة
        savedPayment = await _repository.updatePayment(payment);
      }
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        payment: savedPayment,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// حذف دفعة
  Future<void> deletePayment(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deletePayment(id);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// تحديث حالة الدفعة
  Future<void> updatePaymentStatus(int paymentId, PaymentStatus newStatus) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final updatedPayment = await _repository.updatePaymentStatus(paymentId, newStatus);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        payment: updatedPayment,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// إنشاء رقم دفعة تلقائي
  Future<String> generatePaymentNumber() async {
    try {
      return await _repository.generatePaymentNumber();
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم الدفعة: $e');
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = PaymentFormState.initial();
  }

  /// تحديث الدفعة في الحالة
  void updatePayment(PaymentEntity payment) {
    state = state.copyWith(payment: payment);
  }

  /// تحديث حالة التحميل
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// تحديث حالة النجاح
  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }
}

/// مزود حالة البحث في المدفوعات
final paymentSearchStateProvider = StateNotifierProvider<PaymentSearchNotifier, PaymentSearchState>((ref) {
  return PaymentSearchNotifier();
});

/// حالة البحث في المدفوعات
class PaymentSearchState {
  final String query;
  final bool isSearching;
  final List<PaymentEntity> results;
  final String? error;

  const PaymentSearchState({
    required this.query,
    required this.isSearching,
    required this.results,
    this.error,
  });

  factory PaymentSearchState.initial() {
    return const PaymentSearchState(
      query: '',
      isSearching: false,
      results: [],
      error: null,
    );
  }

  PaymentSearchState copyWith({
    String? query,
    bool? isSearching,
    List<PaymentEntity>? results,
    String? error,
  }) {
    return PaymentSearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      results: results ?? this.results,
      error: error,
    );
  }
}

/// مدير حالة البحث في المدفوعات
class PaymentSearchNotifier extends StateNotifier<PaymentSearchState> {
  PaymentSearchNotifier() : super(PaymentSearchState.initial());

  /// تحديث نص البحث
  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  /// تحديث نتائج البحث
  void updateResults(List<PaymentEntity> results) {
    state = state.copyWith(results: results, isSearching: false);
  }

  /// تحديث حالة البحث
  void setSearching(bool isSearching) {
    state = state.copyWith(isSearching: isSearching);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error, isSearching: false);
  }

  /// مسح البحث
  void clearSearch() {
    state = PaymentSearchState.initial();
  }
}

/// مزود حالة التصفية للمدفوعات
final paymentFilterProvider = StateNotifierProvider<PaymentFilterNotifier, PaymentFilterState>((ref) {
  return PaymentFilterNotifier();
});

/// حالة التصفية للمدفوعات
class PaymentFilterState {
  final PaymentStatus? statusFilter;
  final PaymentMethod? methodFilter;
  final PaymentType? typeFilter;
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minAmount;
  final double? maxAmount;

  const PaymentFilterState({
    this.statusFilter,
    this.methodFilter,
    this.typeFilter,
    this.startDate,
    this.endDate,
    this.minAmount,
    this.maxAmount,
  });

  factory PaymentFilterState.initial() {
    return const PaymentFilterState(
      statusFilter: null,
      methodFilter: null,
      typeFilter: null,
      startDate: null,
      endDate: null,
      minAmount: null,
      maxAmount: null,
    );
  }

  PaymentFilterState copyWith({
    PaymentStatus? statusFilter,
    PaymentMethod? methodFilter,
    PaymentType? typeFilter,
    DateTime? startDate,
    DateTime? endDate,
    double? minAmount,
    double? maxAmount,
  }) {
    return PaymentFilterState(
      statusFilter: statusFilter ?? this.statusFilter,
      methodFilter: methodFilter ?? this.methodFilter,
      typeFilter: typeFilter ?? this.typeFilter,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      minAmount: minAmount ?? this.minAmount,
      maxAmount: maxAmount ?? this.maxAmount,
    );
  }

  /// التحقق من وجود مرشحات نشطة
  bool get hasActiveFilters {
    return statusFilter != null ||
        methodFilter != null ||
        typeFilter != null ||
        startDate != null ||
        endDate != null ||
        minAmount != null ||
        maxAmount != null;
  }
}

/// مدير حالة التصفية للمدفوعات
class PaymentFilterNotifier extends StateNotifier<PaymentFilterState> {
  PaymentFilterNotifier() : super(PaymentFilterState.initial());

  /// تحديث مرشح الحالة
  void updateStatusFilter(PaymentStatus? status) {
    state = state.copyWith(statusFilter: status);
  }

  /// تحديث مرشح طريقة الدفع
  void updateMethodFilter(PaymentMethod? method) {
    state = state.copyWith(methodFilter: method);
  }

  /// تحديث مرشح نوع الدفع
  void updateTypeFilter(PaymentType? type) {
    state = state.copyWith(typeFilter: type);
  }

  /// تحديث تاريخ البداية
  void updateStartDate(DateTime? date) {
    state = state.copyWith(startDate: date);
  }

  /// تحديث تاريخ النهاية
  void updateEndDate(DateTime? date) {
    state = state.copyWith(endDate: date);
  }

  /// تحديث الحد الأدنى للمبلغ
  void updateMinAmount(double? amount) {
    state = state.copyWith(minAmount: amount);
  }

  /// تحديث الحد الأقصى للمبلغ
  void updateMaxAmount(double? amount) {
    state = state.copyWith(maxAmount: amount);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    state = PaymentFilterState.initial();
  }
}
