import 'package:drift/drift.dart';
import '../database/app_database.dart';
import 'database_service.dart';

/// خدمة إضافة البيانات التجريبية
class SampleDataService {
  static final AppDatabase _database = DatabaseService.instance.database;

  /// إضافة بيانات تجريبية للقرى
  static Future<void> addSampleVillages() async {
    try {
      // التحقق من وجود قرى
      final existingVillages = await _database.select(_database.villages).get();
      if (existingVillages.isNotEmpty) {
        print('القرى موجودة بالفعل');
        return;
      }

      // إضافة قرى تجريبية
      final villages = [
        VillagesCompanion.insert(
          name: 'القرية الأولى',
          code: 'V001',
          description: Value('قرية تجريبية للاختبار - شمال المدينة'),
          population: Value(1500),
          isActive: Value(true),
        ),
        VillagesCompanion.insert(
          name: 'القرية الثانية',
          code: 'V002',
          description: Value('قرية تجريبية للاختبار - جنوب المدينة'),
          population: Value(2200),
          isActive: Value(true),
        ),
        VillagesCompanion.insert(
          name: 'القرية الثالثة',
          code: 'V003',
          description: Value('قرية تجريبية للاختبار - شرق المدينة'),
          population: Value(1800),
          isActive: Value(true),
        ),
      ];

      for (final village in villages) {
        await _database.into(_database.villages).insert(village);
      }

      print('تم إضافة ${villages.length} قرى تجريبية');
    } catch (e) {
      print('خطأ في إضافة القرى التجريبية: $e');
    }
  }

  /// إضافة بيانات تجريبية للمشتركين
  static Future<void> addSampleSubscribers() async {
    try {
      // التحقق من وجود مشتركين
      final existingSubscribers = await _database.select(_database.subscribers).get();
      if (existingSubscribers.isNotEmpty) {
        print('المشتركون موجودون بالفعل');
        return;
      }

      // الحصول على القرى
      final villages = await _database.select(_database.villages).get();
      if (villages.isEmpty) {
        await addSampleVillages();
        final newVillages = await _database.select(_database.villages).get();
        if (newVillages.isEmpty) return;
      }

      final villagesList = await _database.select(_database.villages).get();

      // إضافة مشتركين تجريبيين
      final subscribers = [
        SubscribersCompanion.insert(
          subscriberNumber: 'SUB001',
          fullName: 'أحمد محمد علي',
          villageId: villagesList[0].id,
          subscriptionType: 'residential',
          phone: Value('0501234567'),
          email: Value('<EMAIL>'),
          address: Value('شارع الملك فهد، حي النور'),
          nationalId: Value('1234567890'),
        ),
        SubscribersCompanion.insert(
          subscriberNumber: 'SUB002',
          fullName: 'فاطمة عبدالله',
          villageId: villagesList[1].id,
          subscriptionType: 'residential',
          phone: Value('0507654321'),
          email: Value('<EMAIL>'),
          address: Value('شارع العروبة، حي السلام'),
          nationalId: Value('0987654321'),
        ),
        SubscribersCompanion.insert(
          subscriberNumber: 'SUB003',
          fullName: 'محمد سعد الدين',
          villageId: villagesList[2].id,
          subscriptionType: 'commercial',
          phone: Value('0551122334'),
          email: Value('<EMAIL>'),
          address: Value('شارع التجارة، المنطقة الصناعية'),
          nationalId: Value('1122334455'),
        ),
      ];

      for (final subscriber in subscribers) {
        await _database.into(_database.subscribers).insert(subscriber);
      }

      print('تم إضافة ${subscribers.length} مشتركين تجريبيين');
    } catch (e) {
      print('خطأ في إضافة المشتركين التجريبيين: $e');
    }
  }

  /// إضافة بيانات تجريبية للعدادات
  static Future<void> addSampleMeters() async {
    try {
      // التحقق من وجود عدادات
      final existingMeters = await _database.select(_database.meters).get();
      if (existingMeters.isNotEmpty) {
        print('العدادات موجودة بالفعل');
        return;
      }

      // الحصول على المشتركين
      final subscribers = await _database.select(_database.subscribers).get();
      if (subscribers.isEmpty) {
        await addSampleSubscribers();
        final newSubscribers = await _database.select(_database.subscribers).get();
        if (newSubscribers.isEmpty) return;
      }

      final subscribersList = await _database.select(_database.subscribers).get();

      // إضافة عدادات تجريبية
      final meters = [
        MetersCompanion.insert(
          serialNumber: 'M001-2024',
          subscriberId: subscribersList[0].id,
          meterType: Value('mechanical'),
          diameter: Value('20'),
          installationDate: Value(DateTime.now().subtract(const Duration(days: 365))),
          currentReading: Value(1250.5),
          status: Value('active'),
          brand: Value('شركة العدادات المتقدمة'),
          model: Value('MEC-2024'),
        ),
        MetersCompanion.insert(
          serialNumber: 'M002-2024',
          subscriberId: subscribersList[1].id,
          meterType: Value('digital'),
          diameter: Value('25'),
          installationDate: Value(DateTime.now().subtract(const Duration(days: 300))),
          currentReading: Value(890.2),
          status: Value('active'),
          brand: Value('شركة العدادات الرقمية'),
          model: Value('DIG-2024'),
        ),
        MetersCompanion.insert(
          serialNumber: 'M003-2024',
          subscriberId: subscribersList[2].id,
          meterType: Value('smart'),
          diameter: Value('50'),
          installationDate: Value(DateTime.now().subtract(const Duration(days: 180))),
          currentReading: Value(2150.8),
          status: Value('active'),
          brand: Value('شركة العدادات الذكية'),
          model: Value('SMT-2024'),
        ),
      ];

      for (final meter in meters) {
        await _database.into(_database.meters).insert(meter);
      }

      print('تم إضافة ${meters.length} عدادات تجريبية');
    } catch (e) {
      print('خطأ في إضافة العدادات التجريبية: $e');
    }
  }

  /// إضافة بيانات تجريبية للقراءات
  static Future<void> addSampleReadings() async {
    try {
      // التحقق من وجود قراءات
      final existingReadings = await _database.select(_database.readings).get();
      if (existingReadings.isNotEmpty) {
        print('القراءات موجودة بالفعل');
        return;
      }

      // الحصول على العدادات
      final meters = await _database.select(_database.meters).get();
      if (meters.isEmpty) {
        await addSampleMeters();
        final newMeters = await _database.select(_database.meters).get();
        if (newMeters.isEmpty) return;
      }

      final metersList = await _database.select(_database.meters).get();

      // إضافة قراءات تجريبية
      final readings = [
        ReadingsCompanion.insert(
          meterId: metersList[0].id,
          currentReading: 1350.5,
          previousReading: 1250.5,
          consumption: 100.0,
          readingDate: DateTime.now().subtract(const Duration(days: 30)),
          readingPeriod: '2024-11',
          readingMethod: 'manual',
          isValidated: Value(true),
          isEstimated: Value(false),
        ),
        ReadingsCompanion.insert(
          meterId: metersList[1].id,
          currentReading: 950.2,
          previousReading: 890.2,
          consumption: 60.0,
          readingDate: DateTime.now().subtract(const Duration(days: 30)),
          readingPeriod: '2024-11',
          readingMethod: 'digital',
          isValidated: Value(true),
          isEstimated: Value(false),
        ),
        ReadingsCompanion.insert(
          meterId: metersList[2].id,
          currentReading: 2280.8,
          previousReading: 2150.8,
          consumption: 130.0,
          readingDate: DateTime.now().subtract(const Duration(days: 30)),
          readingPeriod: '2024-11',
          readingMethod: 'smart',
          isValidated: Value(true),
          isEstimated: Value(false),
        ),
      ];

      for (final reading in readings) {
        await _database.into(_database.readings).insert(reading);
      }

      print('تم إضافة ${readings.length} قراءات تجريبية');
    } catch (e) {
      print('خطأ في إضافة القراءات التجريبية: $e');
    }
  }

  /// إضافة جميع البيانات التجريبية
  static Future<void> addAllSampleData() async {
    print('بدء إضافة البيانات التجريبية...');
    
    await addSampleVillages();
    await addSampleSubscribers();
    await addSampleMeters();
    await addSampleReadings();
    
    print('تم الانتهاء من إضافة البيانات التجريبية');
  }

  /// حذف جميع البيانات التجريبية
  static Future<void> clearAllData() async {
    try {
      await _database.delete(_database.readings).go();
      await _database.delete(_database.meters).go();
      await _database.delete(_database.subscribers).go();
      await _database.delete(_database.villages).go();
      
      print('تم حذف جميع البيانات التجريبية');
    } catch (e) {
      print('خطأ في حذف البيانات: $e');
    }
  }
}
