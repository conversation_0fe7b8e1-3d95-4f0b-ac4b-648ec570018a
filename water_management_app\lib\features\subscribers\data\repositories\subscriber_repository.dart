import 'package:drift/drift.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/services/database_service.dart';
import '../../domain/entities/subscriber_entity.dart';

/// مستودع المشتركين
class SubscriberRepository {
  final AppDatabase _database = DatabaseService.instance.database;

  /// الحصول على جميع المشتركين
  Future<List<SubscriberEntity>> getAllSubscribers() async {
    try {
      final query = _database.select(_database.subscribers).join([
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ]);

      final results = await query.get();
      
      return results.map((row) {
        final subscriber = row.readTable(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(subscriber, village?.name ?? 'غير محدد');
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على المشتركين: $e');
    }
  }

  /// الحصول على مشترك بالمعرف
  Future<SubscriberEntity?> getSubscriberById(int id) async {
    try {
      final query = _database.select(_database.subscribers).join([
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.subscribers.id.equals(id));

      final result = await query.getSingleOrNull();
      
      if (result == null) return null;
      
      final subscriber = result.readTable(_database.subscribers);
      final village = result.readTableOrNull(_database.villages);
      
      return _mapToEntity(subscriber, village?.name ?? 'غير محدد');
    } catch (e) {
      throw Exception('خطأ في الحصول على المشترك: $e');
    }
  }

  /// البحث في المشتركين
  Future<List<SubscriberEntity>> searchSubscribers(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllSubscribers();
      }

      final searchQuery = _database.select(_database.subscribers).join([
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(
        _database.subscribers.fullName.like('%$query%') |
        _database.subscribers.subscriberNumber.like('%$query%') |
        _database.subscribers.phone.like('%$query%') |
        _database.subscribers.email.like('%$query%')
      );

      final results = await searchQuery.get();
      
      return results.map((row) {
        final subscriber = row.readTable(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(subscriber, village?.name ?? 'غير محدد');
      }).toList();
    } catch (e) {
      throw Exception('خطأ في البحث عن المشتركين: $e');
    }
  }

  /// الحصول على المشتركين حسب القرية
  Future<List<SubscriberEntity>> getSubscribersByVillage(int villageId) async {
    try {
      final query = _database.select(_database.subscribers).join([
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.subscribers.villageId.equals(villageId));

      final results = await query.get();
      
      return results.map((row) {
        final subscriber = row.readTable(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(subscriber, village?.name ?? 'غير محدد');
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على مشتركي القرية: $e');
    }
  }

  /// إضافة مشترك جديد
  Future<SubscriberEntity> createSubscriber(SubscriberEntity subscriber) async {
    try {
      // التحقق من عدم تكرار رقم المشترك
      final existingSubscriber = await (_database.select(_database.subscribers)
        ..where((s) => s.subscriberNumber.equals(subscriber.subscriberNumber)))
        .getSingleOrNull();

      if (existingSubscriber != null) {
        throw Exception('رقم المشترك موجود مسبقاً');
      }

      final id = await _database.into(_database.subscribers).insert(
        SubscribersCompanion.insert(
          subscriberNumber: subscriber.subscriberNumber,
          fullName: subscriber.fullName,
          villageId: subscriber.villageId,
          subscriptionType: subscriber.subscriptionType.name,
          status: Value(subscriber.status.name),
          phone: Value(subscriber.phone),
          email: Value(subscriber.email),
          address: Value(subscriber.address),
          nationalId: Value(subscriber.nationalId),
          connectionDate: Value(subscriber.connectionDate),
          disconnectionDate: Value(subscriber.disconnectionDate),
          notes: Value(subscriber.notes),
          discountPercentage: Value(subscriber.discountPercentage),
          isCharitable: Value(subscriber.isCharitable),
        ),
      );

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'subscribers',
        recordId: id,
        operation: 'INSERT',
        newValues: subscriber.toMap(),
      );

      return subscriber.copyWith(id: id);
    } catch (e) {
      throw Exception('خطأ في إضافة المشترك: $e');
    }
  }

  /// تحديث مشترك
  Future<SubscriberEntity> updateSubscriber(SubscriberEntity subscriber) async {
    try {
      // الحصول على البيانات القديمة
      final oldSubscriber = await getSubscriberById(subscriber.id);
      
      await (_database.update(_database.subscribers)
        ..where((s) => s.id.equals(subscriber.id)))
        .write(SubscribersCompanion(
          subscriberNumber: Value(subscriber.subscriberNumber),
          fullName: Value(subscriber.fullName),
          villageId: Value(subscriber.villageId),
          subscriptionType: Value(subscriber.subscriptionType.name),
          status: Value(subscriber.status.name),
          phone: Value(subscriber.phone),
          email: Value(subscriber.email),
          address: Value(subscriber.address),
          nationalId: Value(subscriber.nationalId),
          connectionDate: Value(subscriber.connectionDate),
          disconnectionDate: Value(subscriber.disconnectionDate),
          notes: Value(subscriber.notes),
          discountPercentage: Value(subscriber.discountPercentage),
          isCharitable: Value(subscriber.isCharitable),
          updatedAt: Value(DateTime.now()),
        ));

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'subscribers',
        recordId: subscriber.id,
        operation: 'UPDATE',
        oldValues: oldSubscriber?.toMap(),
        newValues: subscriber.toMap(),
      );

      return subscriber.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      throw Exception('خطأ في تحديث المشترك: $e');
    }
  }

  /// حذف مشترك
  Future<void> deleteSubscriber(int id) async {
    try {
      // التحقق من وجود عدادات مرتبطة
      final metersCount = await (_database.select(_database.meters)
        ..where((m) => m.subscriberId.equals(id)))
        .get()
        .then((meters) => meters.length);

      if (metersCount > 0) {
        throw Exception('لا يمكن حذف المشترك لوجود عدادات مرتبطة به');
      }

      // الحصول على البيانات قبل الحذف
      final subscriber = await getSubscriberById(id);

      await (_database.delete(_database.subscribers)
        ..where((s) => s.id.equals(id)))
        .go();

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'subscribers',
        recordId: id,
        operation: 'DELETE',
        oldValues: subscriber?.toMap(),
      );
    } catch (e) {
      throw Exception('خطأ في حذف المشترك: $e');
    }
  }

  /// الحصول على إحصائيات المشتركين
  Future<Map<String, int>> getSubscriberStatistics() async {
    try {
      final stats = <String, int>{};
      
      // إجمالي المشتركين
      stats['total'] = await _database.select(_database.subscribers).get()
        .then((list) => list.length);
      
      // المشتركين النشطين
      stats['active'] = await (_database.select(_database.subscribers)
        ..where((s) => s.status.equals('active'))).get()
        .then((list) => list.length);
      
      // المشتركين المعلقين
      stats['suspended'] = await (_database.select(_database.subscribers)
        ..where((s) => s.status.equals('suspended'))).get()
        .then((list) => list.length);
      
      // المشتركين المتأخرين
      stats['overdue'] = await (_database.select(_database.subscribers)
        ..where((s) => s.status.equals('overdue'))).get()
        .then((list) => list.length);
      
      // المشتركين المقطوعين
      stats['disconnected'] = await (_database.select(_database.subscribers)
        ..where((s) => s.status.equals('disconnected'))).get()
        .then((list) => list.length);

      return stats;
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات المشتركين: $e');
    }
  }

  /// تحويل من نموذج قاعدة البيانات إلى كيان
  SubscriberEntity _mapToEntity(Subscriber subscriber, String villageName) {
    return SubscriberEntity(
      id: subscriber.id,
      subscriberNumber: subscriber.subscriberNumber,
      fullName: subscriber.fullName,
      villageId: subscriber.villageId,
      villageName: villageName,
      subscriptionType: SubscriptionType.values.firstWhere(
        (e) => e.name == subscriber.subscriptionType,
        orElse: () => SubscriptionType.residential,
      ),
      status: SubscriptionStatus.values.firstWhere(
        (e) => e.name == subscriber.status,
        orElse: () => SubscriptionStatus.active,
      ),
      phone: subscriber.phone,
      email: subscriber.email,
      address: subscriber.address,
      nationalId: subscriber.nationalId,
      connectionDate: subscriber.connectionDate,
      disconnectionDate: subscriber.disconnectionDate,
      notes: subscriber.notes,
      discountPercentage: subscriber.discountPercentage,
      isCharitable: subscriber.isCharitable,
      createdAt: subscriber.createdAt,
      updatedAt: subscriber.updatedAt,
    );
  }

  /// إنشاء رقم مشترك تلقائي
  Future<String> generateSubscriberNumber() async {
    try {
      final lastSubscriber = await (_database.select(_database.subscribers)
        ..orderBy([(s) => OrderingTerm.desc(s.id)]))
        .getSingleOrNull();

      if (lastSubscriber == null) {
        return 'SUB001';
      }

      // استخراج الرقم من آخر مشترك وزيادته
      final lastNumber = lastSubscriber.subscriberNumber;
      final numberPart = lastNumber.replaceAll(RegExp(r'[^0-9]'), '');
      final nextNumber = (int.tryParse(numberPart) ?? 0) + 1;
      
      return 'SUB${nextNumber.toString().padLeft(3, '0')}';
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم المشترك: $e');
    }
  }
}
