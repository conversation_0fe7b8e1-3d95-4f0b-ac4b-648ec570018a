{"valid_import": true, "imports": [{"uri": "package:crypto/crypto.dart", "transitive": false}, {"uri": "package:encrypt/encrypt.dart", "transitive": false}, {"uri": "package:shared_preferences/shared_preferences.dart", "transitive": false}, {"uri": "package:drift/drift.dart", "transitive": false}, {"uri": "package:logger/logger.dart", "transitive": false}, {"uri": "package:water_management_app/core/database/app_database.dart", "transitive": false}, {"uri": "package:water_management_app/core/constants/app_constants.dart", "transitive": false}, {"uri": "package:water_management_app/core/services/database_service.dart", "transitive": false}], "elements": []}