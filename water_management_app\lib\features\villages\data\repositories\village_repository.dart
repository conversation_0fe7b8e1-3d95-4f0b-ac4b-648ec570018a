import 'package:drift/drift.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/services/database_service.dart';
import '../../domain/entities/village_entity.dart';

/// مستودع القرى
class VillageRepository {
  final AppDatabase _database = DatabaseService.instance.database;

  /// الحصول على جميع القرى
  Future<List<VillageEntity>> getAllVillages() async {
    try {
      final villages = await _database.select(_database.villages).get();
      
      List<VillageEntity> villageEntities = [];
      
      for (final village in villages) {
        final stats = await _getVillageStatistics(village.id);
        villageEntities.add(_mapToEntity(village, stats));
      }
      
      return villageEntities;
    } catch (e) {
      throw Exception('خطأ في الحصول على القرى: $e');
    }
  }

  /// الحصول على قرية بالمعرف
  Future<VillageEntity?> getVillageById(int id) async {
    try {
      final village = await (_database.select(_database.villages)
        ..where((v) => v.id.equals(id)))
        .getSingleOrNull();
      
      if (village == null) return null;
      
      final stats = await _getVillageStatistics(id);
      return _mapToEntity(village, stats);
    } catch (e) {
      throw Exception('خطأ في الحصول على القرية: $e');
    }
  }

  /// البحث في القرى
  Future<List<VillageEntity>> searchVillages(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllVillages();
      }

      final villages = await (_database.select(_database.villages)
        ..where((v) => v.name.like('%$query%') |
                      v.description.like('%$query%')))
        .get();

      List<VillageEntity> villageEntities = [];
      
      for (final village in villages) {
        final stats = await _getVillageStatistics(village.id);
        villageEntities.add(_mapToEntity(village, stats));
      }
      
      return villageEntities;
    } catch (e) {
      throw Exception('خطأ في البحث عن القرى: $e');
    }
  }

  /// الحصول على القرى النشطة فقط
  Future<List<VillageEntity>> getActiveVillages() async {
    try {
      final villages = await (_database.select(_database.villages)
        ..where((v) => v.isActive.equals(true)))
        .get();

      List<VillageEntity> villageEntities = [];
      
      for (final village in villages) {
        final stats = await _getVillageStatistics(village.id);
        villageEntities.add(_mapToEntity(village, stats));
      }
      
      return villageEntities;
    } catch (e) {
      throw Exception('خطأ في الحصول على القرى النشطة: $e');
    }
  }

  /// إضافة قرية جديدة
  Future<VillageEntity> createVillage(VillageEntity village) async {
    try {
      // التحقق من عدم تكرار اسم القرية
      final existingVillage = await (_database.select(_database.villages)
        ..where((v) => v.name.equals(village.name)))
        .getSingleOrNull();

      if (existingVillage != null) {
        throw Exception('اسم القرية موجود مسبقاً');
      }

      final id = await _database.into(_database.villages).insert(
        VillagesCompanion.insert(
          name: village.name,
          code: village.name.replaceAll(' ', '_').toLowerCase(),
          description: Value(village.description),
          latitude: Value(village.latitude),
          longitude: Value(village.longitude),
          isActive: Value(village.isActive),
        ),
      );

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'villages',
        recordId: id,
        operation: 'INSERT',
        newValues: village.toMap(),
      );

      return village.copyWith(id: id);
    } catch (e) {
      throw Exception('خطأ في إضافة القرية: $e');
    }
  }

  /// تحديث قرية
  Future<VillageEntity> updateVillage(VillageEntity village) async {
    try {
      // الحصول على البيانات القديمة
      final oldVillage = await getVillageById(village.id);
      
      await (_database.update(_database.villages)
        ..where((v) => v.id.equals(village.id)))
        .write(VillagesCompanion(
          name: Value(village.name),
          description: Value(village.description),
          latitude: Value(village.latitude),
          longitude: Value(village.longitude),
          isActive: Value(village.isActive),
          updatedAt: Value(DateTime.now()),
        ));

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'villages',
        recordId: village.id,
        operation: 'UPDATE',
        oldValues: oldVillage?.toMap(),
        newValues: village.toMap(),
      );

      return village.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      throw Exception('خطأ في تحديث القرية: $e');
    }
  }

  /// حذف قرية
  Future<void> deleteVillage(int id) async {
    try {
      // التحقق من وجود مشتركين مرتبطين
      final subscribersCount = await (_database.select(_database.subscribers)
        ..where((s) => s.villageId.equals(id)))
        .get()
        .then((subscribers) => subscribers.length);

      if (subscribersCount > 0) {
        throw Exception('لا يمكن حذف القرية لوجود مشتركين مرتبطين بها');
      }

      // الحصول على البيانات قبل الحذف
      final village = await getVillageById(id);

      await (_database.delete(_database.villages)
        ..where((v) => v.id.equals(id)))
        .go();

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'villages',
        recordId: id,
        operation: 'DELETE',
        oldValues: village?.toMap(),
      );
    } catch (e) {
      throw Exception('خطأ في حذف القرية: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل قرية
  Future<VillageEntity> toggleVillageStatus(int id) async {
    try {
      final village = await getVillageById(id);
      if (village == null) {
        throw Exception('القرية غير موجودة');
      }

      final updatedVillage = village.toggleActive();
      return await updateVillage(updatedVillage);
    } catch (e) {
      throw Exception('خطأ في تغيير حالة القرية: $e');
    }
  }

  /// الحصول على إحصائيات القرى
  Future<Map<String, dynamic>> getVillagesStatistics() async {
    try {
      final stats = <String, dynamic>{};
      
      // إجمالي القرى
      stats['total_villages'] = await _database.select(_database.villages).get()
        .then((list) => list.length);
      
      // القرى النشطة
      stats['active_villages'] = await (_database.select(_database.villages)
        ..where((v) => v.isActive.equals(true))).get()
        .then((list) => list.length);
      
      // القرى غير النشطة
      stats['inactive_villages'] = await (_database.select(_database.villages)
        ..where((v) => v.isActive.equals(false))).get()
        .then((list) => list.length);

      // إجمالي المشتركين في جميع القرى
      stats['total_subscribers_all_villages'] = await _database.select(_database.subscribers).get()
        .then((list) => list.length);

      // إجمالي العدادات في جميع القرى
      stats['total_meters_all_villages'] = await _database.select(_database.meters).get()
        .then((list) => list.length);

      return stats;
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات القرى: $e');
    }
  }

  /// الحصول على إحصائيات قرية محددة
  Future<Map<String, int>> _getVillageStatistics(int villageId) async {
    try {
      final stats = <String, int>{};
      
      // إجمالي المشتركين
      stats['total_subscribers'] = await (_database.select(_database.subscribers)
        ..where((s) => s.villageId.equals(villageId))).get()
        .then((list) => list.length);
      
      // المشتركين النشطين
      stats['active_subscribers'] = await (_database.select(_database.subscribers)
        ..where((s) => s.villageId.equals(villageId) & s.status.equals('active'))).get()
        .then((list) => list.length);
      
      // إجمالي العدادات
      final subscriberIds = await (_database.select(_database.subscribers)
        ..where((s) => s.villageId.equals(villageId)))
        .get()
        .then((list) => list.map((s) => s.id).toList());
      
      if (subscriberIds.isNotEmpty) {
        stats['total_meters'] = await (_database.select(_database.meters)
          ..where((m) => m.subscriberId.isIn(subscriberIds))).get()
          .then((list) => list.length);
      } else {
        stats['total_meters'] = 0;
      }

      return stats;
    } catch (e) {
      return {
        'total_subscribers': 0,
        'active_subscribers': 0,
        'total_meters': 0,
      };
    }
  }

  /// تحويل من نموذج قاعدة البيانات إلى كيان
  VillageEntity _mapToEntity(Village village, Map<String, int> stats) {
    return VillageEntity(
      id: village.id,
      name: village.name,
      description: village.description,
      location: null, // غير متوفر في الجدول الحالي
      latitude: village.latitude,
      longitude: village.longitude,
      totalSubscribers: stats['total_subscribers'] ?? 0,
      activeSubscribers: stats['active_subscribers'] ?? 0,
      totalMeters: stats['total_meters'] ?? 0,
      totalConsumption: 0.0, // سيتم حسابها من القراءات لاحقاً
      contactPerson: null, // غير متوفر في الجدول الحالي
      contactPhone: null, // غير متوفر في الجدول الحالي
      notes: null, // غير متوفر في الجدول الحالي
      isActive: village.isActive,
      createdAt: village.createdAt,
      updatedAt: village.updatedAt,
    );
  }

  /// الحصول على قائمة القرى للاختيار
  Future<List<Map<String, dynamic>>> getVillagesForDropdown() async {
    try {
      final villages = await (_database.select(_database.villages)
        ..where((v) => v.isActive.equals(true))
        ..orderBy([(v) => OrderingTerm.asc(v.name)]))
        .get();

      return villages.map((village) => {
        'id': village.id,
        'name': village.name,
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على قائمة القرى: $e');
    }
  }

  /// تحديث إحصائيات جميع القرى
  Future<void> updateAllVillagesStatistics() async {
    try {
      final villages = await _database.select(_database.villages).get();
      
      for (final village in villages) {
        await _getVillageStatistics(village.id);
        // الإحصائيات يتم حسابها ديناميكياً، لا حاجة لحفظها في قاعدة البيانات
      }
    } catch (e) {
      throw Exception('خطأ في تحديث إحصائيات القرى: $e');
    }
  }
}
