import 'package:flutter/material.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../domain/entities/meter_entity.dart';

class MeterCard extends StatelessWidget {
  final MeterEntity meter;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onUpdateReading;
  final Function(MeterStatus)? onUpdateStatus;

  const MeterCard({
    super.key,
    required this.meter,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onUpdateReading,
    this.onUpdateStatus,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 12),
          _buildMeterInfo(context),
          const SizedBox(height: 12),
          _buildReadingInfo(context),
          if (meter.needsMaintenance) ...[
            const SizedBox(height: 8),
            _buildMaintenanceWarning(context),
          ],
          if (onEdit != null || onDelete != null || onUpdateReading != null) ...[
            const SizedBox(height: 12),
            const Divider(),
            _buildActions(context),
          ],
        ],
      ),
    );
  }

  /// بناء الرأس
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: meter.statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            meter.typeIcon,
            color: meter.statusColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                meter.meterNumber,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                meter.subscriberName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: meter.statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                meter.statusIcon,
                size: 12,
                color: meter.statusColor,
              ),
              const SizedBox(width: 4),
              Text(
                meter.statusText,
                style: TextStyle(
                  color: meter.statusColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء معلومات العداد
  Widget _buildMeterInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.location_city,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  meter.villageName,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  meter.typeText,
                  style: const TextStyle(
                    color: Colors.blue,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (meter.meterInfo != 'غير محدد') ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.info,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    meter.meterInfo,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// بناء معلومات القراءة
  Widget _buildReadingInfo(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildReadingItem(
            context,
            'القراءة الحالية',
            meter.currentReading.toString(),
            Icons.speed,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildReadingItem(
            context,
            'إجمالي الاستهلاك',
            meter.totalConsumption.toStringAsFixed(2),
            Icons.water_drop,
            Colors.green,
          ),
        ),
      ],
    );
  }

  /// بناء عنصر قراءة
  Widget _buildReadingItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء تحذير الصيانة
  Widget _buildMaintenanceWarning(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning,
            color: Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              meter.daysUntilMaintenance != null && meter.daysUntilMaintenance! < 0
                  ? 'متأخر عن الصيانة بـ ${(-meter.daysUntilMaintenance!)} يوم'
                  : 'يحتاج صيانة',
              style: const TextStyle(
                color: Colors.orange,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإجراءات
  Widget _buildActions(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        if (onUpdateReading != null)
          TextButton.icon(
            onPressed: onUpdateReading,
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('تحديث القراءة'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue,
            ),
          ),
        if (onUpdateStatus != null)
          PopupMenuButton<MeterStatus>(
            onSelected: onUpdateStatus,
            itemBuilder: (context) => MeterStatus.values.map((status) {
              return PopupMenuItem(
                value: status,
                child: Row(
                  children: [
                    Icon(
                      _getStatusIcon(status),
                      size: 16,
                      color: _getStatusColor(status),
                    ),
                    const SizedBox(width: 8),
                    Text(_getStatusText(status)),
                  ],
                ),
              );
            }).toList(),
            child: TextButton.icon(
              onPressed: null,
              icon: const Icon(Icons.swap_horiz, size: 16),
              label: const Text('تغيير الحالة'),
            ),
          ),
        if (onEdit != null)
          TextButton.icon(
            onPressed: onEdit,
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('تعديل'),
          ),
        if (onDelete != null)
          TextButton.icon(
            onPressed: onDelete,
            icon: const Icon(Icons.delete, size: 16),
            label: const Text('حذف'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
      ],
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(MeterStatus status) {
    switch (status) {
      case MeterStatus.active:
        return Icons.check_circle;
      case MeterStatus.inactive:
        return Icons.pause_circle;
      case MeterStatus.maintenance:
        return Icons.build;
      case MeterStatus.damaged:
        return Icons.error;
      case MeterStatus.replaced:
        return Icons.swap_horiz;
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(MeterStatus status) {
    switch (status) {
      case MeterStatus.active:
        return Colors.green;
      case MeterStatus.inactive:
        return Colors.grey;
      case MeterStatus.maintenance:
        return Colors.orange;
      case MeterStatus.damaged:
        return Colors.red;
      case MeterStatus.replaced:
        return Colors.blue;
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(MeterStatus status) {
    switch (status) {
      case MeterStatus.active:
        return 'نشط';
      case MeterStatus.inactive:
        return 'غير نشط';
      case MeterStatus.maintenance:
        return 'صيانة';
      case MeterStatus.damaged:
        return 'معطل';
      case MeterStatus.replaced:
        return 'مستبدل';
    }
  }
}
