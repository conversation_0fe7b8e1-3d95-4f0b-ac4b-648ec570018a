import '../database/app_database.dart';
import '../constants/app_constants.dart';
import 'package:logger/logger.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._init();
  DatabaseService._init();

  late AppDatabase _database;
  final Logger _logger = Logger();

  AppDatabase get database => _database;

  /// تهيئة قاعدة البيانات
  Future<void> initialize() async {
    try {
      _database = AppDatabase();
      _logger.i('تم تهيئة قاعدة البيانات بنجاح');
      
      // التحقق من وجود البيانات الأساسية
      await _ensureBasicData();
    } catch (e) {
      _logger.e('خطأ في تهيئة قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// التأكد من وجود البيانات الأساسية
  Future<void> _ensureBasicData() async {
    try {
      // التحقق من وجود مستخدم مدير
      final adminCount = await (_database.select(_database.users)
        ..where((u) => u.role.equals('admin')))
        .get()
        .then((users) => users.length);

      if (adminCount == 0) {
        _logger.w('لا يوجد مستخدم مدير، سيتم إنشاء واحد افتراضي');
        await _createDefaultAdmin();
      }

      // التحقق من وجود إعدادات أساسية
      await _ensureDefaultSettings();
      
      // التحقق من وجود شرائح تسعير
      await _ensureDefaultPricingTiers();
      
    } catch (e) {
      _logger.e('خطأ في التحقق من البيانات الأساسية: $e');
    }
  }

  /// إنشاء مستخدم مدير افتراضي
  Future<void> _createDefaultAdmin() async {
    try {
      await _database.into(_database.users).insert(UsersCompanion.insert(
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: _database.hashPassword('admin123'),
        fullName: 'مدير النظام',
        role: 'admin',
      ));
      _logger.i('تم إنشاء مستخدم المدير الافتراضي');
    } catch (e) {
      _logger.e('خطأ في إنشاء مستخدم المدير: $e');
    }
  }

  /// التأكد من وجود الإعدادات الافتراضية
  Future<void> _ensureDefaultSettings() async {
    try {
      for (final entry in AppConstants.defaultSettings.entries) {
        final existingSetting = await (_database.select(_database.appSettings)
          ..where((s) => s.key.equals(entry.key)))
          .getSingleOrNull();

        if (existingSetting == null) {
          await _database.into(_database.appSettings).insert(
            AppSettingsCompanion.insert(
              key: entry.key,
              value: entry.value.toString(),
              description: Value('إعداد افتراضي'),
            ),
          );
        }
      }
      _logger.i('تم التحقق من الإعدادات الافتراضية');
    } catch (e) {
      _logger.e('خطأ في إعداد الإعدادات الافتراضية: $e');
    }
  }

  /// التأكد من وجود شرائح التسعير الافتراضية
  Future<void> _ensureDefaultPricingTiers() async {
    try {
      final existingTiers = await (_database.select(_database.pricingTiers)
        ..where((t) => t.subscriptionType.equals('residential')))
        .get();

      if (existingTiers.isEmpty) {
        final defaultTiers = [
          {'tier': 1, 'min': 0.0, 'max': 10.0, 'price': 0.5, 'fixed': 5.0},
          {'tier': 2, 'min': 10.0, 'max': 20.0, 'price': 1.0, 'fixed': 0.0},
          {'tier': 3, 'min': 20.0, 'max': 50.0, 'price': 1.5, 'fixed': 0.0},
          {'tier': 4, 'min': 50.0, 'max': null, 'price': 2.0, 'fixed': 0.0},
        ];

        for (final tier in defaultTiers) {
          await _database.into(_database.pricingTiers).insert(
            PricingTiersCompanion.insert(
              subscriptionType: 'residential',
              tierNumber: tier['tier'] as int,
              minConsumption: tier['min'] as double,
              maxConsumption: Value(tier['max'] as double?),
              pricePerUnit: tier['price'] as double,
              fixedCharge: Value(tier['fixed'] as double),
              effectiveFrom: DateTime.now(),
            ),
          );
        }
        _logger.i('تم إنشاء شرائح التسعير الافتراضية');
      }
    } catch (e) {
      _logger.e('خطأ في إنشاء شرائح التسعير: $e');
    }
  }

  /// الحصول على إعداد معين
  Future<String?> getSetting(String key) async {
    try {
      final setting = await (_database.select(_database.appSettings)
        ..where((s) => s.key.equals(key)))
        .getSingleOrNull();
      
      return setting?.value;
    } catch (e) {
      _logger.e('خطأ في الحصول على الإعداد $key: $e');
      return null;
    }
  }

  /// تحديث إعداد معين
  Future<bool> updateSetting(String key, String value) async {
    try {
      final existingSetting = await (_database.select(_database.appSettings)
        ..where((s) => s.key.equals(key)))
        .getSingleOrNull();

      if (existingSetting != null) {
        await (_database.update(_database.appSettings)
          ..where((s) => s.key.equals(key)))
          .write(AppSettingsCompanion(
            value: Value(value),
            updatedAt: Value(DateTime.now()),
          ));
      } else {
        await _database.into(_database.appSettings).insert(
          AppSettingsCompanion.insert(
            key: key,
            value: value,
          ),
        );
      }
      
      _logger.i('تم تحديث الإعداد $key');
      return true;
    } catch (e) {
      _logger.e('خطأ في تحديث الإعداد $key: $e');
      return false;
    }
  }

  /// تسجيل عملية في سجل المراجعة
  Future<void> logAuditTrail({
    required String tableName,
    required int recordId,
    required String operation,
    int? userId,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
    String? reason,
    String? sessionId,
  }) async {
    try {
      await _database.into(_database.auditLogs).insert(
        AuditLogsCompanion.insert(
          tableName: tableName,
          recordId: recordId,
          operation: operation,
          userId: Value(userId),
          oldValues: Value(oldValues?.toString()),
          newValues: Value(newValues?.toString()),
          reason: Value(reason),
          sessionId: Value(sessionId),
        ),
      );
    } catch (e) {
      _logger.e('خطأ في تسجيل سجل المراجعة: $e');
    }
  }

  /// الحصول على إحصائيات عامة
  Future<Map<String, int>> getGeneralStatistics() async {
    try {
      final stats = <String, int>{};
      
      // عدد المشتركين
      stats['total_subscribers'] = await _database.select(_database.subscribers).get()
        .then((list) => list.length);
      
      // عدد المشتركين النشطين
      stats['active_subscribers'] = await (_database.select(_database.subscribers)
        ..where((s) => s.status.equals('active'))).get()
        .then((list) => list.length);
      
      // عدد القرى
      stats['total_villages'] = await _database.select(_database.villages).get()
        .then((list) => list.length);
      
      // عدد العدادات
      stats['total_meters'] = await _database.select(_database.meters).get()
        .then((list) => list.length);
      
      // عدد الفواتير المعلقة
      stats['pending_bills'] = await (_database.select(_database.bills)
        ..where((b) => b.status.equals('pending'))).get()
        .then((list) => list.length);
      
      // عدد الفواتير المتأخرة
      stats['overdue_bills'] = await (_database.select(_database.bills)
        ..where((b) => b.status.equals('overdue'))).get()
        .then((list) => list.length);
      
      return stats;
    } catch (e) {
      _logger.e('خطأ في الحصول على الإحصائيات: $e');
      return {};
    }
  }

  /// البحث في المشتركين
  Future<List<Subscriber>> searchSubscribers(String query) async {
    try {
      if (query.isEmpty) {
        return await _database.select(_database.subscribers).get();
      }
      
      return await (_database.select(_database.subscribers)
        ..where((s) => 
          s.fullName.contains(query) |
          s.subscriberNumber.contains(query) |
          s.phone.contains(query) |
          s.email.contains(query)
        )
        ..orderBy([(s) => OrderingTerm.asc(s.fullName)]))
        .get();
    } catch (e) {
      _logger.e('خطأ في البحث عن المشتركين: $e');
      return [];
    }
  }

  /// الحصول على المشتركين حسب القرية
  Future<List<Subscriber>> getSubscribersByVillage(int villageId) async {
    try {
      return await (_database.select(_database.subscribers)
        ..where((s) => s.villageId.equals(villageId))
        ..orderBy([(s) => OrderingTerm.asc(s.fullName)]))
        .get();
    } catch (e) {
      _logger.e('خطأ في الحصول على مشتركي القرية: $e');
      return [];
    }
  }

  /// إنشاء نسخة احتياطية من البيانات
  Future<Map<String, dynamic>> exportData() async {
    try {
      final data = <String, dynamic>{};
      
      // تصدير جميع الجداول
      data['users'] = await _database.select(_database.users).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['villages'] = await _database.select(_database.villages).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['subscribers'] = await _database.select(_database.subscribers).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['meters'] = await _database.select(_database.meters).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['readings'] = await _database.select(_database.readings).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['bills'] = await _database.select(_database.bills).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['payments'] = await _database.select(_database.payments).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['app_settings'] = await _database.select(_database.appSettings).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      data['pricing_tiers'] = await _database.select(_database.pricingTiers).get()
        .then((list) => list.map((e) => e.toJson()).toList());
      
      // إضافة معلومات النسخة الاحتياطية
      data['backup_info'] = {
        'version': AppConstants.appVersion,
        'created_at': DateTime.now().toIso8601String(),
        'database_version': _database.schemaVersion,
      };
      
      _logger.i('تم تصدير البيانات بنجاح');
      return data;
    } catch (e) {
      _logger.e('خطأ في تصدير البيانات: $e');
      rethrow;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    try {
      await _database.close();
      _logger.i('تم إغلاق قاعدة البيانات');
    } catch (e) {
      _logger.e('خطأ في إغلاق قاعدة البيانات: $e');
    }
  }

  /// تنظيف البيانات القديمة
  Future<void> cleanupOldData() async {
    try {
      // حذف سجلات المراجعة الأقدم من 6 أشهر
      final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
      await (_database.delete(_database.auditLogs)
        ..where((log) => log.timestamp.isSmallerThanValue(sixMonthsAgo)))
        .go();
      
      _logger.i('تم تنظيف البيانات القديمة');
    } catch (e) {
      _logger.e('خطأ في تنظيف البيانات: $e');
    }
  }
}
