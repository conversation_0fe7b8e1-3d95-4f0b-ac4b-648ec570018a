import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../meters/presentation/providers/meter_provider.dart';
import '../providers/reading_provider.dart';
import '../../domain/entities/reading_entity.dart';

class AddReadingDialog extends ConsumerStatefulWidget {
  final ReadingEntity? reading;

  const AddReadingDialog({
    super.key,
    this.reading,
  });

  @override
  ConsumerState<AddReadingDialog> createState() => _AddReadingDialogState();
}

class _AddReadingDialogState extends ConsumerState<AddReadingDialog> {
  final _formKey = GlobalKey<FormState>();
  final _previousReadingController = TextEditingController();
  final _currentReadingController = TextEditingController();
  final _consumptionController = TextEditingController();
  final _readerNameController = TextEditingController();
  final _notesController = TextEditingController();

  int? _selectedMeterId;
  ReadingType _selectedType = ReadingType.regular;
  ReadingStatus _selectedStatus = ReadingStatus.pending;
  DateTime _readingDate = DateTime.now();
  bool _isEstimated = false;

  @override
  void initState() {
    super.initState();
    if (widget.reading != null) {
      _initializeWithReading();
    }
  }

  void _initializeWithReading() {
    final reading = widget.reading!;
    _selectedMeterId = reading.meterId;
    _previousReadingController.text = reading.previousReading.toString();
    _currentReadingController.text = reading.currentReading.toString();
    _consumptionController.text = reading.consumption.toString();
    _selectedType = reading.type;
    _selectedStatus = reading.status;
    _readingDate = reading.readingDate;
    _readerNameController.text = reading.readerName ?? '';
    _notesController.text = reading.notes ?? '';
    _isEstimated = reading.isEstimated;
  }

  @override
  void dispose() {
    _previousReadingController.dispose();
    _currentReadingController.dispose();
    _consumptionController.dispose();
    _readerNameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      title: widget.reading == null ? 'إضافة قراءة جديدة' : 'تعديل القراءة',
      scrollable: true,
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMeterSelection(),
            const SizedBox(height: 16),
            _buildReadingInfo(),
            const SizedBox(height: 16),
            _buildConsumptionInfo(),
            const SizedBox(height: 16),
            _buildAdditionalInfo(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveReading,
          child: Text(widget.reading == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }

  /// بناء اختيار العداد
  Widget _buildMeterSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختيار العداد',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildMeterDropdown(),
        const SizedBox(height: 12),
        _buildReadingDatePicker(),
      ],
    );
  }

  /// بناء قائمة العدادات
  Widget _buildMeterDropdown() {
    final metersAsync = ref.watch(metersProvider);
    
    return metersAsync.when(
      data: (meters) => DropdownButtonFormField<int>(
        value: _selectedMeterId,
        decoration: const InputDecoration(
          labelText: 'العداد *',
          border: OutlineInputBorder(),
        ),
        items: meters.map((meter) {
          return DropdownMenuItem(
            value: meter.id,
            child: Text('${meter.meterNumber} - ${meter.subscriberName}'),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedMeterId = value;
          });
          if (value != null) {
            _loadLastReading(value);
          }
        },
        validator: (value) {
          if (value == null) {
            return 'يجب اختيار العداد';
          }
          return null;
        },
      ),
      loading: () => const LinearProgressIndicator(),
      error: (error, stack) => Text('خطأ في تحميل العدادات: $error'),
    );
  }

  /// تحميل آخر قراءة للعداد
  void _loadLastReading(int meterId) async {
    try {
      final repository = ref.read(readingRepositoryProvider);
      final lastReading = await repository.getLastReadingForMeter(meterId);
      
      if (lastReading != null && mounted) {
        setState(() {
          _previousReadingController.text = lastReading.currentReading.toString();
        });
        _calculateConsumption();
      }
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  /// بناء اختيار تاريخ القراءة
  Widget _buildReadingDatePicker() {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _readingDate,
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 30)),
        );
        if (date != null) {
          setState(() {
            _readingDate = date;
          });
        }
      },
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'تاريخ القراءة',
          border: OutlineInputBorder(),
          suffixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          '${_readingDate.day}/${_readingDate.month}/${_readingDate.year}',
        ),
      ),
    );
  }

  /// بناء معلومات القراءة
  Widget _buildReadingInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات القراءة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _previousReadingController,
                decoration: const InputDecoration(
                  labelText: 'القراءة السابقة',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                readOnly: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _currentReadingController,
                decoration: const InputDecoration(
                  labelText: 'القراءة الحالية *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (_) => _calculateConsumption(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'القراءة الحالية مطلوبة';
                  }
                  final currentReading = double.tryParse(value);
                  final previousReading = double.tryParse(_previousReadingController.text);
                  
                  if (currentReading == null) {
                    return 'قيمة غير صحيحة';
                  }
                  
                  if (previousReading != null && currentReading < previousReading) {
                    return 'لا يمكن أن تكون أقل من القراءة السابقة';
                  }
                  
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<ReadingType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع القراءة',
                  border: OutlineInputBorder(),
                ),
                items: ReadingType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getTypeText(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                    _isEstimated = value == ReadingType.estimated;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<ReadingStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'حالة القراءة',
                  border: OutlineInputBorder(),
                ),
                items: ReadingStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء معلومات الاستهلاك
  Widget _buildConsumptionInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الاستهلاك',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _consumptionController,
          decoration: const InputDecoration(
            labelText: 'الاستهلاك (م³)',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.water_drop),
          ),
          keyboardType: TextInputType.number,
          readOnly: _selectedType != ReadingType.estimated,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'الاستهلاك مطلوب';
            }
            final consumption = double.tryParse(value);
            if (consumption == null || consumption < 0) {
              return 'قيمة غير صحيحة';
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(
              Icons.info,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                _selectedType == ReadingType.estimated
                    ? 'يمكن تعديل الاستهلاك للقراءات التقديرية'
                    : 'الاستهلاك محسوب تلقائياً من الفرق بين القراءتين',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات إضافية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _readerNameController,
          decoration: const InputDecoration(
            labelText: 'اسم القارئ',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  /// حساب الاستهلاك
  void _calculateConsumption() {
    if (_selectedType != ReadingType.estimated) {
      final previousReading = double.tryParse(_previousReadingController.text) ?? 0;
      final currentReading = double.tryParse(_currentReadingController.text) ?? 0;
      final consumption = currentReading - previousReading;
      
      if (consumption >= 0) {
        _consumptionController.text = consumption.toString();
      }
    }
  }

  /// الحصول على نص النوع
  String _getTypeText(ReadingType type) {
    switch (type) {
      case ReadingType.regular:
        return 'عادية';
      case ReadingType.estimated:
        return 'تقديرية';
      case ReadingType.final_reading:
        return 'نهائية';
      case ReadingType.initial:
        return 'أولية';
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(ReadingStatus status) {
    switch (status) {
      case ReadingStatus.pending:
        return 'في الانتظار';
      case ReadingStatus.confirmed:
        return 'مؤكدة';
      case ReadingStatus.billed:
        return 'مفوترة';
      case ReadingStatus.disputed:
        return 'متنازع عليها';
    }
  }

  /// حفظ القراءة
  void _saveReading() async {
    if (_formKey.currentState!.validate()) {
      final reading = ReadingEntity(
        id: widget.reading?.id ?? 0,
        meterId: _selectedMeterId!,
        meterNumber: '', // سيتم تحديثه من قاعدة البيانات
        subscriberId: 0, // سيتم تحديثه من قاعدة البيانات
        subscriberName: '', // سيتم تحديثه من قاعدة البيانات
        villageName: '', // سيتم تحديثه من قاعدة البيانات
        previousReading: double.parse(_previousReadingController.text.trim()),
        currentReading: double.parse(_currentReadingController.text.trim()),
        consumption: double.parse(_consumptionController.text.trim()),
        readingDate: _readingDate,
        type: _selectedType,
        status: _selectedStatus,
        readerName: _readerNameController.text.trim().isEmpty ? null : _readerNameController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        isEstimated: _isEstimated,
        estimatedConsumption: _isEstimated ? double.parse(_consumptionController.text.trim()) : null,
        createdAt: widget.reading?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      try {
        await ref.read(readingFormProvider.notifier).saveReading(reading);
        
        final state = ref.read(readingFormProvider);
        if (!mounted) return;
        
        if (state.isSuccess) {
          Navigator.of(context).pop(true);
        } else if (state.error != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.error!),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ القراءة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
