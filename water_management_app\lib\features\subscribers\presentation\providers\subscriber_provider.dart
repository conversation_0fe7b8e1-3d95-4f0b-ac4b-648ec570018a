import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/subscriber_repository.dart';
import '../../domain/entities/subscriber_entity.dart';

/// مزود مستودع المشتركين
final subscriberRepositoryProvider = Provider<SubscriberRepository>((ref) {
  return SubscriberRepository();
});

/// مزود قائمة المشتركين
final subscribersProvider = FutureProvider<List<SubscriberEntity>>((ref) async {
  final repository = ref.read(subscriberRepositoryProvider);
  return await repository.getAllSubscribers();
});

/// مزود البحث في المشتركين
final subscriberSearchProvider = FutureProvider.family<List<SubscriberEntity>, String>((ref, query) async {
  final repository = ref.read(subscriberRepositoryProvider);
  return await repository.searchSubscribers(query);
});

/// مزود المشترك الواحد
final subscriberProvider = FutureProvider.family<SubscriberEntity?, int>((ref, id) async {
  final repository = ref.read(subscriberRepositoryProvider);
  return await repository.getSubscriberById(id);
});

/// مزود المشتركين حسب القرية
final subscribersByVillageProvider = FutureProvider.family<List<SubscriberEntity>, int>((ref, villageId) async {
  final repository = ref.read(subscriberRepositoryProvider);
  return await repository.getSubscribersByVillage(villageId);
});

/// مزود إحصائيات المشتركين
final subscriberStatisticsProvider = FutureProvider<Map<String, int>>((ref) async {
  final repository = ref.read(subscriberRepositoryProvider);
  return await repository.getSubscriberStatistics();
});

/// مزود حالة نموذج المشترك
final subscriberFormProvider = StateNotifierProvider<SubscriberFormNotifier, SubscriberFormState>((ref) {
  final repository = ref.read(subscriberRepositoryProvider);
  return SubscriberFormNotifier(repository);
});

/// حالة نموذج المشترك
class SubscriberFormState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final SubscriberEntity? subscriber;

  const SubscriberFormState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
    this.subscriber,
  });

  factory SubscriberFormState.initial() {
    return const SubscriberFormState(
      isLoading: false,
      isSuccess: false,
      error: null,
      subscriber: null,
    );
  }

  SubscriberFormState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    SubscriberEntity? subscriber,
  }) {
    return SubscriberFormState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      subscriber: subscriber ?? this.subscriber,
    );
  }
}

/// مدير حالة نموذج المشترك
class SubscriberFormNotifier extends StateNotifier<SubscriberFormState> {
  final SubscriberRepository _repository;

  SubscriberFormNotifier(this._repository) : super(SubscriberFormState.initial());

  /// حفظ مشترك (إضافة أو تحديث)
  Future<void> saveSubscriber(SubscriberEntity subscriber) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      SubscriberEntity savedSubscriber;
      
      if (subscriber.id == 0) {
        // إضافة مشترك جديد
        savedSubscriber = await _repository.createSubscriber(subscriber);
      } else {
        // تحديث مشترك موجود
        savedSubscriber = await _repository.updateSubscriber(subscriber);
      }
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        subscriber: savedSubscriber,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// حذف مشترك
  Future<void> deleteSubscriber(int id) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteSubscriber(id);
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// إنشاء رقم مشترك تلقائي
  Future<String> generateSubscriberNumber() async {
    try {
      return await _repository.generateSubscriberNumber();
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم المشترك: $e');
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = SubscriberFormState.initial();
  }

  /// تحديث المشترك في الحالة
  void updateSubscriber(SubscriberEntity subscriber) {
    state = state.copyWith(subscriber: subscriber);
  }

  /// تحديث حالة التحميل
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// تحديث حالة النجاح
  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }
}

/// مزود حالة البحث
final subscriberSearchStateProvider = StateNotifierProvider<SubscriberSearchNotifier, SubscriberSearchState>((ref) {
  return SubscriberSearchNotifier();
});

/// حالة البحث
class SubscriberSearchState {
  final String query;
  final bool isSearching;
  final List<SubscriberEntity> results;
  final String? error;

  const SubscriberSearchState({
    required this.query,
    required this.isSearching,
    required this.results,
    this.error,
  });

  factory SubscriberSearchState.initial() {
    return const SubscriberSearchState(
      query: '',
      isSearching: false,
      results: [],
      error: null,
    );
  }

  SubscriberSearchState copyWith({
    String? query,
    bool? isSearching,
    List<SubscriberEntity>? results,
    String? error,
  }) {
    return SubscriberSearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      results: results ?? this.results,
      error: error,
    );
  }
}

/// مدير حالة البحث
class SubscriberSearchNotifier extends StateNotifier<SubscriberSearchState> {
  SubscriberSearchNotifier() : super(SubscriberSearchState.initial());

  /// تحديث نص البحث
  void updateQuery(String query) {
    state = state.copyWith(query: query);
  }

  /// تحديث نتائج البحث
  void updateResults(List<SubscriberEntity> results) {
    state = state.copyWith(results: results, isSearching: false);
  }

  /// تحديث حالة البحث
  void setSearching(bool isSearching) {
    state = state.copyWith(isSearching: isSearching);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error, isSearching: false);
  }

  /// مسح البحث
  void clearSearch() {
    state = SubscriberSearchState.initial();
  }
}

/// مزود حالة التصفية
final subscriberFilterProvider = StateNotifierProvider<SubscriberFilterNotifier, SubscriberFilterState>((ref) {
  return SubscriberFilterNotifier();
});

/// حالة التصفية
class SubscriberFilterState {
  final SubscriptionStatus? statusFilter;
  final SubscriptionType? typeFilter;
  final int? villageFilter;
  final bool showCharitableOnly;

  const SubscriberFilterState({
    this.statusFilter,
    this.typeFilter,
    this.villageFilter,
    required this.showCharitableOnly,
  });

  factory SubscriberFilterState.initial() {
    return const SubscriberFilterState(
      statusFilter: null,
      typeFilter: null,
      villageFilter: null,
      showCharitableOnly: false,
    );
  }

  SubscriberFilterState copyWith({
    SubscriptionStatus? statusFilter,
    SubscriptionType? typeFilter,
    int? villageFilter,
    bool? showCharitableOnly,
  }) {
    return SubscriberFilterState(
      statusFilter: statusFilter ?? this.statusFilter,
      typeFilter: typeFilter ?? this.typeFilter,
      villageFilter: villageFilter ?? this.villageFilter,
      showCharitableOnly: showCharitableOnly ?? this.showCharitableOnly,
    );
  }

  /// التحقق من وجود مرشحات نشطة
  bool get hasActiveFilters {
    return statusFilter != null ||
        typeFilter != null ||
        villageFilter != null ||
        showCharitableOnly;
  }
}

/// مدير حالة التصفية
class SubscriberFilterNotifier extends StateNotifier<SubscriberFilterState> {
  SubscriberFilterNotifier() : super(SubscriberFilterState.initial());

  /// تحديث مرشح الحالة
  void updateStatusFilter(SubscriptionStatus? status) {
    state = state.copyWith(statusFilter: status);
  }

  /// تحديث مرشح النوع
  void updateTypeFilter(SubscriptionType? type) {
    state = state.copyWith(typeFilter: type);
  }

  /// تحديث مرشح القرية
  void updateVillageFilter(int? villageId) {
    state = state.copyWith(villageFilter: villageId);
  }

  /// تحديث مرشح الخيري
  void updateCharitableFilter(bool showCharitableOnly) {
    state = state.copyWith(showCharitableOnly: showCharitableOnly);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    state = SubscriberFilterState.initial();
  }
}
