﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}"
	ProjectSection(ProjectDependencies) = postProject
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2} = {6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}
		{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E} = {6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{FB5924D4-52CD-39EC-86C5-085B4BFBFE63}"
	ProjectSection(ProjectDependencies) = postProject
		{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40} = {D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2} = {6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{20312FFA-FE06-385B-BDB8-BFAF723CA98D}"
	ProjectSection(ProjectDependencies) = postProject
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2} = {6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{876ED509-40B6-3029-8368-C75666F9D05E}"
	ProjectSection(ProjectDependencies) = postProject
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2} = {6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D} = {20312FFA-FE06-385B-BDB8-BFAF723CA98D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "printing_plugin", "printing_plugin.vcxproj", "{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}"
	ProjectSection(ProjectDependencies) = postProject
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2} = {6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D} = {20312FFA-FE06-385B-BDB8-BFAF723CA98D}
		{876ED509-40B6-3029-8368-C75666F9D05E} = {876ED509-40B6-3029-8368-C75666F9D05E}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}.Debug|x64.ActiveCfg = Debug|x64
		{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}.Debug|x64.Build.0 = Debug|x64
		{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}.Profile|x64.ActiveCfg = Profile|x64
		{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}.Profile|x64.Build.0 = Profile|x64
		{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}.Release|x64.ActiveCfg = Release|x64
		{D9B4AF92-6C7E-382E-8E0C-1AA43F3DDB40}.Release|x64.Build.0 = Release|x64
		{FB5924D4-52CD-39EC-86C5-085B4BFBFE63}.Debug|x64.ActiveCfg = Debug|x64
		{FB5924D4-52CD-39EC-86C5-085B4BFBFE63}.Profile|x64.ActiveCfg = Profile|x64
		{FB5924D4-52CD-39EC-86C5-085B4BFBFE63}.Release|x64.ActiveCfg = Release|x64
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}.Debug|x64.ActiveCfg = Debug|x64
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}.Debug|x64.Build.0 = Debug|x64
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}.Profile|x64.ActiveCfg = Profile|x64
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}.Profile|x64.Build.0 = Profile|x64
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}.Release|x64.ActiveCfg = Release|x64
		{6F6A7935-A1A1-32BE-8961-721A5FC3ADD2}.Release|x64.Build.0 = Release|x64
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D}.Debug|x64.ActiveCfg = Debug|x64
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D}.Debug|x64.Build.0 = Debug|x64
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D}.Profile|x64.ActiveCfg = Profile|x64
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D}.Profile|x64.Build.0 = Profile|x64
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D}.Release|x64.ActiveCfg = Release|x64
		{20312FFA-FE06-385B-BDB8-BFAF723CA98D}.Release|x64.Build.0 = Release|x64
		{876ED509-40B6-3029-8368-C75666F9D05E}.Debug|x64.ActiveCfg = Debug|x64
		{876ED509-40B6-3029-8368-C75666F9D05E}.Debug|x64.Build.0 = Debug|x64
		{876ED509-40B6-3029-8368-C75666F9D05E}.Profile|x64.ActiveCfg = Profile|x64
		{876ED509-40B6-3029-8368-C75666F9D05E}.Profile|x64.Build.0 = Profile|x64
		{876ED509-40B6-3029-8368-C75666F9D05E}.Release|x64.ActiveCfg = Release|x64
		{876ED509-40B6-3029-8368-C75666F9D05E}.Release|x64.Build.0 = Release|x64
		{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}.Debug|x64.ActiveCfg = Debug|x64
		{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}.Debug|x64.Build.0 = Debug|x64
		{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}.Profile|x64.ActiveCfg = Profile|x64
		{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}.Profile|x64.Build.0 = Profile|x64
		{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}.Release|x64.ActiveCfg = Release|x64
		{6CE23711-F7E2-3147-8B3E-7B884C5EBA0E}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A1611E80-DE52-3D63-8160-05F7A1FFAD64}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
