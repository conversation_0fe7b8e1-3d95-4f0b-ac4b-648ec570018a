import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// نقاط الكسر للتخطيط التكيفي
class BreakPoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
  static const double largeDesktop = 1600;
}

/// أنواع التخطيط
enum LayoutType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// فئة مساعدة للتخطيط التكيفي
class AdaptiveLayout {
  /// تحديد نوع التخطيط بناءً على عرض الشاشة
  static LayoutType getLayoutType(double width) {
    if (width < BreakPoints.mobile) {
      return LayoutType.mobile;
    } else if (width < BreakPoints.tablet) {
      return LayoutType.tablet;
    } else if (width < BreakPoints.desktop) {
      return LayoutType.desktop;
    } else {
      return LayoutType.largeDesktop;
    }
  }

  /// التحقق من كون التخطيط للهاتف المحمول
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < BreakPoints.mobile;
  }

  /// التحقق من كون التخطيط للجهاز اللوحي
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= BreakPoints.mobile && width < BreakPoints.tablet;
  }

  /// التحقق من كون التخطيط لسطح المكتب
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= BreakPoints.tablet;
  }

  /// التحقق من كون التخطيط لسطح المكتب الكبير
  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= BreakPoints.largeDesktop;
  }

  /// الحصول على عدد الأعمدة المناسب
  static int getGridColumns(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < BreakPoints.mobile) {
      return 1;
    } else if (width < BreakPoints.tablet) {
      return 2;
    } else if (width < BreakPoints.desktop) {
      return 3;
    } else if (width < BreakPoints.largeDesktop) {
      return 4;
    } else {
      return 6;
    }
  }

  /// الحصول على الحشو المناسب
  static EdgeInsets getPadding(BuildContext context) {
    if (isDesktop(context)) {
      return const EdgeInsets.all(24);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(16);
    } else {
      return const EdgeInsets.all(12);
    }
  }

  /// الحصول على المسافة بين العناصر
  static double getSpacing(BuildContext context) {
    if (isDesktop(context)) {
      return 16;
    } else if (isTablet(context)) {
      return 12;
    } else {
      return 8;
    }
  }

  /// الحصول على عرض الشريط الجانبي
  static double getSidebarWidth(BuildContext context) {
    if (isLargeDesktop(context)) {
      return 280;
    } else if (isDesktop(context)) {
      return 240;
    } else {
      return 200;
    }
  }

  /// الحصول على عرض اللوحة الجانبية
  static double getPanelWidth(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (isLargeDesktop(context)) {
      return width * 0.25;
    } else if (isDesktop(context)) {
      return width * 0.3;
    } else {
      return width * 0.4;
    }
  }
}

/// واجهة التخطيط التكيفي
class AdaptiveLayoutBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, LayoutType layoutType) builder;

  const AdaptiveLayoutBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final layoutType = AdaptiveLayout.getLayoutType(constraints.maxWidth);
        return builder(context, layoutType);
      },
    );
  }
}

/// واجهة التخطيط المتجاوب
class ResponsiveLayout extends StatelessWidget {
  final Widget? mobile;
  final Widget? tablet;
  final Widget desktop;
  final Widget? largeDesktop;

  const ResponsiveLayout({
    super.key,
    this.mobile,
    this.tablet,
    required this.desktop,
    this.largeDesktop,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveLayoutBuilder(
      builder: (context, layoutType) {
        switch (layoutType) {
          case LayoutType.mobile:
            return mobile ?? desktop;
          case LayoutType.tablet:
            return tablet ?? desktop;
          case LayoutType.desktop:
            return desktop;
          case LayoutType.largeDesktop:
            return largeDesktop ?? desktop;
        }
      },
    );
  }
}

/// مساعد للتنقل التكيفي
class AdaptiveNavigation {
  /// تحديد نوع التنقل المناسب
  static NavigationType getNavigationType(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return NavigationType.rail;
    } else if (AdaptiveLayout.isTablet(context)) {
      return NavigationType.drawer;
    } else {
      return NavigationType.bottomBar;
    }
  }

  /// فتح الحوار بحجم مناسب
  static Future<T?> showAdaptiveDialog<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
  }) {
    if (AdaptiveLayout.isDesktop(context)) {
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => Dialog(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: AdaptiveLayout.isLargeDesktop(context) ? 600 : 500,
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            child: child,
          ),
        ),
      );
    } else {
      return showModalBottomSheet<T>(
        context: context,
        isScrollControlled: true,
        builder: (context) => DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (context, scrollController) => child,
        ),
      );
    }
  }

  /// عرض اللوحة الجانبية
  static void showSidePanel({
    required BuildContext context,
    required Widget child,
    String? title,
  }) {
    if (AdaptiveLayout.isDesktop(context)) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => Align(
          alignment: Alignment.centerRight,
          child: Material(
            child: Container(
              width: AdaptiveLayout.getPanelWidth(context),
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(-2, 0),
                  ),
                ],
              ),
              child: Column(
                children: [
                  if (title != null)
                    AppBar(
                      title: Text(title),
                      automaticallyImplyLeading: false,
                      actions: [
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                  Expanded(child: child),
                ],
              ),
            ),
          ),
        ),
      );
    } else {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) => DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (context, scrollController) => child,
        ),
      );
    }
  }
}

/// أنواع التنقل
enum NavigationType {
  bottomBar,
  drawer,
  rail,
}

/// مساعد للاختصارات
class AdaptiveShortcuts extends StatelessWidget {
  final Widget child;
  final Map<LogicalKeySet, VoidCallback> shortcuts;

  const AdaptiveShortcuts({
    super.key,
    required this.child,
    required this.shortcuts,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            for (final entry in shortcuts.entries) {
              // تحقق بسيط من الاختصارات
              if (entry.key.keys.contains(event.logicalKey)) {
                entry.value();
                return KeyEventResult.handled;
              }
            }
          }
          return KeyEventResult.ignored;
        },
        child: child,
      );
    }
    return child;
  }
}

/// مساعد للسياق
class AdaptiveContextMenu extends StatelessWidget {
  final Widget child;
  final List<PopupMenuEntry> menuItems;

  const AdaptiveContextMenu({
    super.key,
    required this.child,
    required this.menuItems,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return GestureDetector(
        onSecondaryTapDown: (details) {
          showMenu(
            context: context,
            position: RelativeRect.fromLTRB(
              details.globalPosition.dx,
              details.globalPosition.dy,
              details.globalPosition.dx,
              details.globalPosition.dy,
            ),
            items: menuItems,
          );
        },
        child: child,
      );
    }
    return child;
  }
}

/// مساعد للتمرير التكيفي
class AdaptiveScrollbar extends StatelessWidget {
  final Widget child;
  final ScrollController? controller;

  const AdaptiveScrollbar({
    super.key,
    required this.child,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return Scrollbar(
        controller: controller,
        thumbVisibility: true,
        trackVisibility: true,
        child: child,
      );
    }
    return child;
  }
}

/// مساعد للتحديد المتعدد
class AdaptiveSelection extends StatefulWidget {
  final Widget child;
  final bool enabled;
  final VoidCallback? onSelectionChanged;

  const AdaptiveSelection({
    super.key,
    required this.child,
    this.enabled = true,
    this.onSelectionChanged,
  });

  @override
  State<AdaptiveSelection> createState() => _AdaptiveSelectionState();
}

class _AdaptiveSelectionState extends State<AdaptiveSelection> {
  @override
  Widget build(BuildContext context) {
    if (!AdaptiveLayout.isDesktop(context) || !widget.enabled) {
      return widget.child;
    }

    return Focus(
      onKeyEvent: (node, event) {
        // يمكن إضافة منطق التحديد المتعدد هنا لاحقاً
        return KeyEventResult.ignored;
      },
      child: widget.child,
    );
  }
}
