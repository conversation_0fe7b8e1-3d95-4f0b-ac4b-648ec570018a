import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../readings/presentation/providers/reading_provider.dart';
import '../providers/bill_provider.dart';
import '../../domain/entities/bill_entity.dart';

class AddBillDialog extends ConsumerStatefulWidget {
  final BillEntity? bill;

  const AddBillDialog({
    super.key,
    this.bill,
  });

  @override
  ConsumerState<AddBillDialog> createState() => _AddBillDialogState();
}

class _AddBillDialogState extends ConsumerState<AddBillDialog> {
  final _formKey = GlobalKey<FormState>();
  final _unitPriceController = TextEditingController();
  final _sewerageRateController = TextEditingController();
  final _maintenanceRateController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _discountController = TextEditingController();
  final _notesController = TextEditingController();

  int? _selectedReadingId;
  DateTime _issueDate = DateTime.now();
  DateTime _dueDate = DateTime.now().add(const Duration(days: 30));
  BillType _selectedType = BillType.regular;
  BillStatus _selectedStatus = BillStatus.draft;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    if (widget.bill != null) {
      _initializeWithBill();
    }
  }

  void _initializeControllers() {
    _unitPriceController.text = '2.50'; // سعر افتراضي
    _sewerageRateController.text = '10'; // 10%
    _maintenanceRateController.text = '5'; // 5%
    _taxRateController.text = '15'; // 15%
    _discountController.text = '0';
  }

  void _initializeWithBill() {
    final bill = widget.bill!;
    _selectedReadingId = bill.readingId;
    _issueDate = bill.issueDate;
    _dueDate = bill.dueDate;
    _selectedType = bill.type;
    _selectedStatus = bill.status;
    _unitPriceController.text = bill.unitPrice.toString();
    _sewerageRateController.text = ((bill.sewerageAmount / bill.waterAmount) * 100).toStringAsFixed(0);
    _maintenanceRateController.text = ((bill.maintenanceAmount / bill.waterAmount) * 100).toStringAsFixed(0);
    _taxRateController.text = ((bill.taxAmount / (bill.waterAmount + bill.sewerageAmount + bill.maintenanceAmount)) * 100).toStringAsFixed(0);
    _discountController.text = bill.discountAmount.toString();
    _notesController.text = bill.notes ?? '';
  }

  @override
  void dispose() {
    _unitPriceController.dispose();
    _sewerageRateController.dispose();
    _maintenanceRateController.dispose();
    _taxRateController.dispose();
    _discountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      title: widget.bill == null ? 'إضافة فاتورة جديدة' : 'تعديل الفاتورة',
      scrollable: true,
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildReadingSelection(),
            const SizedBox(height: 16),
            _buildBillInfo(),
            const SizedBox(height: 16),
            _buildPricingInfo(),
            const SizedBox(height: 16),
            _buildAdditionalInfo(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveBill,
          child: Text(widget.bill == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }

  /// بناء اختيار القراءة
  Widget _buildReadingSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختيار القراءة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildReadingDropdown(),
      ],
    );
  }

  /// بناء قائمة القراءات
  Widget _buildReadingDropdown() {
    final readingsAsync = ref.watch(unbilledReadingsProvider);
    
    return readingsAsync.when(
      data: (readings) => DropdownButtonFormField<int>(
        value: _selectedReadingId,
        decoration: const InputDecoration(
          labelText: 'القراءة *',
          border: OutlineInputBorder(),
          helperText: 'اختر القراءة المراد فوترتها',
        ),
        items: readings.map((reading) {
          return DropdownMenuItem(
            value: reading.id,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${reading.meterNumber} - ${reading.subscriberName}'),
                Text(
                  'الاستهلاك: ${reading.consumption.toStringAsFixed(2)} م³',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedReadingId = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return 'يجب اختيار القراءة';
          }
          return null;
        },
      ),
      loading: () => const LinearProgressIndicator(),
      error: (error, stack) => Text('خطأ في تحميل القراءات: $error'),
    );
  }

  /// بناء معلومات الفاتورة
  Widget _buildBillInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الفاتورة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDatePicker(
                'تاريخ الإصدار',
                _issueDate,
                (date) => setState(() => _issueDate = date),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDatePicker(
                'تاريخ الاستحقاق',
                _dueDate,
                (date) => setState(() => _dueDate = date),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<BillType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الفاتورة',
                  border: OutlineInputBorder(),
                ),
                items: BillType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getTypeText(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<BillStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'حالة الفاتورة',
                  border: OutlineInputBorder(),
                ),
                items: BillStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء معلومات التسعير
  Widget _buildPricingInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات التسعير',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _unitPriceController,
                decoration: const InputDecoration(
                  labelText: 'سعر الوحدة (ريال/م³)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'سعر الوحدة مطلوب';
                  }
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) {
                    return 'قيمة غير صحيحة';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _discountController,
                decoration: const InputDecoration(
                  labelText: 'الخصم (ريال)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final discount = double.tryParse(value);
                    if (discount == null || discount < 0) {
                      return 'قيمة غير صحيحة';
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _sewerageRateController,
                decoration: const InputDecoration(
                  labelText: 'نسبة الصرف الصحي (%)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final rate = double.tryParse(value);
                    if (rate == null || rate < 0 || rate > 100) {
                      return 'نسبة غير صحيحة (0-100)';
                    }
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _maintenanceRateController,
                decoration: const InputDecoration(
                  labelText: 'نسبة الصيانة (%)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final rate = double.tryParse(value);
                    if (rate == null || rate < 0 || rate > 100) {
                      return 'نسبة غير صحيحة (0-100)';
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _taxRateController,
          decoration: const InputDecoration(
            labelText: 'نسبة الضريبة (%)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              final rate = double.tryParse(value);
              if (rate == null || rate < 0 || rate > 100) {
                return 'نسبة غير صحيحة (0-100)';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات إضافية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  /// بناء منتقي التاريخ
  Widget _buildDatePicker(
    String label,
    DateTime selectedDate,
    Function(DateTime) onDateSelected,
  ) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: selectedDate,
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (date != null) {
          onDateSelected(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
        ),
      ),
    );
  }

  /// الحصول على نص النوع
  String _getTypeText(BillType type) {
    switch (type) {
      case BillType.regular:
        return 'عادية';
      case BillType.estimated:
        return 'تقديرية';
      case BillType.final_bill:
        return 'نهائية';
      case BillType.adjustment:
        return 'تعديل';
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(BillStatus status) {
    switch (status) {
      case BillStatus.draft:
        return 'مسودة';
      case BillStatus.issued:
        return 'صادرة';
      case BillStatus.paid:
        return 'مدفوعة';
      case BillStatus.overdue:
        return 'متأخرة';
      case BillStatus.cancelled:
        return 'ملغية';
    }
  }

  /// حفظ الفاتورة
  void _saveBill() async {
    if (_formKey.currentState!.validate()) {
      // الحصول على القراءة المحددة
      final readingsAsync = ref.read(unbilledReadingsProvider);
      final readings = readingsAsync.value ?? [];
      final selectedReading = readings.firstWhere(
        (r) => r.id == _selectedReadingId,
        orElse: () => throw Exception('القراءة غير موجودة'),
      );

      final unitPrice = double.parse(_unitPriceController.text.trim());
      final sewerageRate = double.parse(_sewerageRateController.text.trim()) / 100;
      final maintenanceRate = double.parse(_maintenanceRateController.text.trim()) / 100;
      final taxRate = double.parse(_taxRateController.text.trim()) / 100;
      final discountAmount = double.tryParse(_discountController.text.trim()) ?? 0.0;

      final bill = BillEntity.fromReading(
        readingId: selectedReading.id,
        subscriberId: selectedReading.subscriberId,
        subscriberName: selectedReading.subscriberName,
        villageName: selectedReading.villageName,
        meterNumber: selectedReading.meterNumber,
        previousReading: selectedReading.previousReading,
        currentReading: selectedReading.currentReading,
        consumption: selectedReading.consumption,
        billingPeriodStart: DateTime(selectedReading.readingDate.year, selectedReading.readingDate.month, 1),
        billingPeriodEnd: DateTime(selectedReading.readingDate.year, selectedReading.readingDate.month + 1, 0),
        unitPrice: unitPrice,
        sewerageRate: sewerageRate,
        maintenanceRate: maintenanceRate,
        taxRate: taxRate,
        discountAmount: discountAmount,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      ).copyWith(
        id: widget.bill?.id ?? 0,
        billNumber: widget.bill?.billNumber ?? '',
        issueDate: _issueDate,
        dueDate: _dueDate,
        type: _selectedType,
        status: _selectedStatus,
      );

      try {
        await ref.read(billFormProvider.notifier).saveBill(bill);
        
        final state = ref.read(billFormProvider);
        if (!mounted) return;
        
        if (state.isSuccess) {
          Navigator.of(context).pop(true);
        } else if (state.error != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.error!),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
