import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../core/services/local_auth_service.dart';
import '../../../../core/services/database_service.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../../../../core/adaptive/adaptive_navigation.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../auth/presentation/pages/login_page.dart';
import '../../../subscribers/presentation/pages/subscribers_page.dart';
import '../../../villages/presentation/pages/villages_page.dart';
import '../../../meters/presentation/pages/meters_page.dart';
import '../../../readings/presentation/pages/readings_page.dart';
import '../../../bills/presentation/pages/bills_page.dart';
import '../../../payments/presentation/pages/payments_page.dart';
import '../../../reports/presentation/pages/reports_page.dart';

class AdaptiveHomePage extends StatefulWidget {
  const AdaptiveHomePage({super.key});

  @override
  State<AdaptiveHomePage> createState() => _AdaptiveHomePageState();
}

class _AdaptiveHomePageState extends State<AdaptiveHomePage> {
  Map<String, int> _statistics = {};
  bool _isLoading = true;
  String _currentRoute = '/dashboard';

  // عناصر التنقل
  late final List<NavigationItem> _navigationItems;

  @override
  void initState() {
    super.initState();
    _initializeNavigation();
    _loadStatistics();
  }

  /// تهيئة عناصر التنقل
  void _initializeNavigation() {
    _navigationItems = [
      const NavigationItem(
        label: 'لوحة التحكم',
        icon: Icons.dashboard,
        selectedIcon: Icons.dashboard,
        route: '/dashboard',
      ),
      const NavigationItem(
        label: 'إدارة المشتركين',
        icon: Icons.people,
        selectedIcon: Icons.people,
        route: '/subscribers',
      ),
      const NavigationItem(
        label: 'إدارة القرى',
        icon: Icons.location_city,
        selectedIcon: Icons.location_city,
        route: '/villages',
      ),
      const NavigationItem(
        label: 'إدارة العدادات',
        icon: Icons.speed,
        selectedIcon: Icons.speed,
        route: '/meters',
      ),
      const NavigationItem(
        label: 'القراءات',
        icon: Icons.analytics,
        selectedIcon: Icons.analytics,
        route: '/readings',
      ),
      const NavigationItem(
        label: 'الفوترة',
        icon: Icons.receipt_long,
        selectedIcon: Icons.receipt_long,
        route: '/billing',
      ),
      const NavigationItem(
        label: 'المدفوعات',
        icon: Icons.payment,
        selectedIcon: Icons.payment,
        route: '/payments',
      ),
      const NavigationItem(
        label: 'التقارير',
        icon: Icons.assessment,
        selectedIcon: Icons.assessment,
        route: '/reports',
      ),
      const NavigationItem(
        label: 'الإعدادات',
        icon: Icons.settings,
        selectedIcon: Icons.settings,
        route: '/settings',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveKeyboardShortcuts(
      shortcuts: {
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN): _addNewSubscriber,
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyR): _loadStatistics,
        LogicalKeySet(LogicalKeyboardKey.f5): _loadStatistics,
      },
      child: AdaptiveNavigationScaffold(
        navigationItems: _navigationItems,
        currentRoute: _currentRoute,
        onNavigate: _handleNavigation,
        appBar: _buildAppBar(),
        body: _buildCurrentPage(),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  /// بناء شريط التطبيق
  Widget _buildAppBar() {
    return AdaptiveAppBar(
      title: _getPageTitle(),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadStatistics,
          tooltip: 'تحديث (Ctrl+R)',
        ),
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: _showNotifications,
          tooltip: 'الإشعارات',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: ListTile(
                leading: Icon(Icons.person),
                title: Text('الملف الشخصي'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('الإعدادات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: ListTile(
                leading: Icon(Icons.logout),
                title: Text('تسجيل الخروج'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء الصفحة الحالية
  Widget _buildCurrentPage() {
    switch (_currentRoute) {
      case '/dashboard':
        return _buildDashboard();
      case '/subscribers':
        return const SubscribersPage();
      case '/villages':
        return const VillagesPage();
      case '/meters':
        return const MetersPage();
      case '/readings':
        return const ReadingsPage();
      case '/billing':
        return const BillsPage();
      case '/payments':
        return const PaymentsPage();
      case '/reports':
        return const ReportsPage();
      case '/settings':
        return _buildPlaceholderPage('الإعدادات', Icons.settings);
      default:
        return _buildDashboard();
    }
  }

  /// بناء لوحة التحكم
  Widget _buildDashboard() {
    if (_isLoading) {
      return const AdaptiveLoadingIndicator(
        message: 'جاري تحميل البيانات...',
      );
    }

    return AdaptiveLayoutBuilder(
      builder: (context, layoutType) {
        switch (layoutType) {
          case LayoutType.mobile:
            return _buildMobileDashboard();
          case LayoutType.tablet:
            return _buildTabletDashboard();
          case LayoutType.desktop:
          case LayoutType.largeDesktop:
            return _buildDesktopDashboard();
        }
      },
    );
  }

  /// لوحة التحكم للهاتف المحمول
  Widget _buildMobileDashboard() {
    return AdaptiveListView(
      children: [
        _buildWelcomeCard(),
        const SizedBox(height: 16),
        _buildStatisticsCards(),
        const SizedBox(height: 16),
        _buildQuickActions(),
      ],
    );
  }

  /// لوحة التحكم للجهاز اللوحي
  Widget _buildTabletDashboard() {
    return AdaptiveListView(
      children: [
        _buildWelcomeCard(),
        const SizedBox(height: 16),
        _buildStatisticsGrid(columns: 2),
        const SizedBox(height: 16),
        _buildQuickActions(),
      ],
    );
  }

  /// لوحة التحكم لسطح المكتب
  Widget _buildDesktopDashboard() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: AdaptiveListView(
            children: [
              _buildWelcomeCard(),
              const SizedBox(height: 24),
              _buildStatisticsGrid(columns: 4),
              const SizedBox(height: 24),
              _buildQuickActions(),
            ],
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          flex: 1,
          child: AdaptiveListView(
            children: [
              _buildRecentActivity(),
              const SizedBox(height: 16),
              _buildSystemStatus(),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة الترحيب
  Widget _buildWelcomeCard() {
    const currentUser = 'مدير النظام';
    
    return AdaptiveCard(
      child: Row(
        children: [
          const Icon(
            Icons.waving_hand,
            size: 32,
            color: Colors.orange,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً، $currentUser',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'أهلاً بك في نظام إدارة مشروع المياه',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    return Column(
      children: [
        _buildStatCard(
          title: 'إجمالي المشتركين',
          value: _statistics['total_subscribers']?.toString() ?? '0',
          icon: Icons.people,
          color: Colors.blue,
        ),
        const SizedBox(height: 8),
        _buildStatCard(
          title: 'المشتركين النشطين',
          value: _statistics['active_subscribers']?.toString() ?? '0',
          icon: Icons.check_circle,
          color: Colors.green,
        ),
        const SizedBox(height: 8),
        _buildStatCard(
          title: 'عدد القرى',
          value: _statistics['total_villages']?.toString() ?? '0',
          icon: Icons.location_city,
          color: Colors.orange,
        ),
        const SizedBox(height: 8),
        _buildStatCard(
          title: 'إجمالي العدادات',
          value: _statistics['total_meters']?.toString() ?? '0',
          icon: Icons.speed,
          color: Colors.purple,
        ),
      ],
    );
  }

  /// بناء شبكة الإحصائيات
  Widget _buildStatisticsGrid({required int columns}) {
    final stats = [
      {
        'title': 'إجمالي المشتركين',
        'value': _statistics['total_subscribers']?.toString() ?? '0',
        'icon': Icons.people,
        'color': Colors.blue,
      },
      {
        'title': 'المشتركين النشطين',
        'value': _statistics['active_subscribers']?.toString() ?? '0',
        'icon': Icons.check_circle,
        'color': Colors.green,
      },
      {
        'title': 'عدد القرى',
        'value': _statistics['total_villages']?.toString() ?? '0',
        'icon': Icons.location_city,
        'color': Colors.orange,
      },
      {
        'title': 'إجمالي العدادات',
        'value': _statistics['total_meters']?.toString() ?? '0',
        'icon': Icons.speed,
        'color': Colors.purple,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return _buildStatCard(
          title: stat['title'] as String,
          value: stat['value'] as String,
          icon: stat['icon'] as IconData,
          color: stat['color'] as Color,
        );
      },
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return AdaptiveCard(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 32,
            color: color,
          ),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Flexible(
            child: Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return AdaptiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإجراءات السريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              _buildActionButton(
                title: 'إضافة مشترك',
                icon: Icons.person_add,
                onTap: _addNewSubscriber,
              ),
              _buildActionButton(
                title: 'تسجيل قراءة',
                icon: Icons.add_chart,
                onTap: _addNewReading,
              ),
              _buildActionButton(
                title: 'إنشاء فاتورة',
                icon: Icons.receipt_long,
                onTap: _createBill,
              ),
              _buildActionButton(
                title: 'عرض التقارير',
                icon: Icons.assessment,
                onTap: _viewReports,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      width: AdaptiveLayout.isDesktop(context) ? 200 : 150,
      child: ElevatedButton.icon(
        onPressed: onTap,
        icon: Icon(icon),
        label: Text(title),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  /// بناء النشاط الأخير (لسطح المكتب)
  Widget _buildRecentActivity() {
    return AdaptiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'النشاط الأخير',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) => ListTile(
              leading: const Icon(Icons.history, size: 20),
              title: Text(
                'نشاط ${index + 1}',
                style: const TextStyle(fontSize: 14),
              ),
              subtitle: Text(
                'منذ ${index + 1} ساعة',
                style: const TextStyle(fontSize: 12),
              ),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة النظام (لسطح المكتب)
  Widget _buildSystemStatus() {
    return AdaptiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حالة النظام',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildStatusItem('قاعدة البيانات', true),
          _buildStatusItem('النسخ الاحتياطي', true),
          _buildStatusItem('التحديثات', false),
        ],
      ),
    );
  }

  /// بناء عنصر حالة
  Widget _buildStatusItem(String label, bool isOnline) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isOnline ? Icons.check_circle : Icons.warning,
            color: isOnline ? Colors.green : Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Text(
            isOnline ? 'متصل' : 'تحذير',
            style: TextStyle(
              fontSize: 12,
              color: isOnline ? Colors.green : Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صفحة مؤقتة
  Widget _buildPlaceholderPage(String title, IconData icon) {
    return AdaptiveEmptyState(
      icon: icon,
      title: title,
      subtitle: 'هذه الصفحة قيد التطوير',
      action: ElevatedButton(
        onPressed: () => setState(() => _currentRoute = '/dashboard'),
        child: const Text('العودة للوحة التحكم'),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget? _buildFloatingActionButton() {
    if (_currentRoute == '/subscribers') {
      return AdaptiveFAB(
        onPressed: _addNewSubscriber,
        extended: AdaptiveLayout.isDesktop(context),
        tooltip: 'إضافة مشترك جديد (Ctrl+N)',
        child: AdaptiveLayout.isDesktop(context)
            ? const Text('إضافة مشترك')
            : const Icon(Icons.add),
      );
    }
    return null;
  }

  /// الحصول على عنوان الصفحة
  String _getPageTitle() {
    switch (_currentRoute) {
      case '/dashboard':
        return 'لوحة التحكم';
      case '/subscribers':
        return 'إدارة المشتركين';
      case '/villages':
        return 'إدارة القرى';
      case '/meters':
        return 'إدارة العدادات';
      case '/readings':
        return 'القراءات';
      case '/billing':
        return 'الفوترة';
      case '/payments':
        return 'المدفوعات';
      case '/reports':
        return 'التقارير';
      case '/settings':
        return 'الإعدادات';
      default:
        return AppConstants.appName;
    }
  }

  /// معالجة التنقل
  void _handleNavigation(String route) {
    setState(() {
      _currentRoute = route;
    });
  }

  /// تحميل الإحصائيات
  Future<void> _loadStatistics() async {
    setState(() => _isLoading = true);

    try {
      final stats = await DatabaseService.instance.getGeneralStatistics();
      setState(() {
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorMessage('خطأ في تحميل الإحصائيات: $e');
    }
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'profile':
        _showProfile();
        break;
      case 'settings':
        setState(() => _currentRoute = '/settings');
        break;
      case 'logout':
        _logout();
        break;
    }
  }

  /// عرض الإشعارات
  void _showNotifications() {
    AdaptiveSidePanel.show(
      context: context,
      title: 'الإشعارات',
      child: const Center(
        child: Text('لا توجد إشعارات جديدة'),
      ),
    );
  }

  /// عرض الملف الشخصي
  void _showProfile() {
    AdaptiveDialog.show(
      context: context,
      title: 'الملف الشخصي',
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: Theme.of(context).primaryColor,
            child: const Icon(Icons.person, size: 40, color: Colors.white),
          ),
          const SizedBox(height: 16),
          Text(
            'مدير النظام',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          Text(
            '<EMAIL>',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'الدور: مدير',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }

  /// إضافة مشترك جديد
  void _addNewSubscriber() {
    setState(() => _currentRoute = '/subscribers');
    _showInfoMessage('تم الانتقال لصفحة المشتركين');
  }

  /// إضافة قراءة جديدة
  void _addNewReading() {
    _showInfoMessage('سيتم تنفيذ إضافة قراءة قريباً');
  }

  /// إنشاء فاتورة
  void _createBill() {
    _showInfoMessage('سيتم تنفيذ إنشاء الفاتورة قريباً');
  }

  /// عرض التقارير
  void _viewReports() {
    setState(() => _currentRoute = '/reports');
  }

  /// تسجيل الخروج
  Future<void> _logout() async {
    final confirmed = await _showLogoutConfirmation();
    if (confirmed) {
      await LocalAuthService.instance.logout();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const LoginPage(),
          ),
        );
      }
    }
  }

  /// تأكيد تسجيل الخروج
  Future<bool> _showLogoutConfirmation() async {
    return await AdaptiveDialog.show<bool>(
      context: context,
      title: 'تأكيد تسجيل الخروج',
      content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('تسجيل الخروج'),
        ),
      ],
    ) ?? false;
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة معلومات
  void _showInfoMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
