# 🔒 النظام المحلي والأمان - تطبيق إدارة المياه

## 🏠 مميزات النظام المحلي (Offline-First)

### المزايا الأساسية
- **عدم الحاجة للإنترنت** في العمليات اليومية
- **سرعة عالية** في الاستجابة والأداء
- **أمان البيانات** محلياً دون تسريب خارجي
- **استقلالية كاملة** عن الخوادم الخارجية
- **تكلفة تشغيل منخفضة** (لا توجد رسوم اشتراك)

### التحديات والحلول
| التحدي | الحل المقترح |
|---------|---------------|
| مشاركة البيانات بين الأجهزة | نظام تصدير/استيراد + شبكة محلية |
| النسخ الاحتياطية | نسخ تلقائية محلية + تصدير يدوي |
| التحديثات | تحديثات التطبيق عبر ملفات محلية |
| التقارير المركزية | تجميع البيانات من عدة أجهزة |

---

## 🗄️ هيكل قاعدة البيانات المحلية

### إعداد SQLite مع Drift
```dart
// core/database/app_database.dart
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'dart:io';

// تعريف الجداول
part 'app_database.g.dart';

// جدول المستخدمين
class Users extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get username => text().withLength(min: 3, max: 50).unique()();
  TextColumn get email => text().withLength(max: 100).unique()();
  TextColumn get passwordHash => text().withLength(max: 255)();
  TextColumn get fullName => text().withLength(max: 100)();
  TextColumn get phone => text().withLength(max: 20).nullable()();
  TextColumn get role => textEnum<UserRole>()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  BoolColumn get twoFactorEnabled => boolean().withDefault(const Constant(false))();
  DateTimeColumn get lastLogin => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول القرى
class Villages extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(max: 100)();
  TextColumn get code => text().withLength(max: 20).unique()();
  TextColumn get mainMeterId => text().withLength(max: 50).nullable()();
  IntColumn get population => integer().nullable()();
  TextColumn get description => text().nullable()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// جدول المشتركين
class Subscribers extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get subscriberNumber => text().withLength(max: 20).unique()();
  TextColumn get fullName => text().withLength(max: 100)();
  IntColumn get villageId => integer().references(Villages, #id)();
  TextColumn get subscriptionType => textEnum<SubscriptionType>()();
  TextColumn get status => textEnum<SubscriptionStatus>()
      .withDefault(const Constant('active'))();
  TextColumn get phone => text().withLength(max: 20).nullable()();
  TextColumn get email => text().withLength(max: 100).nullable()();
  TextColumn get address => text().nullable()();
  TextColumn get nationalId => text().withLength(max: 20).nullable()();
  DateTimeColumn get connectionDate => dateTime().nullable()();
  TextColumn get notes => text().nullable()();
  RealColumn get discountPercentage => real().withDefault(const Constant(0))();
  BoolColumn get isCharitable => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// تعريف الأنواع
enum UserRole {
  admin, manager, accountant, collector, technician, financialReviewer, warehouseKeeper, subscriber
}

enum SubscriptionType {
  residential, commercial, industrial, charitable, governmental
}

enum SubscriptionStatus {
  active, suspended, overdue, disconnected
}

@DriftDatabase(tables: [Users, Villages, Subscribers])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
      await _insertDefaultData();
    },
    onUpgrade: (Migrator m, int from, int to) async {
      // منطق ترقية قاعدة البيانات
    },
  );

  // إدراج البيانات الافتراضية
  Future<void> _insertDefaultData() async {
    // إنشاء مستخدم المدير الافتراضي
    await into(users).insert(UsersCompanion.insert(
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: _hashPassword('admin123'), // يجب تغييره في الإنتاج
      fullName: 'مدير النظام',
      role: UserRole.admin,
    ));

    // إنشاء قرية افتراضية
    await into(villages).insert(VillagesCompanion.insert(
      name: 'القرية الرئيسية',
      code: 'MAIN001',
    ));
  }

  String _hashPassword(String password) {
    // تنفيذ تشفير كلمة المرور
    return password; // مؤقت - يجب استخدام تشفير حقيقي
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'water_management.db'));
    return NativeDatabase(file);
  });
}
```

---

## 🔐 نظام المصادقة المحلي

### خدمة المصادقة المحلية
```dart
// core/services/local_auth_service.dart
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';

class LocalAuthService {
  static final LocalAuthService instance = LocalAuthService._init();
  LocalAuthService._init();

  late SharedPreferences _prefs;
  late Encrypter _encrypter;
  String? _currentUserId;
  String? _currentUserRole;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    
    // إنشاء مفتاح التشفير
    final key = Key.fromSecureRandom(32);
    final iv = IV.fromSecureRandom(16);
    _encrypter = Encrypter(AES(key));
    
    // حفظ المفاتيح بشكل آمن
    await _saveEncryptionKeys(key, iv);
  }

  // تسجيل الدخول
  Future<AuthResult> login(String username, String password) async {
    try {
      final database = AppDatabase();
      
      // البحث عن المستخدم
      final user = await (database.select(database.users)
        ..where((u) => u.username.equals(username) & u.isActive.equals(true)))
        .getSingleOrNull();

      if (user == null) {
        return AuthResult.failure('اسم المستخدم غير موجود');
      }

      // التحقق من كلمة المرور
      if (!_verifyPassword(password, user.passwordHash)) {
        await _recordFailedLogin(user.id);
        return AuthResult.failure('كلمة المرور غير صحيحة');
      }

      // تحديث آخر تسجيل دخول
      await database.update(database.users)
        .replace(user.copyWith(lastLogin: DateTime.now()));

      // حفظ جلسة المستخدم
      await _saveUserSession(user.id.toString(), user.role.name);
      
      _currentUserId = user.id.toString();
      _currentUserRole = user.role.name;

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('خطأ في تسجيل الدخول: $e');
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    await _prefs.remove('user_session');
    await _prefs.remove('session_token');
    _currentUserId = null;
    _currentUserRole = null;
  }

  // التحقق من الجلسة النشطة
  Future<bool> isLoggedIn() async {
    final sessionData = _prefs.getString('user_session');
    if (sessionData == null) return false;

    try {
      final decrypted = _encrypter.decrypt64(sessionData);
      final session = jsonDecode(decrypted);
      
      // التحقق من انتهاء صلاحية الجلسة
      final expiryTime = DateTime.parse(session['expiry']);
      if (DateTime.now().isAfter(expiryTime)) {
        await logout();
        return false;
      }

      _currentUserId = session['userId'];
      _currentUserRole = session['role'];
      return true;
    } catch (e) {
      await logout();
      return false;
    }
  }

  // الحصول على المستخدم الحالي
  String? get currentUserId => _currentUserId;
  String? get currentUserRole => _currentUserRole;

  // التحقق من الصلاحيات
  bool hasPermission(String permission) {
    if (_currentUserRole == null) return false;
    
    final permissions = _getRolePermissions(_currentUserRole!);
    return permissions.contains(permission);
  }

  // تشفير كلمة المرور
  String _hashPassword(String password) {
    final salt = _generateSalt();
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return '$salt:${digest.toString()}';
  }

  // التحقق من كلمة المرور
  bool _verifyPassword(String password, String hashedPassword) {
    final parts = hashedPassword.split(':');
    if (parts.length != 2) return false;
    
    final salt = parts[0];
    final hash = parts[1];
    
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    
    return digest.toString() == hash;
  }

  // إنشاء ملح عشوائي
  String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  // حفظ جلسة المستخدم
  Future<void> _saveUserSession(String userId, String role) async {
    final sessionData = {
      'userId': userId,
      'role': role,
      'loginTime': DateTime.now().toIso8601String(),
      'expiry': DateTime.now().add(Duration(hours: 8)).toIso8601String(),
    };
    
    final encrypted = _encrypter.encrypt(jsonEncode(sessionData));
    await _prefs.setString('user_session', encrypted.base64);
  }

  // حفظ مفاتيح التشفير
  Future<void> _saveEncryptionKeys(Key key, IV iv) async {
    // في التطبيق الحقيقي، يجب حفظ المفاتيح بشكل أكثر أماناً
    await _prefs.setString('encryption_key', key.base64);
    await _prefs.setString('encryption_iv', iv.base64);
  }

  // تسجيل محاولات الدخول الفاشلة
  Future<void> _recordFailedLogin(int userId) async {
    // تنفيذ منطق تسجيل المحاولات الفاشلة
    // يمكن إضافة قفل الحساب بعد عدد معين من المحاولات
  }

  // الحصول على صلاحيات الدور
  List<String> _getRolePermissions(String role) {
    switch (role) {
      case 'admin':
        return ['*']; // جميع الصلاحيات
      case 'manager':
        return ['view_reports', 'manage_users', 'manage_subscribers', 'manage_billing'];
      case 'accountant':
        return ['view_reports', 'manage_billing', 'manage_payments', 'manage_expenses'];
      case 'collector':
        return ['view_subscribers', 'record_payments', 'view_bills'];
      case 'technician':
        return ['view_maintenance', 'update_maintenance', 'view_meters'];
      default:
        return [];
    }
  }
}

// نتيجة المصادقة
class AuthResult {
  final bool isSuccess;
  final String? message;
  final User? user;

  AuthResult._(this.isSuccess, this.message, this.user);

  factory AuthResult.success(User user) => AuthResult._(true, null, user);
  factory AuthResult.failure(String message) => AuthResult._(false, message, null);
}
```

---

## 💾 نظام النسخ الاحتياطية المحلية

### خدمة النسخ الاحتياطية
```dart
// core/services/backup_service.dart
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:archive/archive_io.dart';

class BackupService {
  static final BackupService instance = BackupService._init();
  BackupService._init();

  // إنشاء نسخة احتياطية
  Future<BackupResult> createBackup({String? customPath}) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupName = 'water_backup_$timestamp';
      
      // تحديد مسار النسخة الاحتياطية
      final backupDir = customPath ?? await _getDefaultBackupPath();
      final backupPath = '$backupDir/$backupName';
      
      // إنشاء مجلد النسخة الاحتياطية
      final backupFolder = Directory(backupPath);
      await backupFolder.create(recursive: true);
      
      // نسخ قاعدة البيانات
      await _backupDatabase(backupPath);
      
      // نسخ الملفات المرفقة
      await _backupAttachments(backupPath);
      
      // إنشاء ملف معلومات النسخة الاحتياطية
      await _createBackupInfo(backupPath);
      
      // ضغط النسخة الاحتياطية
      final zipPath = await _compressBackup(backupPath);
      
      // حذف المجلد المؤقت
      await backupFolder.delete(recursive: true);
      
      // تسجيل النسخة الاحتياطية في قاعدة البيانات
      await _logBackup(zipPath, await File(zipPath).length());
      
      return BackupResult.success(zipPath);
    } catch (e) {
      return BackupResult.failure('فشل في إنشاء النسخة الاحتياطية: $e');
    }
  }

  // استعادة نسخة احتياطية
  Future<RestoreResult> restoreBackup(String backupPath) async {
    try {
      // التحقق من وجود الملف
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        return RestoreResult.failure('ملف النسخة الاحتياطية غير موجود');
      }
      
      // إنشاء مجلد مؤقت للاستخراج
      final tempDir = await getTemporaryDirectory();
      final extractPath = '${tempDir.path}/restore_${DateTime.now().millisecondsSinceEpoch}';
      
      // استخراج النسخة الاحتياطية
      await _extractBackup(backupPath, extractPath);
      
      // التحقق من صحة النسخة الاحتياطية
      final isValid = await _validateBackup(extractPath);
      if (!isValid) {
        return RestoreResult.failure('النسخة الاحتياطية تالفة أو غير صالحة');
      }
      
      // إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
      await createBackup();
      
      // استعادة قاعدة البيانات
      await _restoreDatabase(extractPath);
      
      // استعادة الملفات المرفقة
      await _restoreAttachments(extractPath);
      
      // تنظيف المجلد المؤقت
      await Directory(extractPath).delete(recursive: true);
      
      return RestoreResult.success();
    } catch (e) {
      return RestoreResult.failure('فشل في استعادة النسخة الاحتياطية: $e');
    }
  }

  // النسخ الاحتياطية التلقائية
  Future<void> scheduleAutoBackup() async {
    // تنفيذ النسخ الاحتياطية التلقائية
    // يمكن استخدام مؤقت أو خدمة في الخلفية
  }

  // الحصول على قائمة النسخ الاحتياطية
  Future<List<BackupInfo>> getBackupHistory() async {
    final database = AppDatabase();
    // استعلام قاعدة البيانات للحصول على تاريخ النسخ الاحتياطية
    return [];
  }

  // طرق مساعدة خاصة
  Future<String> _getDefaultBackupPath() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    return '${documentsDir.path}/backups';
  }

  Future<void> _backupDatabase(String backupPath) async {
    final dbPath = await _getDatabasePath();
    final dbFile = File(dbPath);
    final backupDbPath = '$backupPath/database.db';
    await dbFile.copy(backupDbPath);
  }

  Future<void> _backupAttachments(String backupPath) async {
    // نسخ جميع الملفات المرفقة (صور، مستندات، إلخ)
    final attachmentsDir = await _getAttachmentsDirectory();
    if (await attachmentsDir.exists()) {
      final backupAttachmentsPath = '$backupPath/attachments';
      await _copyDirectory(attachmentsDir.path, backupAttachmentsPath);
    }
  }

  Future<void> _createBackupInfo(String backupPath) async {
    final backupInfo = {
      'version': '1.0.0',
      'created_at': DateTime.now().toIso8601String(),
      'app_version': '1.0.0', // يجب الحصول عليها من package_info
      'database_version': 1,
      'total_subscribers': await _getSubscribersCount(),
      'total_bills': await _getBillsCount(),
    };
    
    final infoFile = File('$backupPath/backup_info.json');
    await infoFile.writeAsString(jsonEncode(backupInfo));
  }

  Future<String> _compressBackup(String backupPath) async {
    final encoder = ZipFileEncoder();
    final zipPath = '$backupPath.zip';
    encoder.create(zipPath);
    encoder.addDirectory(Directory(backupPath));
    encoder.close();
    return zipPath;
  }

  Future<void> _extractBackup(String zipPath, String extractPath) async {
    final bytes = File(zipPath).readAsBytesSync();
    final archive = ZipDecoder().decodeBytes(bytes);
    
    for (final file in archive) {
      final filename = file.name;
      if (file.isFile) {
        final data = file.content as List<int>;
        File('$extractPath/$filename')
          ..createSync(recursive: true)
          ..writeAsBytesSync(data);
      } else {
        Directory('$extractPath/$filename').create(recursive: true);
      }
    }
  }

  Future<bool> _validateBackup(String extractPath) async {
    // التحقق من وجود الملفات الأساسية
    final dbFile = File('$extractPath/database.db');
    final infoFile = File('$extractPath/backup_info.json');
    
    return await dbFile.exists() && await infoFile.exists();
  }

  Future<void> _restoreDatabase(String extractPath) async {
    final backupDbPath = '$extractPath/database.db';
    final currentDbPath = await _getDatabasePath();
    
    // إغلاق الاتصال الحالي بقاعدة البيانات
    // نسخ قاعدة البيانات المستعادة
    await File(backupDbPath).copy(currentDbPath);
  }

  Future<void> _restoreAttachments(String extractPath) async {
    final backupAttachmentsPath = '$extractPath/attachments';
    final backupAttachmentsDir = Directory(backupAttachmentsPath);
    
    if (await backupAttachmentsDir.exists()) {
      final currentAttachmentsDir = await _getAttachmentsDirectory();
      await _copyDirectory(backupAttachmentsPath, currentAttachmentsDir.path);
    }
  }

  Future<String> _getDatabasePath() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    return '${documentsDir.path}/water_management.db';
  }

  Future<Directory> _getAttachmentsDirectory() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    return Directory('${documentsDir.path}/attachments');
  }

  Future<void> _copyDirectory(String sourcePath, String destinationPath) async {
    final sourceDir = Directory(sourcePath);
    final destinationDir = Directory(destinationPath);
    
    if (!await destinationDir.exists()) {
      await destinationDir.create(recursive: true);
    }
    
    await for (final entity in sourceDir.list(recursive: true)) {
      if (entity is File) {
        final relativePath = entity.path.substring(sourcePath.length);
        final newPath = destinationPath + relativePath;
        await entity.copy(newPath);
      }
    }
  }

  Future<void> _logBackup(String backupPath, int fileSize) async {
    // تسجيل النسخة الاحتياطية في قاعدة البيانات
  }

  Future<int> _getSubscribersCount() async {
    // الحصول على عدد المشتركين
    return 0;
  }

  Future<int> _getBillsCount() async {
    // الحصول على عدد الفواتير
    return 0;
  }
}

// نتائج العمليات
class BackupResult {
  final bool isSuccess;
  final String? message;
  final String? backupPath;

  BackupResult._(this.isSuccess, this.message, this.backupPath);

  factory BackupResult.success(String backupPath) => 
      BackupResult._(true, null, backupPath);
  factory BackupResult.failure(String message) => 
      BackupResult._(false, message, null);
}

class RestoreResult {
  final bool isSuccess;
  final String? message;

  RestoreResult._(this.isSuccess, this.message);

  factory RestoreResult.success() => RestoreResult._(true, null);
  factory RestoreResult.failure(String message) => RestoreResult._(false, message);
}

class BackupInfo {
  final String path;
  final DateTime createdAt;
  final int fileSize;
  final String version;

  BackupInfo({
    required this.path,
    required this.createdAt,
    required this.fileSize,
    required this.version,
  });
}
```

---

*هذا الملف يوفر أساساً قوياً للنظام المحلي مع التركيز على الأمان والموثوقية والاستقلالية عن الإنترنت.*
