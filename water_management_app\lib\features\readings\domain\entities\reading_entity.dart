import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// حالات القراءة
enum ReadingStatus {
  pending,
  confirmed,
  billed,
  disputed,
}

/// أنواع القراءة
enum ReadingType {
  regular,
  estimated,
  final_reading,
  initial,
}

/// كيان القراءة
class ReadingEntity extends Equatable {
  final int id;
  final int meterId;
  final String meterNumber;
  final int subscriberId;
  final String subscriberName;
  final String villageName;
  final double previousReading;
  final double currentReading;
  final double consumption;
  final DateTime readingDate;
  final ReadingType type;
  final ReadingStatus status;
  final String? readerName;
  final String? notes;
  final String? imageUrl;
  final bool isEstimated;
  final double? estimatedConsumption;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ReadingEntity({
    required this.id,
    required this.meterId,
    required this.meterNumber,
    required this.subscriberId,
    required this.subscriberName,
    required this.villageName,
    required this.previousReading,
    required this.currentReading,
    required this.consumption,
    required this.readingDate,
    required this.type,
    required this.status,
    this.readerName,
    this.notes,
    this.imageUrl,
    required this.isEstimated,
    this.estimatedConsumption,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        meterId,
        meterNumber,
        subscriberId,
        subscriberName,
        villageName,
        previousReading,
        currentReading,
        consumption,
        readingDate,
        type,
        status,
        readerName,
        notes,
        imageUrl,
        isEstimated,
        estimatedConsumption,
        createdAt,
        updatedAt,
      ];

  /// نسخ الكيان مع تعديل بعض الخصائص
  ReadingEntity copyWith({
    int? id,
    int? meterId,
    String? meterNumber,
    int? subscriberId,
    String? subscriberName,
    String? villageName,
    double? previousReading,
    double? currentReading,
    double? consumption,
    DateTime? readingDate,
    ReadingType? type,
    ReadingStatus? status,
    String? readerName,
    String? notes,
    String? imageUrl,
    bool? isEstimated,
    double? estimatedConsumption,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReadingEntity(
      id: id ?? this.id,
      meterId: meterId ?? this.meterId,
      meterNumber: meterNumber ?? this.meterNumber,
      subscriberId: subscriberId ?? this.subscriberId,
      subscriberName: subscriberName ?? this.subscriberName,
      villageName: villageName ?? this.villageName,
      previousReading: previousReading ?? this.previousReading,
      currentReading: currentReading ?? this.currentReading,
      consumption: consumption ?? this.consumption,
      readingDate: readingDate ?? this.readingDate,
      type: type ?? this.type,
      status: status ?? this.status,
      readerName: readerName ?? this.readerName,
      notes: notes ?? this.notes,
      imageUrl: imageUrl ?? this.imageUrl,
      isEstimated: isEstimated ?? this.isEstimated,
      estimatedConsumption: estimatedConsumption ?? this.estimatedConsumption,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'meter_id': meterId,
      'meter_number': meterNumber,
      'subscriber_id': subscriberId,
      'subscriber_name': subscriberName,
      'village_name': villageName,
      'previous_reading': previousReading,
      'current_reading': currentReading,
      'consumption': consumption,
      'reading_date': readingDate.toIso8601String(),
      'type': type.name,
      'status': status.name,
      'reader_name': readerName,
      'notes': notes,
      'image_url': imageUrl,
      'is_estimated': isEstimated,
      'estimated_consumption': estimatedConsumption,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory ReadingEntity.fromMap(Map<String, dynamic> map) {
    return ReadingEntity(
      id: map['id'] as int,
      meterId: map['meter_id'] as int,
      meterNumber: map['meter_number'] as String? ?? '',
      subscriberId: map['subscriber_id'] as int,
      subscriberName: map['subscriber_name'] as String? ?? '',
      villageName: map['village_name'] as String? ?? '',
      previousReading: (map['previous_reading'] as num?)?.toDouble() ?? 0.0,
      currentReading: (map['current_reading'] as num?)?.toDouble() ?? 0.0,
      consumption: (map['consumption'] as num?)?.toDouble() ?? 0.0,
      readingDate: DateTime.parse(map['reading_date'] as String),
      type: ReadingType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ReadingType.regular,
      ),
      status: ReadingStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ReadingStatus.pending,
      ),
      readerName: map['reader_name'] as String?,
      notes: map['notes'] as String?,
      imageUrl: map['image_url'] as String?,
      isEstimated: map['is_estimated'] as bool? ?? false,
      estimatedConsumption: (map['estimated_consumption'] as num?)?.toDouble(),
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// الحصول على نص نوع القراءة
  String get typeText {
    switch (type) {
      case ReadingType.regular:
        return 'عادية';
      case ReadingType.estimated:
        return 'تقديرية';
      case ReadingType.final_reading:
        return 'نهائية';
      case ReadingType.initial:
        return 'أولية';
    }
  }

  /// الحصول على نص حالة القراءة
  String get statusText {
    switch (status) {
      case ReadingStatus.pending:
        return 'في الانتظار';
      case ReadingStatus.confirmed:
        return 'مؤكدة';
      case ReadingStatus.billed:
        return 'مفوترة';
      case ReadingStatus.disputed:
        return 'متنازع عليها';
    }
  }

  /// الحصول على لون الحالة
  Color get statusColor {
    switch (status) {
      case ReadingStatus.pending:
        return Colors.orange;
      case ReadingStatus.confirmed:
        return Colors.green;
      case ReadingStatus.billed:
        return Colors.blue;
      case ReadingStatus.disputed:
        return Colors.red;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get statusIcon {
    switch (status) {
      case ReadingStatus.pending:
        return Icons.pending;
      case ReadingStatus.confirmed:
        return Icons.check_circle;
      case ReadingStatus.billed:
        return Icons.receipt;
      case ReadingStatus.disputed:
        return Icons.error;
    }
  }

  /// الحصول على أيقونة النوع
  IconData get typeIcon {
    switch (type) {
      case ReadingType.regular:
        return Icons.speed;
      case ReadingType.estimated:
        return Icons.calculate;
      case ReadingType.final_reading:
        return Icons.flag;
      case ReadingType.initial:
        return Icons.start;
    }
  }

  /// التحقق من صحة القراءة
  bool get isValid {
    return currentReading >= previousReading && consumption >= 0;
  }

  /// الحصول على الاستهلاك المحسوب
  double get calculatedConsumption {
    return currentReading - previousReading;
  }

  /// التحقق من وجود تناقض في الاستهلاك
  bool get hasConsumptionDiscrepancy {
    return (calculatedConsumption - consumption).abs() > 0.01;
  }

  /// الحصول على معدل الاستهلاك اليومي
  double getDailyConsumptionRate(DateTime? previousReadingDate) {
    if (previousReadingDate == null) return 0.0;
    
    final daysDifference = readingDate.difference(previousReadingDate).inDays;
    if (daysDifference <= 0) return 0.0;
    
    return consumption / daysDifference;
  }

  /// التحقق من الاستهلاك غير العادي
  bool isUnusualConsumption(double averageConsumption, {double threshold = 0.5}) {
    if (averageConsumption <= 0) return false;
    
    final deviation = (consumption - averageConsumption).abs() / averageConsumption;
    return deviation > threshold;
  }

  /// الحصول على ملخص القراءة
  String get summary {
    return '$meterNumber - $subscriberName: ${consumption.toStringAsFixed(2)} م³';
  }

  /// تحديث حالة القراءة
  ReadingEntity updateStatus(ReadingStatus newStatus) {
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  /// تأكيد القراءة
  ReadingEntity confirm({String? readerName}) {
    return copyWith(
      status: ReadingStatus.confirmed,
      readerName: readerName ?? this.readerName,
      updatedAt: DateTime.now(),
    );
  }

  /// إضافة ملاحظة
  ReadingEntity addNote(String note) {
    final existingNotes = notes ?? '';
    final newNotes = existingNotes.isEmpty 
        ? note 
        : '$existingNotes\n${DateTime.now().toString().substring(0, 16)}: $note';
    
    return copyWith(
      notes: newNotes,
      updatedAt: DateTime.now(),
    );
  }

  /// تحديث صورة القراءة
  ReadingEntity updateImage(String imageUrl) {
    return copyWith(
      imageUrl: imageUrl,
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء قراءة تقديرية
  factory ReadingEntity.estimated({
    required int meterId,
    required String meterNumber,
    required int subscriberId,
    required String subscriberName,
    required String villageName,
    required double previousReading,
    required double estimatedConsumption,
    required DateTime readingDate,
    String? readerName,
    String? notes,
  }) {
    final currentReading = previousReading + estimatedConsumption;
    
    return ReadingEntity(
      id: 0,
      meterId: meterId,
      meterNumber: meterNumber,
      subscriberId: subscriberId,
      subscriberName: subscriberName,
      villageName: villageName,
      previousReading: previousReading,
      currentReading: currentReading,
      consumption: estimatedConsumption,
      readingDate: readingDate,
      type: ReadingType.estimated,
      status: ReadingStatus.pending,
      readerName: readerName,
      notes: notes,
      isEstimated: true,
      estimatedConsumption: estimatedConsumption,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء قراءة عادية
  factory ReadingEntity.regular({
    required int meterId,
    required String meterNumber,
    required int subscriberId,
    required String subscriberName,
    required String villageName,
    required double previousReading,
    required double currentReading,
    required DateTime readingDate,
    String? readerName,
    String? notes,
    String? imageUrl,
  }) {
    final consumption = currentReading - previousReading;
    
    return ReadingEntity(
      id: 0,
      meterId: meterId,
      meterNumber: meterNumber,
      subscriberId: subscriberId,
      subscriberName: subscriberName,
      villageName: villageName,
      previousReading: previousReading,
      currentReading: currentReading,
      consumption: consumption,
      readingDate: readingDate,
      type: ReadingType.regular,
      status: ReadingStatus.pending,
      readerName: readerName,
      notes: notes,
      imageUrl: imageUrl,
      isEstimated: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// التحقق من إمكانية التعديل
  bool get canBeEdited {
    return status == ReadingStatus.pending || status == ReadingStatus.disputed;
  }

  /// التحقق من إمكانية الحذف
  bool get canBeDeleted {
    return status == ReadingStatus.pending;
  }

  /// التحقق من إمكانية الفوترة
  bool get canBeBilled {
    return status == ReadingStatus.confirmed;
  }
}
