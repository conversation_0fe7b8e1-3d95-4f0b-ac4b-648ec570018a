# 🛠️ دليل التنفيذ العملي لتطبيق إدارة المياه - Flutter

## 🚀 البدء السريع

### 1. إعداد البيئة التطويرية

#### متطلبات النظام
```bash
# تثبيت Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# التحقق من التثبيت
flutter doctor

# تفعيل دعم Desktop
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop
```

#### إنشاء المشروع
```bash
# إنشاء مشروع جديد
flutter create --org com.waterproject water_management_app
cd water_management_app

# إضافة دعم المنصات
flutter create --platforms=windows,macos,linux,android .
```

### 2. هيكل المشروع المقترح
```
lib/
├── core/
│   ├── constants/
│   │   ├── app_constants.dart
│   │   ├── api_endpoints.dart
│   │   └── database_constants.dart
│   ├── services/
│   │   ├── database_service.dart
│   │   ├── auth_service.dart
│   │   ├── notification_service.dart
│   │   └── pdf_service.dart
│   ├── utils/
│   │   ├── validators.dart
│   │   ├── formatters.dart
│   │   └── helpers.dart
│   └── theme/
│       ├── app_theme.dart
│       ├── colors.dart
│       └── text_styles.dart
├── features/
│   ├── auth/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── subscribers/
│   ├── meters/
│   ├── billing/
│   ├── payments/
│   └── reports/
├── shared/
│   ├── widgets/
│   ├── models/
│   └── repositories/
└── main.dart
```

---

## 📱 الأكواد النموذجية الأساسية

### 1. إعداد التطبيق الرئيسي (main.dart)
```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'core/theme/app_theme.dart';
import 'core/services/database_service.dart';
import 'features/auth/presentation/pages/login_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Supabase
  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY',
  );
  
  // تهيئة قاعدة البيانات المحلية
  await DatabaseService.instance.initialize();
  
  runApp(
    const ProviderScope(
      child: WaterManagementApp(),
    ),
  );
}

class WaterManagementApp extends StatelessWidget {
  const WaterManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'نظام إدارة مشروع المياه',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      
      // دعم اللغة العربية
      locale: const Locale('ar', 'SA'),
      supportedLocales: const [
        Locale('ar', 'SA'),
        Locale('en', 'US'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      
      home: const LoginPage(),
      
      // إعدادات إضافية للـ Desktop
      debugShowCheckedModeBanner: false,
    );
  }
}
```

### 2. نموذج البيانات الأساسي
```dart
// shared/models/subscriber.dart
import 'package:json_annotation/json_annotation.dart';

part 'subscriber.g.dart';

@JsonSerializable()
class Subscriber {
  final String id;
  final String subscriberNumber;
  final String fullName;
  final String villageId;
  final SubscriptionType subscriptionType;
  final SubscriptionStatus status;
  final String? phone;
  final String? email;
  final String? address;
  final DateTime connectionDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Subscriber({
    required this.id,
    required this.subscriberNumber,
    required this.fullName,
    required this.villageId,
    required this.subscriptionType,
    required this.status,
    this.phone,
    this.email,
    this.address,
    required this.connectionDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Subscriber.fromJson(Map<String, dynamic> json) =>
      _$SubscriberFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriberToJson(this);

  Subscriber copyWith({
    String? id,
    String? subscriberNumber,
    String? fullName,
    String? villageId,
    SubscriptionType? subscriptionType,
    SubscriptionStatus? status,
    String? phone,
    String? email,
    String? address,
    DateTime? connectionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Subscriber(
      id: id ?? this.id,
      subscriberNumber: subscriberNumber ?? this.subscriberNumber,
      fullName: fullName ?? this.fullName,
      villageId: villageId ?? this.villageId,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      status: status ?? this.status,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      connectionDate: connectionDate ?? this.connectionDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum SubscriptionType {
  @JsonValue('residential')
  residential,
  @JsonValue('commercial')
  commercial,
  @JsonValue('charitable')
  charitable,
}

enum SubscriptionStatus {
  @JsonValue('active')
  active,
  @JsonValue('suspended')
  suspended,
  @JsonValue('overdue')
  overdue,
}
```

### 3. خدمة قاعدة البيانات
```dart
// core/services/database_service.dart
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._init();
  static Database? _database;

  DatabaseService._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('water_management.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
      onUpgrade: _upgradeDB,
    );
  }

  Future<void> _createDB(Database db, int version) async {
    // إنشاء جدول المستخدمين
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        phone TEXT,
        role TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        two_factor_enabled INTEGER DEFAULT 0,
        last_login TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // إنشاء جدول القرى
    await db.execute('''
      CREATE TABLE villages (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        main_meter_id TEXT UNIQUE,
        latitude REAL,
        longitude REAL,
        population INTEGER,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // إنشاء جدول المشتركين
    await db.execute('''
      CREATE TABLE subscribers (
        id TEXT PRIMARY KEY,
        subscriber_number TEXT UNIQUE NOT NULL,
        full_name TEXT NOT NULL,
        village_id TEXT NOT NULL,
        subscription_type TEXT NOT NULL,
        status TEXT DEFAULT 'active',
        phone TEXT,
        email TEXT,
        address TEXT,
        national_id TEXT,
        connection_date TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (village_id) REFERENCES villages (id)
      )
    ''');

    // إنشاء جدول العدادات
    await db.execute('''
      CREATE TABLE meters (
        id TEXT PRIMARY KEY,
        serial_number TEXT UNIQUE NOT NULL,
        subscriber_id TEXT NOT NULL,
        meter_type TEXT DEFAULT 'residential',
        status TEXT DEFAULT 'active',
        installation_date TEXT,
        last_maintenance TEXT,
        latitude REAL,
        longitude REAL,
        initial_reading REAL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (subscriber_id) REFERENCES subscribers (id)
      )
    ''');

    // إنشاء جدول القراءات
    await db.execute('''
      CREATE TABLE readings (
        id TEXT PRIMARY KEY,
        meter_id TEXT NOT NULL,
        current_reading REAL NOT NULL,
        previous_reading REAL NOT NULL,
        consumption REAL NOT NULL,
        reading_date TEXT NOT NULL,
        read_by TEXT NOT NULL,
        reading_method TEXT NOT NULL,
        is_validated INTEGER DEFAULT 0,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (meter_id) REFERENCES meters (id)
      )
    ''');

    // إنشاء الفهارس
    await db.execute('CREATE INDEX idx_subscribers_village ON subscribers(village_id)');
    await db.execute('CREATE INDEX idx_meters_subscriber ON meters(subscriber_id)');
    await db.execute('CREATE INDEX idx_readings_meter_date ON readings(meter_id, reading_date)');
  }

  Future<void> _upgradeDB(Database db, int oldVersion, int newVersion) async {
    // منطق ترقية قاعدة البيانات
    if (oldVersion < 2) {
      // إضافة أعمدة جديدة أو جداول
    }
  }

  Future<void> initialize() async {
    await database;
  }

  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
    }
  }
}
```

### 4. مزود الحالة باستخدام Riverpod
```dart
// features/subscribers/data/providers/subscriber_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../repositories/subscriber_repository.dart';
import '../../domain/entities/subscriber.dart';

// مزود المستودع
final subscriberRepositoryProvider = Provider<SubscriberRepository>((ref) {
  return SubscriberRepositoryImpl();
});

// مزود قائمة المشتركين
final subscribersProvider = FutureProvider<List<Subscriber>>((ref) async {
  final repository = ref.read(subscriberRepositoryProvider);
  return await repository.getAllSubscribers();
});

// مزود المشترك الواحد
final subscriberProvider = FutureProvider.family<Subscriber?, String>((ref, id) async {
  final repository = ref.read(subscriberRepositoryProvider);
  return await repository.getSubscriberById(id);
});

// مزود حالة النموذج
final subscriberFormProvider = StateNotifierProvider<SubscriberFormNotifier, SubscriberFormState>((ref) {
  final repository = ref.read(subscriberRepositoryProvider);
  return SubscriberFormNotifier(repository);
});

class SubscriberFormNotifier extends StateNotifier<SubscriberFormState> {
  final SubscriberRepository _repository;

  SubscriberFormNotifier(this._repository) : super(SubscriberFormState.initial());

  Future<void> saveSubscriber(Subscriber subscriber) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      if (subscriber.id.isEmpty) {
        await _repository.createSubscriber(subscriber);
      } else {
        await _repository.updateSubscriber(subscriber);
      }
      
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void resetForm() {
    state = SubscriberFormState.initial();
  }
}

class SubscriberFormState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;

  const SubscriberFormState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
  });

  factory SubscriberFormState.initial() {
    return const SubscriberFormState(
      isLoading: false,
      isSuccess: false,
      error: null,
    );
  }

  SubscriberFormState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
  }) {
    return SubscriberFormState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error ?? this.error,
    );
  }
}
```

### 5. واجهة المستخدم النموذجية
```dart
// features/subscribers/presentation/pages/subscribers_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/subscriber_provider.dart';
import '../widgets/subscriber_card.dart';
import '../widgets/add_subscriber_dialog.dart';

class SubscribersPage extends ConsumerWidget {
  const SubscribersPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subscribersAsync = ref.watch(subscribersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المشتركين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddSubscriberDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
        ],
      ),
      body: subscribersAsync.when(
        data: (subscribers) => _buildSubscribersList(subscribers),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ في تحميل البيانات',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(subscribersProvider),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddSubscriberDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSubscribersList(List<Subscriber> subscribers) {
    if (subscribers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد مشتركين مسجلين',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: subscribers.length,
      itemBuilder: (context, index) {
        return SubscriberCard(
          subscriber: subscribers[index],
          onTap: () => _navigateToSubscriberDetails(context, subscribers[index]),
        );
      },
    );
  }

  void _showAddSubscriberDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddSubscriberDialog(),
    );
  }

  void _showSearchDialog(BuildContext context) {
    // تنفيذ البحث
  }

  void _navigateToSubscriberDetails(BuildContext context, Subscriber subscriber) {
    // الانتقال لصفحة تفاصيل المشترك
  }
}
```

---

## 🎨 التصميم والثيم

### ملف الثيم الأساسي
```dart
// core/theme/app_theme.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'colors.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ),
      
      // الخطوط
      textTheme: GoogleFonts.cairoTextTheme(),
      
      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      
      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // البطاقات
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      
      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.dark,
      ),
      textTheme: GoogleFonts.cairoTextTheme(ThemeData.dark().textTheme),
      // باقي إعدادات الثيم المظلم...
    );
  }
}
```

---

## 🔧 أدوات التطوير والبناء

### سكريبت البناء للمنصات المختلفة
```bash
#!/bin/bash
# build_all_platforms.sh

echo "بناء التطبيق لجميع المنصات..."

# Android
echo "بناء Android APK..."
flutter build apk --release --split-per-abi

echo "بناء Android App Bundle..."
flutter build appbundle --release

# Windows
echo "بناء Windows..."
flutter build windows --release

# macOS
echo "بناء macOS..."
flutter build macos --release

# Linux
echo "بناء Linux..."
flutter build linux --release

echo "تم الانتهاء من البناء لجميع المنصات!"
```

### إعدادات CI/CD (GitHub Actions)
```yaml
# .github/workflows/build.yml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    - run: flutter pub get
    - run: flutter test
    - run: flutter analyze

  build-android:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
    - run: flutter pub get
    - run: flutter build apk --release

  build-windows:
    needs: test
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
    - run: flutter pub get
    - run: flutter build windows --release
```

---

*هذا الدليل يوفر نقطة انطلاق قوية لبدء تطوير تطبيق إدارة المياه باستخدام Flutter مع أفضل الممارسات والأكواد النموذجية.*
