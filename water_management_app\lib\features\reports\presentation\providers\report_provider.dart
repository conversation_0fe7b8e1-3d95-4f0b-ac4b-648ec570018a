import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/report_repository.dart';
import '../../domain/entities/report_entity.dart';

/// مزود مستودع التقارير
final reportRepositoryProvider = Provider<ReportRepository>((ref) {
  return ReportRepository();
});

/// مزود تقرير الإحصائيات العامة
final statisticsReportProvider = FutureProvider<ReportEntity>((ref) async {
  final repository = ref.read(reportRepositoryProvider);
  return await repository.generateStatisticsReport();
});

/// مزود التقرير المالي
final financialReportProvider = FutureProvider.family<ReportEntity, Map<String, DateTime>>((ref, dates) async {
  final repository = ref.read(reportRepositoryProvider);
  return await repository.generateFinancialReport(
    startDate: dates['startDate']!,
    endDate: dates['endDate']!,
  );
});

/// مزود تقرير المتأخرات
final overdueReportProvider = FutureProvider<ReportEntity>((ref) async {
  final repository = ref.read(reportRepositoryProvider);
  return await repository.generateOverdueReport();
});

/// مزود تقرير الاستهلاك
final consumptionReportProvider = FutureProvider.family<ReportEntity, Map<String, DateTime>>((ref, dates) async {
  final repository = ref.read(reportRepositoryProvider);
  return await repository.generateConsumptionReport(
    startDate: dates['startDate']!,
    endDate: dates['endDate']!,
  );
});

/// مزود حالة إنشاء التقارير
final reportGeneratorProvider = StateNotifierProvider<ReportGeneratorNotifier, ReportGeneratorState>((ref) {
  final repository = ref.read(reportRepositoryProvider);
  return ReportGeneratorNotifier(repository);
});

/// حالة مولد التقارير
class ReportGeneratorState {
  final bool isGenerating;
  final bool isSuccess;
  final String? error;
  final ReportEntity? report;
  final double progress;

  const ReportGeneratorState({
    required this.isGenerating,
    required this.isSuccess,
    this.error,
    this.report,
    required this.progress,
  });

  factory ReportGeneratorState.initial() {
    return const ReportGeneratorState(
      isGenerating: false,
      isSuccess: false,
      error: null,
      report: null,
      progress: 0.0,
    );
  }

  ReportGeneratorState copyWith({
    bool? isGenerating,
    bool? isSuccess,
    String? error,
    ReportEntity? report,
    double? progress,
  }) {
    return ReportGeneratorState(
      isGenerating: isGenerating ?? this.isGenerating,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      report: report ?? this.report,
      progress: progress ?? this.progress,
    );
  }
}

/// مدير حالة مولد التقارير
class ReportGeneratorNotifier extends StateNotifier<ReportGeneratorState> {
  final ReportRepository _repository;

  ReportGeneratorNotifier(this._repository) : super(ReportGeneratorState.initial());

  /// إنشاء تقرير الإحصائيات
  Future<void> generateStatisticsReport() async {
    state = state.copyWith(isGenerating: true, error: null, progress: 0.0);
    
    try {
      state = state.copyWith(progress: 0.3);
      final report = await _repository.generateStatisticsReport();
      state = state.copyWith(progress: 1.0);
      
      state = state.copyWith(
        isGenerating: false,
        isSuccess: true,
        report: report,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// إنشاء التقرير المالي
  Future<void> generateFinancialReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    state = state.copyWith(isGenerating: true, error: null, progress: 0.0);
    
    try {
      state = state.copyWith(progress: 0.3);
      final report = await _repository.generateFinancialReport(
        startDate: startDate,
        endDate: endDate,
      );
      state = state.copyWith(progress: 1.0);
      
      state = state.copyWith(
        isGenerating: false,
        isSuccess: true,
        report: report,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// إنشاء تقرير المتأخرات
  Future<void> generateOverdueReport() async {
    state = state.copyWith(isGenerating: true, error: null, progress: 0.0);
    
    try {
      state = state.copyWith(progress: 0.3);
      final report = await _repository.generateOverdueReport();
      state = state.copyWith(progress: 1.0);
      
      state = state.copyWith(
        isGenerating: false,
        isSuccess: true,
        report: report,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// إنشاء تقرير الاستهلاك
  Future<void> generateConsumptionReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    state = state.copyWith(isGenerating: true, error: null, progress: 0.0);
    
    try {
      state = state.copyWith(progress: 0.3);
      final report = await _repository.generateConsumptionReport(
        startDate: startDate,
        endDate: endDate,
      );
      state = state.copyWith(progress: 1.0);
      
      state = state.copyWith(
        isGenerating: false,
        isSuccess: true,
        report: report,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = ReportGeneratorState.initial();
  }

  /// تحديث التقدم
  void updateProgress(double progress) {
    state = state.copyWith(progress: progress);
  }

  /// تحديث حالة التحميل
  void setGenerating(bool isGenerating) {
    state = state.copyWith(isGenerating: isGenerating);
  }

  /// تحديث رسالة الخطأ
  void setError(String? error) {
    state = state.copyWith(error: error, isGenerating: false);
  }

  /// تحديث حالة النجاح
  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }
}

/// مزود حالة فلترة التقارير
final reportFilterProvider = StateNotifierProvider<ReportFilterNotifier, ReportFilterState>((ref) {
  return ReportFilterNotifier();
});

/// حالة فلترة التقارير
class ReportFilterState {
  final ReportType? selectedType;
  final ReportPeriod selectedPeriod;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic> customFilters;

  const ReportFilterState({
    this.selectedType,
    required this.selectedPeriod,
    required this.startDate,
    required this.endDate,
    required this.customFilters,
  });

  factory ReportFilterState.initial() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    
    return ReportFilterState(
      selectedType: null,
      selectedPeriod: ReportPeriod.monthly,
      startDate: startOfMonth,
      endDate: now,
      customFilters: {},
    );
  }

  ReportFilterState copyWith({
    ReportType? selectedType,
    ReportPeriod? selectedPeriod,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? customFilters,
  }) {
    return ReportFilterState(
      selectedType: selectedType ?? this.selectedType,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      customFilters: customFilters ?? this.customFilters,
    );
  }

  /// التحقق من صحة التواريخ
  bool get isValidDateRange {
    return startDate.isBefore(endDate);
  }

  /// الحصول على نص الفترة
  String get periodText {
    switch (selectedPeriod) {
      case ReportPeriod.daily:
        return 'يومي';
      case ReportPeriod.weekly:
        return 'أسبوعي';
      case ReportPeriod.monthly:
        return 'شهري';
      case ReportPeriod.quarterly:
        return 'ربع سنوي';
      case ReportPeriod.yearly:
        return 'سنوي';
      case ReportPeriod.custom:
        return 'فترة مخصصة';
    }
  }
}

/// مدير حالة فلترة التقارير
class ReportFilterNotifier extends StateNotifier<ReportFilterState> {
  ReportFilterNotifier() : super(ReportFilterState.initial());

  /// تحديث نوع التقرير
  void updateReportType(ReportType? type) {
    state = state.copyWith(selectedType: type);
  }

  /// تحديث فترة التقرير
  void updateReportPeriod(ReportPeriod period) {
    final now = DateTime.now();
    DateTime startDate;
    DateTime endDate = now;

    switch (period) {
      case ReportPeriod.daily:
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case ReportPeriod.weekly:
        startDate = now.subtract(Duration(days: now.weekday - 1));
        break;
      case ReportPeriod.monthly:
        startDate = DateTime(now.year, now.month, 1);
        break;
      case ReportPeriod.quarterly:
        final quarter = ((now.month - 1) ~/ 3) + 1;
        startDate = DateTime(now.year, (quarter - 1) * 3 + 1, 1);
        break;
      case ReportPeriod.yearly:
        startDate = DateTime(now.year, 1, 1);
        break;
      case ReportPeriod.custom:
        startDate = state.startDate;
        endDate = state.endDate;
        break;
    }

    state = state.copyWith(
      selectedPeriod: period,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// تحديث تاريخ البداية
  void updateStartDate(DateTime date) {
    state = state.copyWith(startDate: date);
  }

  /// تحديث تاريخ النهاية
  void updateEndDate(DateTime date) {
    state = state.copyWith(endDate: date);
  }

  /// تحديث المرشحات المخصصة
  void updateCustomFilters(Map<String, dynamic> filters) {
    state = state.copyWith(customFilters: filters);
  }

  /// إعادة تعيين المرشحات
  void resetFilters() {
    state = ReportFilterState.initial();
  }
}
