import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/subscriber_provider.dart';
import '../widgets/subscriber_card.dart';
import '../widgets/subscriber_search_bar.dart';
import '../widgets/add_subscriber_dialog.dart';
import '../../domain/entities/subscriber_entity.dart';

class SubscribersPage extends ConsumerStatefulWidget {
  const SubscribersPage({super.key});

  @override
  ConsumerState<SubscribersPage> createState() => _SubscribersPageState();
}

class _SubscribersPageState extends ConsumerState<SubscribersPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(child: _buildSubscribersList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('إدارة المشتركين'),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => ref.refresh(subscribersProvider),
          tooltip: 'تحديث',
        ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
          tooltip: 'تصفية',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('تصدير البيانات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'import',
              child: ListTile(
                leading: Icon(Icons.upload),
                title: Text('استيراد البيانات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'statistics',
              child: ListTile(
                leading: Icon(Icons.analytics),
                title: Text('الإحصائيات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في المشتركين...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          
          // مرشحات سريعة
          _buildQuickFilters(),
        ],
      ),
    );
  }

  /// بناء المرشحات السريعة
  Widget _buildQuickFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('الكل', null),
          const SizedBox(width: 8),
          _buildFilterChip('نشط', SubscriptionStatus.active),
          const SizedBox(width: 8),
          _buildFilterChip('معلق', SubscriptionStatus.suspended),
          const SizedBox(width: 8),
          _buildFilterChip('متأخر', SubscriptionStatus.overdue),
          const SizedBox(width: 8),
          _buildFilterChip('مقطوع', SubscriptionStatus.disconnected),
        ],
      ),
    );
  }

  /// بناء رقاقة المرشح
  Widget _buildFilterChip(String label, SubscriptionStatus? status) {
    final filterState = ref.watch(subscriberFilterProvider);
    final isSelected = filterState.statusFilter == status;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(subscriberFilterProvider.notifier)
            .updateStatusFilter(selected ? status : null);
      },
    );
  }

  /// بناء قائمة المشتركين
  Widget _buildSubscribersList() {
    final subscribersAsync = _searchQuery.isEmpty
        ? ref.watch(subscribersProvider)
        : ref.watch(subscriberSearchProvider(_searchQuery));

    return subscribersAsync.when(
      data: (subscribers) => _buildSubscribersListView(subscribers),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  /// بناء عرض قائمة المشتركين
  Widget _buildSubscribersListView(List<SubscriberEntity> subscribers) {
    final filterState = ref.watch(subscriberFilterProvider);
    
    // تطبيق المرشحات
    final filteredSubscribers = _applyFilters(subscribers, filterState);

    if (filteredSubscribers.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(subscribersProvider);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredSubscribers.length,
        itemBuilder: (context, index) {
          return SubscriberCard(
            subscriber: filteredSubscribers[index],
            onTap: () => _navigateToSubscriberDetails(filteredSubscribers[index]),
            onEdit: () => _editSubscriber(filteredSubscribers[index]),
            onDelete: () => _deleteSubscriber(filteredSubscribers[index]),
          );
        },
      ),
    );
  }

  /// تطبيق المرشحات
  List<SubscriberEntity> _applyFilters(
    List<SubscriberEntity> subscribers,
    SubscriberFilterState filterState,
  ) {
    var filtered = subscribers;

    if (filterState.statusFilter != null) {
      filtered = filtered.where((s) => s.status == filterState.statusFilter).toList();
    }

    if (filterState.typeFilter != null) {
      filtered = filtered.where((s) => s.subscriptionType == filterState.typeFilter).toList();
    }

    if (filterState.villageFilter != null) {
      filtered = filtered.where((s) => s.villageId == filterState.villageFilter).toList();
    }

    if (filterState.showCharitableOnly) {
      filtered = filtered.where((s) => s.isCharitable).toList();
    }

    return filtered;
  }

  /// بناء واجهة فارغة
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مشتركين',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة مشترك جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.refresh(subscribersProvider),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewSubscriber,
      tooltip: 'إضافة مشترك جديد',
      child: const Icon(Icons.add),
    );
  }

  /// تغيير نص البحث
  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });
  }

  /// مسح البحث
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  /// عرض حوار التصفية
  void _showFilterDialog() {
    // TODO: تنفيذ حوار التصفية المتقدمة
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية المشتركين'),
        content: const Text('سيتم تنفيذ التصفية المتقدمة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportData();
        break;
      case 'import':
        _importData();
        break;
      case 'statistics':
        _showStatistics();
        break;
    }
  }

  /// إضافة مشترك جديد
  void _addNewSubscriber() {
    showDialog(
      context: context,
      builder: (context) => const AddSubscriberDialog(),
    ).then((result) {
      if (result == true) {
        ref.refresh(subscribersProvider);
        _showSuccessMessage('تم إضافة المشترك بنجاح');
      }
    });
  }

  /// تعديل مشترك
  void _editSubscriber(SubscriberEntity subscriber) {
    showDialog(
      context: context,
      builder: (context) => AddSubscriberDialog(subscriber: subscriber),
    ).then((result) {
      if (result == true) {
        ref.refresh(subscribersProvider);
        _showSuccessMessage('تم تحديث المشترك بنجاح');
      }
    });
  }

  /// حذف مشترك
  void _deleteSubscriber(SubscriberEntity subscriber) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المشترك "${subscriber.fullName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(subscriberFormProvider.notifier)
                  .deleteSubscriber(subscriber.id);
              
              final state = ref.read(subscriberFormProvider);
              if (state.isSuccess) {
                ref.refresh(subscribersProvider);
                _showSuccessMessage('تم حذف المشترك بنجاح');
              } else if (state.error != null) {
                _showErrorMessage(state.error!);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// الانتقال لتفاصيل المشترك
  void _navigateToSubscriberDetails(SubscriberEntity subscriber) {
    // TODO: تنفيذ صفحة تفاصيل المشترك
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(subscriber.fullName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم المشترك: ${subscriber.subscriberNumber}'),
            Text('القرية: ${subscriber.villageName}'),
            Text('النوع: ${subscriber.subscriptionTypeText}'),
            Text('الحالة: ${subscriber.statusText}'),
            if (subscriber.phone != null) Text('الهاتف: ${subscriber.phone}'),
            if (subscriber.email != null) Text('البريد: ${subscriber.email}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// تصدير البيانات
  void _exportData() {
    // TODO: تنفيذ تصدير البيانات
    _showInfoMessage('سيتم تنفيذ تصدير البيانات قريباً');
  }

  /// استيراد البيانات
  void _importData() {
    // TODO: تنفيذ استيراد البيانات
    _showInfoMessage('سيتم تنفيذ استيراد البيانات قريباً');
  }

  /// عرض الإحصائيات
  void _showStatistics() {
    final statisticsAsync = ref.read(subscriberStatisticsProvider);
    
    statisticsAsync.when(
      data: (stats) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('إحصائيات المشتركين'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatRow('إجمالي المشتركين', stats['total'] ?? 0),
                _buildStatRow('المشتركين النشطين', stats['active'] ?? 0),
                _buildStatRow('المشتركين المعلقين', stats['suspended'] ?? 0),
                _buildStatRow('المشتركين المتأخرين', stats['overdue'] ?? 0),
                _buildStatRow('المشتركين المقطوعين', stats['disconnected'] ?? 0),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
        );
      },
      loading: () => _showInfoMessage('جاري تحميل الإحصائيات...'),
      error: (error, stack) => _showErrorMessage('خطأ في تحميل الإحصائيات'),
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(String label, int value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value.toString(),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة معلومات
  void _showInfoMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
