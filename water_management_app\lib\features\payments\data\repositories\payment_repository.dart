import 'package:drift/drift.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/services/database_service.dart';
import '../../domain/entities/payment_entity.dart';

/// مستودع المدفوعات
class PaymentRepository {
  final AppDatabase _database = DatabaseService.instance.database;

  /// الحصول على جميع المدفوعات
  Future<List<PaymentEntity>> getAllPayments() async {
    try {
      final query = _database.select(_database.payments).join([
        leftOuterJoin(
          _database.bills,
          _database.bills.id.equalsExp(_database.payments.billId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.payments.subscriberId),
        ),
      ])..orderBy([OrderingTerm.desc(_database.payments.paymentDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final payment = row.readTable(_database.payments);
        final bill = row.readTableOrNull(_database.bills);
        final subscriber = row.readTableOrNull(_database.subscribers);
        
        return _mapToEntity(
          payment,
          bill?.billNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على المدفوعات: $e');
    }
  }

  /// الحصول على دفعة بالمعرف
  Future<PaymentEntity?> getPaymentById(int id) async {
    try {
      final query = _database.select(_database.payments).join([
        leftOuterJoin(
          _database.bills,
          _database.bills.id.equalsExp(_database.payments.billId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.payments.subscriberId),
        ),
      ])..where(_database.payments.id.equals(id));

      final result = await query.getSingleOrNull();
      
      if (result == null) return null;
      
      final payment = result.readTable(_database.payments);
      final bill = result.readTableOrNull(_database.bills);
      final subscriber = result.readTableOrNull(_database.subscribers);
      
      return _mapToEntity(
        payment,
        bill?.billNumber ?? 'غير محدد',
        subscriber?.fullName ?? 'غير محدد',
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على الدفعة: $e');
    }
  }

  /// البحث في المدفوعات
  Future<List<PaymentEntity>> searchPayments(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllPayments();
      }

      final searchQuery = _database.select(_database.payments).join([
        leftOuterJoin(
          _database.bills,
          _database.bills.id.equalsExp(_database.payments.billId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.payments.subscriberId),
        ),
      ])..where(
        _database.payments.paymentNumber.like('%$query%') |
        _database.bills.billNumber.like('%$query%') |
        _database.subscribers.fullName.like('%$query%') |
        _database.payments.bankReference.like('%$query%')
      );

      final results = await searchQuery.get();
      
      return results.map((row) {
        final payment = row.readTable(_database.payments);
        final bill = row.readTableOrNull(_database.bills);
        final subscriber = row.readTableOrNull(_database.subscribers);
        
        return _mapToEntity(
          payment,
          bill?.billNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في البحث عن المدفوعات: $e');
    }
  }

  /// الحصول على المدفوعات حسب الفاتورة
  Future<List<PaymentEntity>> getPaymentsByBill(int billId) async {
    try {
      final query = _database.select(_database.payments).join([
        leftOuterJoin(
          _database.bills,
          _database.bills.id.equalsExp(_database.payments.billId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.payments.subscriberId),
        ),
      ])..where(_database.payments.billId.equals(billId))
      ..orderBy([OrderingTerm.desc(_database.payments.paymentDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final payment = row.readTable(_database.payments);
        final bill = row.readTableOrNull(_database.bills);
        final subscriber = row.readTableOrNull(_database.subscribers);
        
        return _mapToEntity(
          payment,
          bill?.billNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على مدفوعات الفاتورة: $e');
    }
  }

  /// الحصول على المدفوعات حسب المشترك
  Future<List<PaymentEntity>> getPaymentsBySubscriber(int subscriberId) async {
    try {
      final query = _database.select(_database.payments).join([
        leftOuterJoin(
          _database.bills,
          _database.bills.id.equalsExp(_database.payments.billId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.payments.subscriberId),
        ),
      ])..where(_database.payments.subscriberId.equals(subscriberId))
      ..orderBy([OrderingTerm.desc(_database.payments.paymentDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final payment = row.readTable(_database.payments);
        final bill = row.readTableOrNull(_database.bills);
        final subscriber = row.readTableOrNull(_database.subscribers);
        
        return _mapToEntity(
          payment,
          bill?.billNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على مدفوعات المشترك: $e');
    }
  }

  /// الحصول على المدفوعات المعلقة
  Future<List<PaymentEntity>> getPendingPayments() async {
    try {
      final query = _database.select(_database.payments).join([
        leftOuterJoin(
          _database.bills,
          _database.bills.id.equalsExp(_database.payments.billId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.payments.subscriberId),
        ),
      ])..where(_database.payments.status.equals('pending'))
      ..orderBy([OrderingTerm.desc(_database.payments.paymentDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final payment = row.readTable(_database.payments);
        final bill = row.readTableOrNull(_database.bills);
        final subscriber = row.readTableOrNull(_database.subscribers);
        
        return _mapToEntity(
          payment,
          bill?.billNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على المدفوعات المعلقة: $e');
    }
  }

  /// إضافة دفعة جديدة
  Future<PaymentEntity> createPayment(PaymentEntity payment) async {
    try {
      // إنشاء رقم دفعة تلقائي إذا لم يكن موجوداً
      String paymentNumber = payment.paymentNumber;
      if (paymentNumber.isEmpty) {
        paymentNumber = await generatePaymentNumber();
      }

      final id = await _database.into(_database.payments).insert(
        PaymentsCompanion.insert(
          paymentNumber: paymentNumber,
          billId: Value(payment.billId),
          subscriberId: payment.subscriberId,
          amount: payment.amount,
          paymentMethod: payment.method.name,
          status: Value(payment.status.name),
          paymentDate: payment.paymentDate,
          bankReference: Value(payment.referenceNumber),
          checkNumber: Value(payment.checkNumber),
          notes: Value(payment.notes),
          collectedBy: Value(1), // افتراضي للمستخدم الحالي
        ),
      );

      // تحديث حالة الفاتورة إذا تم الدفع بالكامل
      await _updateBillPaymentStatus(payment.billId);

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'payments',
        recordId: id,
        operation: 'INSERT',
        newValues: payment.toMap(),
      );

      return payment.copyWith(id: id, paymentNumber: paymentNumber);
    } catch (e) {
      throw Exception('خطأ في إضافة الدفعة: $e');
    }
  }

  /// تحديث دفعة
  Future<PaymentEntity> updatePayment(PaymentEntity payment) async {
    try {
      // الحصول على البيانات القديمة
      final oldPayment = await getPaymentById(payment.id);
      
      await (_database.update(_database.payments)
        ..where((p) => p.id.equals(payment.id)))
        .write(PaymentsCompanion(
          paymentNumber: Value(payment.paymentNumber),
          billId: Value(payment.billId),
          subscriberId: Value(payment.subscriberId),
          amount: Value(payment.amount),
          paymentMethod: Value(payment.method.name),
          status: Value(payment.status.name),
          paymentDate: Value(payment.paymentDate),
          bankReference: Value(payment.referenceNumber),
          checkNumber: Value(payment.checkNumber),
          notes: Value(payment.notes),
          collectedBy: Value(1), // افتراضي للمستخدم الحالي
        ));

      // تحديث حالة الفاتورة
      await _updateBillPaymentStatus(payment.billId);

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'payments',
        recordId: payment.id,
        operation: 'UPDATE',
        oldValues: oldPayment?.toMap(),
        newValues: payment.toMap(),
      );

      return payment.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      throw Exception('خطأ في تحديث الدفعة: $e');
    }
  }

  /// حذف دفعة
  Future<void> deletePayment(int id) async {
    try {
      // الحصول على البيانات قبل الحذف
      final payment = await getPaymentById(id);
      
      if (payment == null) {
        throw Exception('الدفعة غير موجودة');
      }

      await (_database.delete(_database.payments)
        ..where((p) => p.id.equals(id)))
        .go();

      // تحديث حالة الفاتورة
      await _updateBillPaymentStatus(payment.billId);

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'payments',
        recordId: id,
        operation: 'DELETE',
        oldValues: payment.toMap(),
      );
    } catch (e) {
      throw Exception('خطأ في حذف الدفعة: $e');
    }
  }

  /// تحديث حالة الدفعة
  Future<PaymentEntity> updatePaymentStatus(int paymentId, PaymentStatus newStatus) async {
    try {
      final payment = await getPaymentById(paymentId);
      if (payment == null) {
        throw Exception('الدفعة غير موجودة');
      }

      final updatedPayment = payment.updateStatus(newStatus);
      return await updatePayment(updatedPayment);
    } catch (e) {
      throw Exception('خطأ في تحديث حالة الدفعة: $e');
    }
  }

  /// الحصول على إحصائيات المدفوعات
  Future<Map<String, dynamic>> getPaymentsStatistics() async {
    try {
      final stats = <String, dynamic>{};
      
      // إجمالي المدفوعات
      stats['total'] = await _database.select(_database.payments).get()
        .then((list) => list.length);
      
      // المدفوعات المكتملة
      stats['completed'] = await (_database.select(_database.payments)
        ..where((p) => p.status.equals('completed'))).get()
        .then((list) => list.length);
      
      // المدفوعات المعلقة
      stats['pending'] = await (_database.select(_database.payments)
        ..where((p) => p.status.equals('pending'))).get()
        .then((list) => list.length);
      
      // المدفوعات الفاشلة
      stats['failed'] = await (_database.select(_database.payments)
        ..where((p) => p.status.equals('failed'))).get()
        .then((list) => list.length);
      
      // إجمالي المبالغ المحصلة
      final totalAmountResult = await _database.customSelect(
        'SELECT SUM(amount) as total FROM payments WHERE status = "completed"'
      ).getSingleOrNull();
      stats['total_amount'] = totalAmountResult?.data['total'] ?? 0.0;
      
      // المبالغ المعلقة
      final pendingAmountResult = await _database.customSelect(
        'SELECT SUM(amount) as pending FROM payments WHERE status = "pending"'
      ).getSingleOrNull();
      stats['pending_amount'] = pendingAmountResult?.data['pending'] ?? 0.0;

      return stats;
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات المدفوعات: $e');
    }
  }

  /// تحديث حالة دفع الفاتورة
  Future<void> _updateBillPaymentStatus(int billId) async {
    try {
      // حساب إجمالي المدفوعات المكتملة للفاتورة
      final completedPayments = await (_database.select(_database.payments)
        ..where((p) => p.billId.equals(billId) & p.status.equals('completed')))
        .get();

      final totalPaid = completedPayments.fold<double>(
        0.0,
        (sum, payment) => sum + payment.amount,
      );

      // الحصول على الفاتورة
      final bill = await (_database.select(_database.bills)
        ..where((b) => b.id.equals(billId)))
        .getSingleOrNull();

      if (bill != null) {
        final remainingAmount = bill.totalAmount - totalPaid;
        String newStatus = 'issued';
        
        if (remainingAmount <= 0) {
          newStatus = 'paid';
        } else if (totalPaid > 0) {
          newStatus = 'partial';
        }

        // تحديث الفاتورة
        await (_database.update(_database.bills)
          ..where((b) => b.id.equals(billId)))
          .write(BillsCompanion(
            paidAmount: Value(totalPaid),
            remainingAmount: Value(remainingAmount),
            status: Value(newStatus),
          ));
      }
    } catch (e) {
      print('خطأ في تحديث حالة الفاتورة: $e');
    }
  }

  /// تحويل من نموذج قاعدة البيانات إلى كيان
  PaymentEntity _mapToEntity(
    Payment payment,
    String billNumber,
    String subscriberName,
  ) {
    return PaymentEntity(
      id: payment.id,
      paymentNumber: payment.paymentNumber,
      billId: payment.billId ?? 0,
      billNumber: billNumber,
      subscriberId: payment.subscriberId,
      subscriberName: subscriberName,
      amount: payment.amount,
      method: PaymentMethod.values.firstWhere(
        (e) => e.name == payment.paymentMethod,
        orElse: () => PaymentMethod.cash,
      ),
      type: PaymentType.full, // افتراضي لأن الجدول لا يحتوي على هذا الحقل
      status: PaymentStatus.values.firstWhere(
        (e) => e.name == payment.status,
        orElse: () => PaymentStatus.pending,
      ),
      paymentDate: payment.paymentDate,
      referenceNumber: payment.bankReference,
      bankName: null, // غير متوفر في الجدول الحالي
      checkNumber: payment.checkNumber,
      notes: payment.notes,
      receiptPath: null, // غير متوفر في الجدول الحالي
      collectedBy: payment.collectedBy?.toString() ?? 'غير محدد',
      createdAt: payment.createdAt,
      updatedAt: payment.createdAt, // لا يوجد updatedAt في الجدول
    );
  }

  /// إنشاء رقم دفعة تلقائي
  Future<String> generatePaymentNumber() async {
    try {
      final lastPayment = await (_database.select(_database.payments)
        ..orderBy([(p) => OrderingTerm.desc(p.id)]))
        .getSingleOrNull();

      if (lastPayment == null) {
        return 'PAY001';
      }

      // استخراج الرقم من آخر دفعة وزيادته
      final lastNumber = lastPayment.paymentNumber;
      final numberPart = lastNumber.replaceAll(RegExp(r'[^0-9]'), '');
      final nextNumber = (int.tryParse(numberPart) ?? 0) + 1;
      
      return 'PAY${nextNumber.toString().padLeft(3, '0')}';
    } catch (e) {
      throw Exception('خطأ في إنشاء رقم الدفعة: $e');
    }
  }
}
