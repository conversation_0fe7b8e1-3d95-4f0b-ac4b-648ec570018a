import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../../core/adaptive/adaptive_layout.dart';
import '../providers/village_provider.dart';
import '../widgets/village_card.dart';
import '../widgets/add_village_dialog.dart';
import '../../domain/entities/village_entity.dart';

class VillagesPage extends ConsumerStatefulWidget {
  const VillagesPage({super.key});

  @override
  ConsumerState<VillagesPage> createState() => _VillagesPageState();
}

class _VillagesPageState extends ConsumerState<VillagesPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildSearchAndFilters(),
          Expanded(child: _buildVillagesList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء الرأس
  Widget _buildHeader() {
    return AdaptiveCard(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            Icons.location_city,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة القرى',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'إدارة وتنظيم القرى والمناطق في المشروع',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildStatisticsChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة الإحصائيات
  Widget _buildStatisticsChip() {
    final statisticsAsync = ref.watch(villageStatisticsProvider);
    
    return statisticsAsync.when(
      data: (stats) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          '${stats['total_villages'] ?? 0} قرية',
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      loading: () => const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
      error: (_, __) => const Icon(Icons.error, color: Colors.red),
    );
  }

  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في القرى...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          
          // مرشحات سريعة
          _buildQuickFilters(),
        ],
      ),
    );
  }

  /// بناء المرشحات السريعة
  Widget _buildQuickFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('الكل', null),
          const SizedBox(width: 8),
          _buildFilterChip('نشطة', true),
          const SizedBox(width: 8),
          _buildFilterChip('غير نشطة', false),
          const SizedBox(width: 8),
          _buildSubscribersFilterChip(),
          const SizedBox(width: 8),
          _buildMetersFilterChip(),
        ],
      ),
    );
  }

  /// بناء رقاقة المرشح
  Widget _buildFilterChip(String label, bool? isActive) {
    final filterState = ref.watch(villageFilterProvider);
    final isSelected = filterState.isActiveFilter == isActive;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        ref.read(villageFilterProvider.notifier)
            .updateActiveFilter(selected ? isActive : null);
      },
    );
  }

  /// بناء رقاقة مرشح المشتركين
  Widget _buildSubscribersFilterChip() {
    final filterState = ref.watch(villageFilterProvider);

    return FilterChip(
      label: const Text('بها مشتركين'),
      selected: filterState.showOnlyWithSubscribers,
      onSelected: (selected) {
        ref.read(villageFilterProvider.notifier)
            .updateSubscribersFilter(selected);
      },
    );
  }

  /// بناء رقاقة مرشح العدادات
  Widget _buildMetersFilterChip() {
    final filterState = ref.watch(villageFilterProvider);

    return FilterChip(
      label: const Text('بها عدادات'),
      selected: filterState.showOnlyWithMeters,
      onSelected: (selected) {
        ref.read(villageFilterProvider.notifier)
            .updateMetersFilter(selected);
      },
    );
  }

  /// بناء قائمة القرى
  Widget _buildVillagesList() {
    final villagesAsync = _searchQuery.isEmpty
        ? ref.watch(villagesProvider)
        : ref.watch(villageSearchProvider(_searchQuery));

    return villagesAsync.when(
      data: (villages) => _buildVillagesListView(villages),
      loading: () => const AdaptiveLoadingIndicator(
        message: 'جاري تحميل القرى...',
      ),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  /// بناء عرض قائمة القرى
  Widget _buildVillagesListView(List<VillageEntity> villages) {
    final filterState = ref.watch(villageFilterProvider);
    
    // تطبيق المرشحات
    final filteredVillages = _applyFilters(villages, filterState);

    if (filteredVillages.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(villagesProvider);
      },
      child: AdaptiveLayoutBuilder(
        builder: (context, layoutType) {
          if (layoutType == LayoutType.mobile) {
            return _buildMobileList(filteredVillages);
          } else {
            return _buildDesktopGrid(filteredVillages);
          }
        },
      ),
    );
  }

  /// بناء قائمة للهاتف المحمول
  Widget _buildMobileList(List<VillageEntity> villages) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: villages.length,
      itemBuilder: (context, index) {
        return VillageCard(
          village: villages[index],
          onTap: () => _navigateToVillageDetails(villages[index]),
          onEdit: () => _editVillage(villages[index]),
          onDelete: () => _deleteVillage(villages[index]),
          onToggleStatus: () => _toggleVillageStatus(villages[index]),
        );
      },
    );
  }

  /// بناء شبكة لسطح المكتب
  Widget _buildDesktopGrid(List<VillageEntity> villages) {
    final columns = AdaptiveLayout.getGridColumns(context);
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: villages.length,
      itemBuilder: (context, index) {
        return VillageCard(
          village: villages[index],
          onTap: () => _navigateToVillageDetails(villages[index]),
          onEdit: () => _editVillage(villages[index]),
          onDelete: () => _deleteVillage(villages[index]),
          onToggleStatus: () => _toggleVillageStatus(villages[index]),
        );
      },
    );
  }

  /// تطبيق المرشحات
  List<VillageEntity> _applyFilters(
    List<VillageEntity> villages,
    VillageFilterState filterState,
  ) {
    var filtered = villages;

    if (filterState.isActiveFilter != null) {
      filtered = filtered.where((v) => v.isActive == filterState.isActiveFilter).toList();
    }

    if (filterState.showOnlyWithSubscribers) {
      filtered = filtered.where((v) => v.totalSubscribers > 0).toList();
    }

    if (filterState.showOnlyWithMeters) {
      filtered = filtered.where((v) => v.totalMeters > 0).toList();
    }

    return filtered;
  }

  /// بناء واجهة فارغة
  Widget _buildEmptyWidget() {
    return AdaptiveEmptyState(
      icon: Icons.location_city,
      title: 'لا توجد قرى',
      subtitle: 'اضغط على زر + لإضافة قرية جديدة',
      action: ElevatedButton.icon(
        onPressed: _addNewVillage,
        icon: const Icon(Icons.add),
        label: const Text('إضافة قرية'),
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget(String error) {
    return AdaptiveEmptyState(
      icon: Icons.error_outline,
      title: 'حدث خطأ في تحميل البيانات',
      subtitle: error,
      action: ElevatedButton(
        onPressed: () => ref.refresh(villagesProvider),
        child: const Text('إعادة المحاولة'),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewVillage,
      tooltip: 'إضافة قرية جديدة',
      child: const Icon(Icons.add),
    );
  }

  /// تغيير نص البحث
  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });
  }

  /// مسح البحث
  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  /// إضافة قرية جديدة
  void _addNewVillage() {
    showDialog(
      context: context,
      builder: (context) => const AddVillageDialog(),
    ).then((result) {
      if (result == true) {
        ref.refresh(villagesProvider);
        _showSuccessMessage('تم إضافة القرية بنجاح');
      }
    });
  }

  /// تعديل قرية
  void _editVillage(VillageEntity village) {
    showDialog(
      context: context,
      builder: (context) => AddVillageDialog(village: village),
    ).then((result) {
      if (result == true) {
        ref.refresh(villagesProvider);
        _showSuccessMessage('تم تحديث القرية بنجاح');
      }
    });
  }

  /// حذف قرية
  void _deleteVillage(VillageEntity village) {
    AdaptiveDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      content: Text('هل أنت متأكد من حذف القرية "${village.name}"؟'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            await ref.read(villageFormProvider.notifier)
                .deleteVillage(village.id);
            
            final state = ref.read(villageFormProvider);
            if (state.isSuccess) {
              ref.refresh(villagesProvider);
              _showSuccessMessage('تم حذف القرية بنجاح');
            } else if (state.error != null) {
              _showErrorMessage(state.error!);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('حذف'),
        ),
      ],
    );
  }

  /// تفعيل/إلغاء تفعيل قرية
  void _toggleVillageStatus(VillageEntity village) async {
    await ref.read(villageFormProvider.notifier)
        .toggleVillageStatus(village.id);
    
    final state = ref.read(villageFormProvider);
    if (state.isSuccess) {
      ref.refresh(villagesProvider);
      _showSuccessMessage(
        village.isActive ? 'تم إلغاء تفعيل القرية' : 'تم تفعيل القرية'
      );
    } else if (state.error != null) {
      _showErrorMessage(state.error!);
    }
  }

  /// الانتقال لتفاصيل القرية
  void _navigateToVillageDetails(VillageEntity village) {
    AdaptiveDialog.show(
      context: context,
      title: village.name,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow('الوصف', village.description ?? 'غير محدد'),
          _buildDetailRow('الموقع', village.locationInfo),
          _buildDetailRow('إجمالي المشتركين', village.totalSubscribers.toString()),
          _buildDetailRow('المشتركين النشطين', village.activeSubscribers.toString()),
          _buildDetailRow('إجمالي العدادات', village.totalMeters.toString()),
          _buildDetailRow('الحالة', village.statusText),
          if (village.contactInfo != null)
            _buildDetailRow('معلومات الاتصال', village.contactInfo!),
          if (village.notes != null)
            _buildDetailRow('ملاحظات', village.notes!),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            _editVillage(village);
          },
          child: const Text('تعديل'),
        ),
      ],
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
