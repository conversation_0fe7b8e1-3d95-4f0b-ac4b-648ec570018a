import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// أنواع التقارير
enum ReportType {
  subscribers,
  meters,
  readings,
  bills,
  payments,
  villages,
  financial,
  consumption,
  overdue,
  statistics,
}

/// فترات التقرير
enum ReportPeriod {
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
  custom,
}

/// تنسيقات التصدير
enum ExportFormat {
  pdf,
  excel,
  csv,
  json,
}

/// كيان التقرير
class ReportEntity extends Equatable {
  final String id;
  final String title;
  final String description;
  final ReportType type;
  final ReportPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic> filters;
  final Map<String, dynamic> data;
  final DateTime generatedAt;
  final String generatedBy;

  const ReportEntity({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.filters,
    required this.data,
    required this.generatedAt,
    required this.generatedBy,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        period,
        startDate,
        endDate,
        filters,
        data,
        generatedAt,
        generatedBy,
      ];

  /// نسخ الكيان مع تعديل بعض الخصائص
  ReportEntity copyWith({
    String? id,
    String? title,
    String? description,
    ReportType? type,
    ReportPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? data,
    DateTime? generatedAt,
    String? generatedBy,
  }) {
    return ReportEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      filters: filters ?? this.filters,
      data: data ?? this.data,
      generatedAt: generatedAt ?? this.generatedAt,
      generatedBy: generatedBy ?? this.generatedBy,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'period': period.name,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'filters': filters,
      'data': data,
      'generated_at': generatedAt.toIso8601String(),
      'generated_by': generatedBy,
    };
  }

  /// إنشاء من Map
  factory ReportEntity.fromMap(Map<String, dynamic> map) {
    return ReportEntity(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      type: ReportType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ReportType.statistics,
      ),
      period: ReportPeriod.values.firstWhere(
        (e) => e.name == map['period'],
        orElse: () => ReportPeriod.monthly,
      ),
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      filters: Map<String, dynamic>.from(map['filters'] as Map),
      data: Map<String, dynamic>.from(map['data'] as Map),
      generatedAt: DateTime.parse(map['generated_at'] as String),
      generatedBy: map['generated_by'] as String,
    );
  }

  /// الحصول على نص نوع التقرير
  String get typeText {
    switch (type) {
      case ReportType.subscribers:
        return 'تقرير المشتركين';
      case ReportType.meters:
        return 'تقرير العدادات';
      case ReportType.readings:
        return 'تقرير القراءات';
      case ReportType.bills:
        return 'تقرير الفواتير';
      case ReportType.payments:
        return 'تقرير المدفوعات';
      case ReportType.villages:
        return 'تقرير القرى';
      case ReportType.financial:
        return 'التقرير المالي';
      case ReportType.consumption:
        return 'تقرير الاستهلاك';
      case ReportType.overdue:
        return 'تقرير المتأخرات';
      case ReportType.statistics:
        return 'تقرير الإحصائيات';
    }
  }

  /// الحصول على نص فترة التقرير
  String get periodText {
    switch (period) {
      case ReportPeriod.daily:
        return 'يومي';
      case ReportPeriod.weekly:
        return 'أسبوعي';
      case ReportPeriod.monthly:
        return 'شهري';
      case ReportPeriod.quarterly:
        return 'ربع سنوي';
      case ReportPeriod.yearly:
        return 'سنوي';
      case ReportPeriod.custom:
        return 'فترة مخصصة';
    }
  }

  /// الحصول على أيقونة نوع التقرير
  IconData get typeIcon {
    switch (type) {
      case ReportType.subscribers:
        return Icons.people;
      case ReportType.meters:
        return Icons.speed;
      case ReportType.readings:
        return Icons.analytics;
      case ReportType.bills:
        return Icons.receipt_long;
      case ReportType.payments:
        return Icons.payment;
      case ReportType.villages:
        return Icons.location_city;
      case ReportType.financial:
        return Icons.account_balance;
      case ReportType.consumption:
        return Icons.water_drop;
      case ReportType.overdue:
        return Icons.warning;
      case ReportType.statistics:
        return Icons.bar_chart;
    }
  }

  /// الحصول على لون نوع التقرير
  Color get typeColor {
    switch (type) {
      case ReportType.subscribers:
        return Colors.blue;
      case ReportType.meters:
        return Colors.green;
      case ReportType.readings:
        return Colors.purple;
      case ReportType.bills:
        return Colors.orange;
      case ReportType.payments:
        return Colors.teal;
      case ReportType.villages:
        return Colors.brown;
      case ReportType.financial:
        return Colors.indigo;
      case ReportType.consumption:
        return Colors.cyan;
      case ReportType.overdue:
        return Colors.red;
      case ReportType.statistics:
        return Colors.pink;
    }
  }

  /// الحصول على ملخص التقرير
  String get summary {
    final period = '${startDate.day}/${startDate.month}/${startDate.year} - ${endDate.day}/${endDate.month}/${endDate.year}';
    return '$typeText ($period)';
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return title.isNotEmpty &&
        description.isNotEmpty &&
        startDate.isBefore(endDate) &&
        generatedBy.isNotEmpty;
  }

  /// الحصول على حجم البيانات
  int get dataSize {
    return data.length;
  }

  /// التحقق من وجود بيانات
  bool get hasData {
    return data.isNotEmpty;
  }

  /// إنشاء تقرير جديد
  factory ReportEntity.create({
    required String title,
    required String description,
    required ReportType type,
    required ReportPeriod period,
    required DateTime startDate,
    required DateTime endDate,
    required String generatedBy,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? data,
  }) {
    return ReportEntity(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      type: type,
      period: period,
      startDate: startDate,
      endDate: endDate,
      filters: filters ?? {},
      data: data ?? {},
      generatedAt: DateTime.now(),
      generatedBy: generatedBy,
    );
  }

  /// إنشاء تقرير إحصائيات سريع
  factory ReportEntity.quickStats({
    required String generatedBy,
    Map<String, dynamic>? data,
  }) {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    
    return ReportEntity.create(
      title: 'إحصائيات سريعة',
      description: 'تقرير الإحصائيات العامة للشهر الحالي',
      type: ReportType.statistics,
      period: ReportPeriod.monthly,
      startDate: startOfMonth,
      endDate: now,
      generatedBy: generatedBy,
      data: data,
    );
  }

  /// إنشاء تقرير مالي
  factory ReportEntity.financial({
    required DateTime startDate,
    required DateTime endDate,
    required String generatedBy,
    Map<String, dynamic>? data,
  }) {
    return ReportEntity.create(
      title: 'التقرير المالي',
      description: 'تقرير شامل للوضع المالي والمدفوعات',
      type: ReportType.financial,
      period: ReportPeriod.custom,
      startDate: startDate,
      endDate: endDate,
      generatedBy: generatedBy,
      data: data,
    );
  }

  /// إنشاء تقرير المتأخرات
  factory ReportEntity.overdue({
    required String generatedBy,
    Map<String, dynamic>? data,
  }) {
    final now = DateTime.now();
    
    return ReportEntity.create(
      title: 'تقرير المتأخرات',
      description: 'تقرير الفواتير والمدفوعات المتأخرة',
      type: ReportType.overdue,
      period: ReportPeriod.custom,
      startDate: DateTime(2020, 1, 1),
      endDate: now,
      generatedBy: generatedBy,
      data: data,
    );
  }
}
