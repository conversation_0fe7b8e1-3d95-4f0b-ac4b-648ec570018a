import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'adaptive_layout.dart';

/// عنصر التنقل
class NavigationItem {
  final String label;
  final IconData icon;
  final IconData? selectedIcon;
  final String route;
  final List<NavigationItem>? children;
  final bool isExpanded;
  final VoidCallback? onTap;

  const NavigationItem({
    required this.label,
    required this.icon,
    this.selectedIcon,
    required this.route,
    this.children,
    this.isExpanded = false,
    this.onTap,
  });

  NavigationItem copyWith({
    String? label,
    IconData? icon,
    IconData? selectedIcon,
    String? route,
    List<NavigationItem>? children,
    bool? isExpanded,
    VoidCallback? onTap,
  }) {
    return NavigationItem(
      label: label ?? this.label,
      icon: icon ?? this.icon,
      selectedIcon: selectedIcon ?? this.selectedIcon,
      route: route ?? this.route,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      onTap: onTap ?? this.onTap,
    );
  }
}

/// التنقل التكيفي الرئيسي
class AdaptiveNavigationScaffold extends StatefulWidget {
  final List<NavigationItem> navigationItems;
  final Widget body;
  final String currentRoute;
  final Function(String route) onNavigate;
  final Widget? appBar;
  final Widget? floatingActionButton;
  final bool showNavigationLabels;

  const AdaptiveNavigationScaffold({
    super.key,
    required this.navigationItems,
    required this.body,
    required this.currentRoute,
    required this.onNavigate,
    this.appBar,
    this.floatingActionButton,
    this.showNavigationLabels = true,
  });

  @override
  State<AdaptiveNavigationScaffold> createState() => _AdaptiveNavigationScaffoldState();
}

class _AdaptiveNavigationScaffoldState extends State<AdaptiveNavigationScaffold> {
  bool _isRailExtended = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return AdaptiveLayoutBuilder(
      builder: (context, layoutType) {
        switch (layoutType) {
          case LayoutType.mobile:
            return _buildMobileLayout();
          case LayoutType.tablet:
            return _buildTabletLayout();
          case LayoutType.desktop:
          case LayoutType.largeDesktop:
            return _buildDesktopLayout();
        }
      },
    );
  }

  /// تخطيط الهاتف المحمول
  Widget _buildMobileLayout() {
    return Scaffold(
      key: _scaffoldKey,
      appBar: widget.appBar as PreferredSizeWidget?,
      body: widget.body,
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// تخطيط الجهاز اللوحي
  Widget _buildTabletLayout() {
    return Scaffold(
      key: _scaffoldKey,
      appBar: widget.appBar as PreferredSizeWidget?,
      drawer: _buildDrawer(),
      body: widget.body,
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// تخطيط سطح المكتب
  Widget _buildDesktopLayout() {
    return Scaffold(
      key: _scaffoldKey,
      body: Row(
        children: [
          _buildNavigationRail(),
          const VerticalDivider(width: 1),
          Expanded(
            child: Column(
              children: [
                if (widget.appBar != null) widget.appBar!,
                Expanded(child: widget.body),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigationBar() {
    final mainItems = widget.navigationItems.where((item) => item.children == null).take(5).toList();
    
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _getCurrentIndex(mainItems),
      onTap: (index) => widget.onNavigate(mainItems[index].route),
      items: mainItems.map((item) => BottomNavigationBarItem(
        icon: Icon(item.icon),
        activeIcon: Icon(item.selectedIcon ?? item.icon),
        label: widget.showNavigationLabels ? item.label : null,
      )).toList(),
    );
  }

  /// بناء الدرج الجانبي
  Widget _buildDrawer() {
    return Drawer(
      child: Column(
        children: [
          _buildDrawerHeader(),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: widget.navigationItems.map(_buildDrawerItem).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الدرج
  Widget _buildDrawerHeader() {
    return DrawerHeader(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Icon(
            Icons.water_drop,
            size: 48,
            color: Colors.white,
          ),
          SizedBox(height: 8),
          Text(
            'نظام إدارة المياه',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر الدرج
  Widget _buildDrawerItem(NavigationItem item) {
    final isSelected = widget.currentRoute == item.route;
    
    if (item.children != null && item.children!.isNotEmpty) {
      return ExpansionTile(
        leading: Icon(item.icon),
        title: Text(item.label),
        initiallyExpanded: item.isExpanded,
        children: item.children!.map((child) => Padding(
          padding: const EdgeInsets.only(left: 16),
          child: _buildDrawerItem(child),
        )).toList(),
      );
    }

    return ListTile(
      leading: Icon(
        isSelected ? (item.selectedIcon ?? item.icon) : item.icon,
        color: isSelected ? Theme.of(context).primaryColor : null,
      ),
      title: Text(
        item.label,
        style: TextStyle(
          color: isSelected ? Theme.of(context).primaryColor : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      selected: isSelected,
      onTap: () {
        widget.onNavigate(item.route);
        Navigator.of(context).pop();
      },
    );
  }

  /// بناء شريط التنقل الجانبي
  Widget _buildNavigationRail() {
    return NavigationRail(
      extended: _isRailExtended,
      minExtendedWidth: AdaptiveLayout.getSidebarWidth(context),
      leading: _buildRailHeader(),
      destinations: widget.navigationItems.map((item) => NavigationRailDestination(
        icon: Icon(item.icon),
        selectedIcon: Icon(item.selectedIcon ?? item.icon),
        label: Text(item.label),
      )).toList(),
      selectedIndex: _getCurrentIndex(widget.navigationItems),
      onDestinationSelected: (index) {
        final item = widget.navigationItems[index];
        if (item.children == null || item.children!.isEmpty) {
          widget.onNavigate(item.route);
        }
      },
      trailing: Expanded(
        child: Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: IconButton(
              icon: Icon(_isRailExtended ? Icons.chevron_left : Icons.chevron_right),
              onPressed: () {
                setState(() {
                  _isRailExtended = !_isRailExtended;
                });
              },
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رأس الشريط الجانبي
  Widget _buildRailHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Icon(
            Icons.water_drop,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          if (_isRailExtended) ...[
            const SizedBox(height: 8),
            const Text(
              'نظام إدارة المياه',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// الحصول على الفهرس الحالي
  int _getCurrentIndex(List<NavigationItem> items) {
    for (int i = 0; i < items.length; i++) {
      if (items[i].route == widget.currentRoute) {
        return i;
      }
      if (items[i].children != null) {
        for (final child in items[i].children!) {
          if (child.route == widget.currentRoute) {
            return i;
          }
        }
      }
    }
    return 0;
  }
}

/// شريط التطبيق التكيفي
class AdaptiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final PreferredSizeWidget? bottom;

  const AdaptiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveLayoutBuilder(
      builder: (context, layoutType) {
        switch (layoutType) {
          case LayoutType.mobile:
          case LayoutType.tablet:
            return AppBar(
              title: Text(title),
              actions: actions,
              leading: leading,
              automaticallyImplyLeading: automaticallyImplyLeading,
              bottom: bottom,
            );
          case LayoutType.desktop:
          case LayoutType.largeDesktop:
            return _buildDesktopAppBar(context);
        }
      },
    );
  }

  Widget _buildDesktopAppBar(BuildContext context) {
    return Container(
      height: preferredSize.height,
      decoration: BoxDecoration(
        color: Theme.of(context).appBarTheme.backgroundColor ?? Theme.of(context).primaryColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            if (leading != null) leading!,
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).appBarTheme.titleTextStyle ?? 
                       Theme.of(context).textTheme.titleLarge?.copyWith(
                         color: Colors.white,
                         fontWeight: FontWeight.bold,
                       ),
              ),
            ),
            if (actions != null) ...actions!,
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + (bottom?.preferredSize.height ?? 0),
  );
}

/// زر الإجراء العائم التكيفي
class AdaptiveFAB extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final String? tooltip;
  final bool extended;

  const AdaptiveFAB({
    super.key,
    this.onPressed,
    required this.child,
    this.tooltip,
    this.extended = false,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context) && extended) {
      return FloatingActionButton.extended(
        onPressed: onPressed,
        label: child,
        tooltip: tooltip,
      );
    }
    
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      child: child,
    );
  }
}

/// قائمة السياق التكيفية
class AdaptiveContextMenuRegion extends StatelessWidget {
  final Widget child;
  final List<ContextMenuEntry> contextMenuEntries;

  const AdaptiveContextMenuRegion({
    super.key,
    required this.child,
    required this.contextMenuEntries,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return GestureDetector(
        onSecondaryTapDown: (details) {
          _showContextMenu(context, details.globalPosition);
        },
        child: child,
      );
    }
    return child;
  }

  void _showContextMenu(BuildContext context, Offset position) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx,
        position.dy,
      ),
      items: contextMenuEntries.map((entry) => PopupMenuItem(
        value: entry.value,
        child: ListTile(
          leading: entry.icon != null ? Icon(entry.icon) : null,
          title: Text(entry.label),
          contentPadding: EdgeInsets.zero,
        ),
      )).toList(),
    );
  }
}

/// عنصر قائمة السياق
class ContextMenuEntry {
  final String label;
  final IconData? icon;
  final VoidCallback onTap;
  final String? value;

  const ContextMenuEntry({
    required this.label,
    this.icon,
    required this.onTap,
    this.value,
  });
}

/// اختصارات لوحة المفاتيح التكيفية
class AdaptiveKeyboardShortcuts extends StatelessWidget {
  final Widget child;
  final Map<LogicalKeySet, VoidCallback> shortcuts;

  const AdaptiveKeyboardShortcuts({
    super.key,
    required this.child,
    required this.shortcuts,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return Focus(
        autofocus: true,
        onKeyEvent: (node, event) {
          if (event is KeyDownEvent) {
            for (final entry in shortcuts.entries) {
              // تحقق بسيط من الاختصارات
              if (entry.key.keys.contains(event.logicalKey)) {
                entry.value();
                return KeyEventResult.handled;
              }
            }
          }
          return KeyEventResult.ignored;
        },
        child: child,
      );
    }
    return child;
  }
}
