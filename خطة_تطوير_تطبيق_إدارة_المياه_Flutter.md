# 📋 خطة تطوير تطبيق إدارة مشروع المياه التعاوني باستخدام Flutter

## 🎯 نظرة عامة على المشروع

### الهدف الرئيسي
تطوير تطبيق شامل لإدارة مشروع المياه التعاوني يدعم منصات Desktop و Mobile Android باستخدام إطار عمل Flutter، مع التركيز على الأمان والشفافية والكفاءة التشغيلية.

### المنصات المستهدفة
- **Desktop**: Windows,
- **Mobile**: Android (مع إمكانية التوسع لـ iOS لاحقاً)

### التقنيات الأساسية
- **Frontend**: Flutter 3.x
- **Backend**: Firebase / Supabase
- **Database**: PostgreSQL / SQLite
- **State Management**: Riverpod / Bloc
- **Authentication**: Firebase Auth / Supabase Auth

---

## 🏗️ المعمارية التقنية

### هيكل المشروع
```
water_management_app/
├── lib/
│   ├── core/
│   │   ├── constants/
│   │   ├── utils/
│   │   ├── services/
│   │   └── theme/
│   ├── features/
│   │   ├── auth/
│   │   ├── subscribers/
│   │   ├── meters/
│   │   ├── billing/
│   │   ├── payments/
│   │   ├── maintenance/
│   │   ├── financial/
│   │   ├── inventory/
│   │   ├── reports/
│   │   └── notifications/
│   ├── shared/
│   │   ├── widgets/
│   │   ├── models/
│   │   └── repositories/
│   └── main.dart
├── assets/
├── test/
└── docs/
```

### نمط المعمارية
- **Clean Architecture** مع **Feature-First** approach
- **Repository Pattern** لإدارة البيانات
- **Provider/Riverpod** لإدارة الحالة
- **Dependency Injection** باستخدام GetIt

---

## 📊 تحليل المتطلبات الوظيفية

### 1. إدارة المستخدمين والصلاحيات
- **أنواع المستخدمين**: مشترك، محصل، فني، محاسب، مدير، مراجع مالي، أمين مخزن
- **المصادقة الثنائية (2FA)** للمشرفين والإداريين
- **صلاحيات متدرجة** حسب نوع المستخدم

### 2. إدارة المشتركين والقرى
- **تصنيف الاشتراكات**: منزلي، تجاري، خيري
- **ربط المشتركين بالقرى** والعدادات
- **كشف حساب شامل** للمستحقات والمدفوعات

### 3. إدارة العدادات والقراءات
- **دعم QR/NFC** للتعرف السريع على العدادات
- **تصوير العداد** لتسجيل القراءات
- **كشف تلقائي للقراءات غير المنطقية**
- **مقارنة استهلاك المشتركين** مع العداد الرئيسي

### 4. نظام التسعير والفوترة
- **شرائح استهلاك متدرجة**
- **خصومات مخصصة** (خيري، كبار السن)
- **فوترة تلقائية شهرية**
- **مقارنة الاستهلاك** مع الأشهر السابقة

### 5. إدارة الدفعات والتحصيل
- **دعم عملات متعددة**
- **أنواع سندات مختلفة**: نقدي، حوالة، تحويل
- **محفظة مالية داخلية** (صندوق، بنك، محصلين)
- **حدود يومية للمحصلين**

---

## 🔧 المتطلبات التقنية

### الحزم الأساسية المطلوبة
```yaml
dependencies:
  flutter: ^3.16.0
  # State Management
  riverpod: ^2.4.0
  flutter_riverpod: ^2.4.0
  
  # Database & Backend
  supabase_flutter: ^2.0.0
  sqflite: ^2.3.0
  
  # UI & Navigation
  go_router: ^12.0.0
  flutter_screenutil: ^5.9.0
  
  # Forms & Validation
  flutter_form_builder: ^9.1.0
  form_builder_validators: ^9.1.0
  
  # File & Image Handling
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  pdf: ^3.10.4
  
  # QR & NFC
  qr_code_scanner: ^1.0.1
  nfc_manager: ^3.3.0
  
  # Maps & Location
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  
  # Notifications
  firebase_messaging: ^14.7.6
  flutter_local_notifications: ^16.2.0
  
  # Charts & Analytics
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.4
  
  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1
```

### قاعدة البيانات
**الجداول الأساسية**:
- `users` - المستخدمين والصلاحيات
- `villages` - القرى والعدادات الرئيسية
- `subscribers` - المشتركين
- `meters` - العدادات الفرعية
- `readings` - قراءات العدادات
- `bills` - الفواتير
- `payments` - المدفوعات
- `maintenance_requests` - طلبات الصيانة
- `expenses` - المصروفات
- `inventory` - المخزون
- `audit_logs` - سجل العمليات

---

## 📅 الجدول الزمني للتطوير

### المرحلة الأولى (4-6 أسابيع): الأساسيات
1. **إعداد البيئة التقنية** (أسبوع 1)
2. **نظام المصادقة والصلاحيات** (أسبوع 2)
3. **إدارة المشتركين والقرى** (أسبوع 3)
4. **إدارة العدادات والقراءات** (أسبوع 4-5)
5. **نظام التسعير والفوترة الأساسي** (أسبوع 6)

### المرحلة الثانية (4-5 أسابيع): الوظائف المتقدمة
6. **نظام الدفع والتحصيل** (أسبوع 7-8)
7. **الدعم الفني والصيانة** (أسبوع 9)
8. **الإدارة المالية** (أسبوع 10)
9. **إدارة المخزون** (أسبوع 11)

### المرحلة الثالثة (3-4 أسابيع): التقارير والأمان
10. **نظام التقارير والإحصائيات** (أسبوع 12-13)
11. **الإشعارات الذكية** (أسبوع 14)
12. **ميزات الأمان والرقابة** (أسبوع 15)

### المرحلة الرابعة (2-3 أسابيع): التحسين والنشر
13. **الواجهات المتقدمة** (أسبوع 16)
14. **الاختبار والتحسين** (أسبوع 17)
15. **النشر والتوثيق** (أسبوع 18)

**إجمالي المدة المتوقعة**: 18-20 أسبوع (4.5-5 أشهر)

---

## 🎨 تصميم واجهة المستخدم

### المبادئ التصميمية
- **Material Design 3** للـ Android
- **Responsive Design** للدعم متعدد المنصات
- **Dark/Light Theme** مع دعم النظام
- **RTL Support** للغة العربية
- **Accessibility** للمستخدمين ذوي الاحتياجات الخاصة

### الألوان الأساسية
```dart
// Primary Colors
primaryColor: Color(0xFF1976D2), // أزرق
secondaryColor: Color(0xFF03DAC6), // تركوازي
errorColor: Color(0xFFB00020), // أحمر
warningColor: Color(0xFFFF9800), // برتقالي
successColor: Color(0xFF4CAF50), // أخضر
```

---

## 🔒 الأمان والرقابة

### ميزات الأمان
- **تشفير البيانات الحساسة**
- **سجل شامل للعمليات** (Audit Trail)
- **قيود التعديل** بعد إصدار الفواتير
- **الإغلاق الشهري التلقائي**
- **نسخ احتياطية تلقائية**

### الرقابة المالية
- **تسلسل الموافقات** للمصروفات الكبيرة
- **حدود يومية للمحصلين**
- **إشعارات للمعاملات غير العادية**
- **مراجعة مالية ربعية**

---

## 📱 التوافق مع المنصات

### Desktop
- **Windows**: تطبيق مستقل قابل للتثبيت
- **macOS**: دعم Apple Silicon و Intel
- **Linux**: AppImage أو Snap package

### Mobile
- **Android**: API Level 21+ (Android 5.0+)
- **تحسين للشاشات المختلفة**: هواتف وأجهزة لوحية

---

## 🚀 استراتيجية النشر

### بيئات التطوير
1. **Development**: للتطوير اليومي
2. **Staging**: للاختبار قبل النشر
3. **Production**: البيئة الحية

### خطة النشر
1. **Alpha Release**: اختبار داخلي
2. **Beta Release**: اختبار مع مجموعة محدودة
3. **Production Release**: النشر الكامل

---

## 📚 التوثيق والتدريب

### الوثائق المطلوبة
- **دليل المستخدم** لكل نوع مستخدم
- **الوثائق الفنية** للمطورين
- **دليل التثبيت والصيانة**
- **سياسات الأمان والخصوصية**

### خطة التدريب
- **تدريب المديرين** على الإعدادات العامة
- **تدريب المحاسبين** على النظام المالي
- **تدريب المحصلين** على تطبيق الموبايل
- **تدريب الفنيين** على نظام البلاغات

---

## 💰 التكلفة المتوقعة

### تكاليف التطوير
- **فريق التطوير**: 4-5 مطورين لمدة 5 أشهر
- **التصميم والـ UX**: مصمم واحد لمدة شهرين
- **الاختبار**: مختبر واحد لمدة شهر
- **إدارة المشروع**: مدير مشروع بدوام جزئي

### تكاليف التشغيل السنوية
- **الاستضافة السحابية**: $500-1000
- **قواعد البيانات**: $300-600
- **الخدمات الخارجية**: $200-400
- **الصيانة والدعم**: $2000-4000

---

## 🎯 مؤشرات الأداء الرئيسية (KPIs)

### مؤشرات تقنية
- **وقت الاستجابة**: أقل من 2 ثانية
- **معدل التوفر**: 99.5%+
- **معدل الأخطاء**: أقل من 0.1%

### مؤشرات الأعمال
- **تحسين كفاءة التحصيل**: 15%+
- **تقليل الأخطاء المحاسبية**: 80%+
- **توفير الوقت**: 50%+ في العمليات اليومية

---

## ⚠️ المخاطر والتحديات

### المخاطر التقنية
- **تعقيد التكامل** بين المنصات المختلفة
- **أداء التطبيق** مع كميات البيانات الكبيرة
- **أمان البيانات** والحماية من الاختراق

### المخاطر التشغيلية
- **مقاومة التغيير** من المستخدمين
- **التدريب والتأهيل** المطلوب
- **انقطاع الخدمة** أثناء الانتقال

### خطط التخفيف
- **اختبارات شاملة** قبل النشر
- **تدريب مكثف** للمستخدمين
- **خطة طوارئ** للعودة للنظام القديم
- **دعم فني مستمر** في الأشهر الأولى

---

## 📞 الدعم والصيانة

### خطة الدعم
- **دعم فني 24/7** في الأشهر الثلاثة الأولى
- **تحديثات أمنية** شهرية
- **تحديثات الميزات** ربع سنوية
- **نسخ احتياطية** يومية تلقائية

### فريق الصيانة
- **مطور رئيسي** للتحديثات الكبيرة
- **مطور دعم** للإصلاحات السريعة
- **مدير نظم** لإدارة الخوادم

---

## ✅ الخطوات التالية

1. **موافقة على الخطة** والميزانية
2. **تشكيل فريق العمل** وتحديد الأدوار
3. **إعداد البيئة التطويرية** والأدوات
4. **بدء المرحلة الأولى** من التطوير
5. **مراجعة دورية** للتقدم والجودة

---

---

## 🛠️ التفاصيل التقنية المتقدمة

### نمط البيانات والـ Models
```dart
// نموذج المشترك
class Subscriber {
  final String id;
  final String name;
  final String villageId;
  final SubscriptionType type;
  final SubscriptionStatus status;
  final List<String> meterIds;
  final ContactInfo contactInfo;
  final DateTime createdAt;
  final DateTime updatedAt;
}

// نموذج العداد
class Meter {
  final String id;
  final String subscriberId;
  final String serialNumber;
  final MeterStatus status;
  final Location location;
  final List<Reading> readings;
  final DateTime installationDate;
}

// نموذج القراءة
class Reading {
  final String id;
  final String meterId;
  final double currentReading;
  final double previousReading;
  final double consumption;
  final DateTime readingDate;
  final String readBy;
  final ReadingMethod method;
  final bool isValidated;
}
```

### خدمات الـ API والتكامل
```dart
// خدمة إدارة المشتركين
abstract class SubscriberRepository {
  Future<List<Subscriber>> getAllSubscribers();
  Future<Subscriber?> getSubscriberById(String id);
  Future<void> createSubscriber(Subscriber subscriber);
  Future<void> updateSubscriber(Subscriber subscriber);
  Future<void> deleteSubscriber(String id);
  Future<List<Subscriber>> getSubscribersByVillage(String villageId);
}

// خدمة إدارة الفواتير
abstract class BillingService {
  Future<Bill> generateBill(String subscriberId, DateTime period);
  Future<List<Bill>> getBillsForSubscriber(String subscriberId);
  Future<void> sendBillNotification(String billId);
  Future<PdfDocument> generateBillPdf(String billId);
}
```

### نظام الإشعارات المتقدم
```dart
// أنواع الإشعارات
enum NotificationType {
  billGenerated,
  paymentReceived,
  maintenanceRequest,
  unusualReading,
  systemAlert,
  reminderDue
}

// خدمة الإشعارات
class NotificationService {
  // إرسال إشعار فوري
  Future<void> sendInstantNotification(
    String userId,
    NotificationType type,
    Map<String, dynamic> data
  );

  // جدولة إشعار مؤجل
  Future<void> scheduleNotification(
    String userId,
    NotificationType type,
    DateTime scheduledTime,
    Map<String, dynamic> data
  );

  // إرسال عبر WhatsApp
  Future<void> sendWhatsAppMessage(
    String phoneNumber,
    String message
  );

  // إرسال عبر البريد الإلكتروني
  Future<void> sendEmailNotification(
    String email,
    String subject,
    String body,
    List<Attachment>? attachments
  );
}
```

### نظام التقارير المرن
```dart
// بناء التقارير المخصصة
class ReportBuilder {
  final List<String> selectedColumns;
  final Map<String, dynamic> filters;
  final String sortBy;
  final SortOrder sortOrder;
  final DateRange dateRange;

  Future<ReportData> buildReport();
  Future<PdfDocument> exportToPdf();
  Future<ExcelDocument> exportToExcel();
  Future<void> scheduleAutomaticReport();
}

// أنواع التقارير المتاحة
enum ReportType {
  subscriberStatement,
  villageConsumption,
  financialSummary,
  maintenanceLog,
  collectionReport,
  inventoryStatus,
  auditTrail
}
```

### نظام الأمان المتقدم
```dart
// تشفير البيانات الحساسة
class EncryptionService {
  static String encryptSensitiveData(String data);
  static String decryptSensitiveData(String encryptedData);
  static String hashPassword(String password);
  static bool verifyPassword(String password, String hash);
}

// سجل العمليات (Audit Trail)
class AuditLogger {
  static Future<void> logOperation(
    String userId,
    String operation,
    String entityType,
    String entityId,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues
  );

  static Future<List<AuditLog>> getAuditTrail(
    String entityId,
    DateRange? dateRange
  );
}
```

---

## 📊 قاعدة البيانات المفصلة

### جداول النظام الأساسية

#### جدول المستخدمين (users)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  role user_role NOT NULL,
  is_active BOOLEAN DEFAULT true,
  two_factor_enabled BOOLEAN DEFAULT false,
  last_login TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول القرى (villages)
```sql
CREATE TABLE villages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  main_meter_id VARCHAR(50) UNIQUE,
  location POINT,
  population INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول المشتركين (subscribers)
```sql
CREATE TABLE subscribers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscriber_number VARCHAR(20) UNIQUE NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  village_id UUID REFERENCES villages(id),
  subscription_type subscription_type NOT NULL,
  status subscription_status DEFAULT 'active',
  phone VARCHAR(20),
  email VARCHAR(100),
  address TEXT,
  national_id VARCHAR(20),
  connection_date DATE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول العدادات (meters)
```sql
CREATE TABLE meters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  serial_number VARCHAR(50) UNIQUE NOT NULL,
  subscriber_id UUID REFERENCES subscribers(id),
  meter_type meter_type DEFAULT 'residential',
  status meter_status DEFAULT 'active',
  installation_date DATE,
  last_maintenance DATE,
  location POINT,
  initial_reading DECIMAL(10,3) DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### الفهارس والقيود
```sql
-- فهارس لتحسين الأداء
CREATE INDEX idx_subscribers_village ON subscribers(village_id);
CREATE INDEX idx_meters_subscriber ON meters(subscriber_id);
CREATE INDEX idx_readings_meter_date ON readings(meter_id, reading_date);
CREATE INDEX idx_bills_subscriber_period ON bills(subscriber_id, billing_period);
CREATE INDEX idx_payments_subscriber_date ON payments(subscriber_id, payment_date);

-- قيود البيانات
ALTER TABLE readings ADD CONSTRAINT chk_reading_positive
  CHECK (current_reading >= 0);
ALTER TABLE bills ADD CONSTRAINT chk_amount_positive
  CHECK (total_amount >= 0);
ALTER TABLE payments ADD CONSTRAINT chk_payment_positive
  CHECK (amount > 0);
```

---

## 🔧 إعدادات التطوير المتقدمة

### ملف pubspec.yaml الكامل
```yaml
name: water_management_app
description: نظام إدارة مشروع المياه التعاوني
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Backend & Database
  supabase_flutter: ^2.0.0
  sqflite: ^2.3.0
  drift: ^2.14.0

  # Navigation & Routing
  go_router: ^12.1.3
  auto_route: ^7.8.4

  # UI & Styling
  flutter_screenutil: ^5.9.0
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0

  # Forms & Validation
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^9.1.0
  reactive_forms: ^16.1.1

  # File Handling
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  path_provider: ^2.1.1

  # PDF & Documents
  pdf: ^3.10.4
  printing: ^5.11.0
  excel: ^2.1.0

  # QR & NFC
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  nfc_manager: ^3.3.0

  # Maps & Location
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # Notifications
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0
  awesome_notifications: ^0.8.2

  # Charts & Analytics
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.7
  syncfusion_flutter_datagrid: ^23.2.7

  # Network & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  pretty_dio_logger: ^1.3.1

  # Storage & Caching
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Utils & Helpers
  intl: ^0.18.1
  uuid: ^4.2.1
  crypto: ^3.0.3
  logger: ^2.0.2+1

  # Permissions
  permission_handler: ^11.1.0

  # Device Info
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

  # Code Generation
  build_runner: ^2.4.7
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/config/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
```

### إعدادات البناء للمنصات المختلفة

#### Android (android/app/build.gradle)
```gradle
android {
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId "com.waterproject.management"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

#### Desktop Windows (windows/runner/main.cpp)
```cpp
#include <flutter/dart_project.h>
#include <flutter/flutter_view_controller.h>
#include <windows.h>

#include "flutter_window.h"
#include "utils.h"

int APIENTRY wWinMain(_In_ HINSTANCE instance, _In_opt_ HINSTANCE prev,
                      _In_ wchar_t *command_line, _In_ int show_command) {
  // Attach to console when present (e.g., 'flutter run') or create a
  // new console when running with a debugger.
  if (!::AttachConsole(ATTACH_PARENT_PROCESS) && ::IsDebuggerPresent()) {
    CreateAndAttachConsole();
  }

  // Initialize COM, so that it is available for use in the library and/or
  // plugins.
  ::CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);

  flutter::DartProject project(L"data");

  std::vector<std::string> command_line_arguments =
      GetCommandLineArguments();

  project.set_dart_entrypoint_arguments(std::move(command_line_arguments));

  FlutterWindow window(project);
  Win32Window::Point origin(10, 10);
  Win32Window::Size size(1280, 720);
  if (!window.CreateAndShow(L"نظام إدارة مشروع المياه", origin, size)) {
    return EXIT_FAILURE;
  }
  window.SetQuitOnClose(true);

  ::MSG msg;
  while (::GetMessage(&msg, nullptr, 0, 0)) {
    ::TranslateMessage(&msg);
    ::DispatchMessage(&msg);
  }

  ::CoUninitialize();
  return EXIT_SUCCESS;
}
```

---

## 🧪 استراتيجية الاختبار الشاملة

### أنواع الاختبارات
1. **Unit Tests**: اختبار الوحدات الفردية
2. **Widget Tests**: اختبار واجهات المستخدم
3. **Integration Tests**: اختبار التكامل الكامل
4. **Performance Tests**: اختبار الأداء
5. **Security Tests**: اختبار الأمان

### مثال على اختبارات الوحدة
```dart
// test/services/billing_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:water_management_app/services/billing_service.dart';

void main() {
  group('BillingService Tests', () {
    late BillingService billingService;
    late MockSubscriberRepository mockSubscriberRepo;
    late MockMeterRepository mockMeterRepo;

    setUp(() {
      mockSubscriberRepo = MockSubscriberRepository();
      mockMeterRepo = MockMeterRepository();
      billingService = BillingService(
        subscriberRepo: mockSubscriberRepo,
        meterRepo: mockMeterRepo,
      );
    });

    test('should generate bill correctly for residential subscriber', () async {
      // Arrange
      final subscriber = Subscriber(
        id: '123',
        type: SubscriptionType.residential,
        // ... other properties
      );
      final readings = [
        Reading(currentReading: 100, previousReading: 80),
      ];

      when(mockSubscriberRepo.getById('123'))
          .thenAnswer((_) async => subscriber);
      when(mockMeterRepo.getReadingsForPeriod(any, any))
          .thenAnswer((_) async => readings);

      // Act
      final bill = await billingService.generateBill('123', DateTime.now());

      // Assert
      expect(bill.consumption, equals(20));
      expect(bill.totalAmount, greaterThan(0));
      verify(mockSubscriberRepo.getById('123')).called(1);
    });
  });
}
```

### اختبارات التكامل
```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:water_management_app/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Water Management App Integration Tests', () {
    testWidgets('Complete billing workflow', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login
      await tester.enterText(find.byKey(Key('username_field')), 'admin');
      await tester.enterText(find.byKey(Key('password_field')), 'password');
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();

      // Navigate to billing
      await tester.tap(find.byIcon(Icons.receipt));
      await tester.pumpAndSettle();

      // Generate bills
      await tester.tap(find.byKey(Key('generate_bills_button')));
      await tester.pumpAndSettle();

      // Verify bills are generated
      expect(find.text('تم إنشاء الفواتير بنجاح'), findsOneWidget);
    });
  });
}
```

---

*تم إعداد هذه الخطة الشاملة بناءً على تحليل دقيق لمتطلبات نظام إدارة مشروع المياه التعاوني، مع التركيز على أفضل الممارسات التقنية والأمان والقابلية للصيانة.*
