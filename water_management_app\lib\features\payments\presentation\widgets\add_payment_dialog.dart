import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../../bills/presentation/providers/bill_provider.dart';
import '../../../bills/domain/entities/bill_entity.dart';
import '../providers/payment_provider.dart';
import '../../domain/entities/payment_entity.dart';

class AddPaymentDialog extends ConsumerStatefulWidget {
  final PaymentEntity? payment;

  const AddPaymentDialog({
    super.key,
    this.payment,
  });

  @override
  ConsumerState<AddPaymentDialog> createState() => _AddPaymentDialogState();
}

class _AddPaymentDialogState extends ConsumerState<AddPaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _referenceController = TextEditingController();
  final _bankNameController = TextEditingController();
  final _checkNumberController = TextEditingController();
  final _notesController = TextEditingController();
  final _collectedByController = TextEditingController();

  int? _selectedBillId;
  PaymentMethod _selectedMethod = PaymentMethod.cash;
  PaymentType _selectedType = PaymentType.full;
  PaymentStatus _selectedStatus = PaymentStatus.pending;
  DateTime _paymentDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _collectedByController.text = 'مدير النظام'; // افتراضي
    if (widget.payment != null) {
      _initializeWithPayment();
    }
  }

  void _initializeWithPayment() {
    final payment = widget.payment!;
    _selectedBillId = payment.billId;
    _amountController.text = payment.amount.toString();
    _selectedMethod = payment.method;
    _selectedType = payment.type;
    _selectedStatus = payment.status;
    _paymentDate = payment.paymentDate;
    _referenceController.text = payment.referenceNumber ?? '';
    _bankNameController.text = payment.bankName ?? '';
    _checkNumberController.text = payment.checkNumber ?? '';
    _notesController.text = payment.notes ?? '';
    _collectedByController.text = payment.collectedBy;
  }

  @override
  void dispose() {
    _amountController.dispose();
    _referenceController.dispose();
    _bankNameController.dispose();
    _checkNumberController.dispose();
    _notesController.dispose();
    _collectedByController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      title: widget.payment == null ? 'إضافة دفعة جديدة' : 'تعديل الدفعة',
      scrollable: true,
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildBillSelection(),
            const SizedBox(height: 16),
            _buildPaymentInfo(),
            const SizedBox(height: 16),
            _buildPaymentDetails(),
            const SizedBox(height: 16),
            _buildAdditionalInfo(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _savePayment,
          child: Text(widget.payment == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }

  /// بناء اختيار الفاتورة
  Widget _buildBillSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختيار الفاتورة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildBillDropdown(),
      ],
    );
  }

  /// بناء قائمة الفواتير
  Widget _buildBillDropdown() {
    final billsAsync = ref.watch(billsProvider);
    
    return billsAsync.when(
      data: (bills) {
        // فلترة الفواتير غير المدفوعة بالكامل
        final unpaidBills = bills.where((bill) => 
          bill.status != BillStatus.paid && bill.status != BillStatus.cancelled
        ).toList();
        
        return DropdownButtonFormField<int>(
          value: _selectedBillId,
          decoration: const InputDecoration(
            labelText: 'الفاتورة *',
            border: OutlineInputBorder(),
            helperText: 'اختر الفاتورة المراد دفعها',
          ),
          items: unpaidBills.map((bill) {
            return DropdownMenuItem(
              value: bill.id,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${bill.billNumber} - ${bill.subscriberName}'),
                  Text(
                    'المبلغ: ${bill.remainingAmount.toStringAsFixed(2)} ريال',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedBillId = value;
            });
            if (value != null) {
              _loadBillAmount(value, unpaidBills);
            }
          },
          validator: (value) {
            if (value == null) {
              return 'يجب اختيار الفاتورة';
            }
            return null;
          },
        );
      },
      loading: () => const LinearProgressIndicator(),
      error: (error, stack) => Text('خطأ في تحميل الفواتير: $error'),
    );
  }

  /// تحميل مبلغ الفاتورة
  void _loadBillAmount(int billId, List<BillEntity> bills) {
    final bill = bills.firstWhere((b) => b.id == billId);
    _amountController.text = bill.remainingAmount.toString();
  }

  /// بناء معلومات الدفعة
  Widget _buildPaymentInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الدفعة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ *',
                  border: OutlineInputBorder(),
                  suffixText: 'ريال',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'المبلغ مطلوب';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'قيمة غير صحيحة';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDatePicker(
                'تاريخ الدفع',
                _paymentDate,
                (date) => setState(() => _paymentDate = date),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<PaymentMethod>(
                value: _selectedMethod,
                decoration: const InputDecoration(
                  labelText: 'طريقة الدفع',
                  border: OutlineInputBorder(),
                ),
                items: PaymentMethod.values.map((method) {
                  return DropdownMenuItem(
                    value: method,
                    child: Text(_getMethodText(method)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedMethod = value!;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<PaymentType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الدفع',
                  border: OutlineInputBorder(),
                ),
                items: PaymentType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getTypeText(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<PaymentStatus>(
          value: _selectedStatus,
          decoration: const InputDecoration(
            labelText: 'حالة الدفع',
            border: OutlineInputBorder(),
          ),
          items: PaymentStatus.values.map((status) {
            return DropdownMenuItem(
              value: status,
              child: Text(_getStatusText(status)),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedStatus = value!;
            });
          },
        ),
      ],
    );
  }

  /// بناء تفاصيل الدفع
  Widget _buildPaymentDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفاصيل الدفع',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (_selectedMethod == PaymentMethod.bankTransfer ||
            _selectedMethod == PaymentMethod.online) ...[
          TextFormField(
            controller: _referenceController,
            decoration: const InputDecoration(
              labelText: 'رقم المرجع',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.confirmation_number),
            ),
          ),
          const SizedBox(height: 12),
        ],
        if (_selectedMethod == PaymentMethod.bankTransfer ||
            _selectedMethod == PaymentMethod.creditCard ||
            _selectedMethod == PaymentMethod.debitCard) ...[
          TextFormField(
            controller: _bankNameController,
            decoration: const InputDecoration(
              labelText: 'اسم البنك',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.account_balance),
            ),
          ),
          const SizedBox(height: 12),
        ],
        if (_selectedMethod == PaymentMethod.check) ...[
          TextFormField(
            controller: _checkNumberController,
            decoration: const InputDecoration(
              labelText: 'رقم الشيك',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.receipt),
            ),
            validator: (value) {
              if (_selectedMethod == PaymentMethod.check && 
                  (value == null || value.trim().isEmpty)) {
                return 'رقم الشيك مطلوب';
              }
              return null;
            },
          ),
          const SizedBox(height: 12),
        ],
        TextFormField(
          controller: _collectedByController,
          decoration: const InputDecoration(
            labelText: 'المحصل *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'اسم المحصل مطلوب';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات إضافية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  /// بناء منتقي التاريخ
  Widget _buildDatePicker(
    String label,
    DateTime selectedDate,
    Function(DateTime) onDateSelected,
  ) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: selectedDate,
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 30)),
        );
        if (date != null) {
          onDateSelected(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: const Icon(Icons.calendar_today),
        ),
        child: Text(
          '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
        ),
      ),
    );
  }

  /// الحصول على نص طريقة الدفع
  String _getMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
      case PaymentMethod.debitCard:
        return 'بطاقة خصم';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.online:
        return 'دفع إلكتروني';
    }
  }

  /// الحصول على نص نوع الدفع
  String _getTypeText(PaymentType type) {
    switch (type) {
      case PaymentType.full:
        return 'دفع كامل';
      case PaymentType.partial:
        return 'دفع جزئي';
      case PaymentType.installment:
        return 'قسط';
      case PaymentType.advance:
        return 'دفع مقدم';
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'في الانتظار';
      case PaymentStatus.completed:
        return 'مكتمل';
      case PaymentStatus.failed:
        return 'فشل';
      case PaymentStatus.cancelled:
        return 'ملغي';
      case PaymentStatus.refunded:
        return 'مسترد';
    }
  }

  /// حفظ الدفعة
  void _savePayment() async {
    if (_formKey.currentState!.validate()) {
      // الحصول على الفاتورة المحددة
      final billsAsync = ref.read(billsProvider);
      final bills = billsAsync.value ?? [];
      final selectedBill = bills.firstWhere(
        (b) => b.id == _selectedBillId,
        orElse: () => throw Exception('الفاتورة غير موجودة'),
      );

      final payment = PaymentEntity.create(
        billId: selectedBill.id,
        billNumber: selectedBill.billNumber,
        subscriberId: selectedBill.subscriberId,
        subscriberName: selectedBill.subscriberName,
        amount: double.parse(_amountController.text.trim()),
        method: _selectedMethod,
        type: _selectedType,
        collectedBy: _collectedByController.text.trim(),
        paymentDate: _paymentDate,
        referenceNumber: _referenceController.text.trim().isEmpty ? null : _referenceController.text.trim(),
        bankName: _bankNameController.text.trim().isEmpty ? null : _bankNameController.text.trim(),
        checkNumber: _checkNumberController.text.trim().isEmpty ? null : _checkNumberController.text.trim(),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      ).copyWith(
        id: widget.payment?.id ?? 0,
        paymentNumber: widget.payment?.paymentNumber ?? '',
        status: _selectedStatus,
      );

      try {
        await ref.read(paymentFormProvider.notifier).savePayment(payment);
        
        final state = ref.read(paymentFormProvider);
        if (!mounted) return;
        
        if (state.isSuccess) {
          Navigator.of(context).pop(true);
        } else if (state.error != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.error!),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الدفعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
