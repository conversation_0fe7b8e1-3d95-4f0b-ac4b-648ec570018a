import 'package:drift/drift.dart';
import '../../../../core/database/app_database.dart';
import '../../../../core/services/database_service.dart';
import '../../domain/entities/reading_entity.dart';

/// مستودع القراءات
class ReadingRepository {
  final AppDatabase _database = DatabaseService.instance.database;

  /// الحصول على جميع القراءات
  Future<List<ReadingEntity>> getAllReadings() async {
    try {
      final query = _database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..orderBy([OrderingTerm.desc(_database.readings.readingDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final reading = row.readTable(_database.readings);
        final meter = row.readTableOrNull(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(
          reading,
          meter?.serialNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد'
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على القراءات: $e');
    }
  }

  /// الحصول على قراءة بالمعرف
  Future<ReadingEntity?> getReadingById(int id) async {
    try {
      final query = _database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.readings.id.equals(id));

      final result = await query.getSingleOrNull();
      
      if (result == null) return null;
      
      final reading = result.readTable(_database.readings);
      final meter = result.readTableOrNull(_database.meters);
      final subscriber = result.readTableOrNull(_database.subscribers);
      final village = result.readTableOrNull(_database.villages);
      
      return _mapToEntity(
        reading,
        meter?.serialNumber ?? 'غير محدد',
        subscriber?.fullName ?? 'غير محدد',
        village?.name ?? 'غير محدد'
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على القراءة: $e');
    }
  }

  /// البحث في القراءات
  Future<List<ReadingEntity>> searchReadings(String query) async {
    try {
      if (query.isEmpty) {
        return await getAllReadings();
      }

      final searchQuery = _database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(
        _database.meters.serialNumber.like('%$query%') |
        _database.subscribers.fullName.like('%$query%') |
        _database.villages.name.like('%$query%')
      );

      final results = await searchQuery.get();
      
      return results.map((row) {
        final reading = row.readTable(_database.readings);
        final meter = row.readTableOrNull(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(
          reading,
          meter?.serialNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد'
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في البحث عن القراءات: $e');
    }
  }

  /// الحصول على القراءات حسب العداد
  Future<List<ReadingEntity>> getReadingsByMeter(int meterId) async {
    try {
      final query = _database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.readings.meterId.equals(meterId))
      ..orderBy([OrderingTerm.desc(_database.readings.readingDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final reading = row.readTable(_database.readings);
        final meter = row.readTableOrNull(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(
          reading,
          meter?.serialNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد'
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على قراءات العداد: $e');
    }
  }

  /// الحصول على القراءات حسب المشترك
  Future<List<ReadingEntity>> getReadingsBySubscriber(int subscriberId) async {
    try {
      final query = _database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.subscribers.id.equals(subscriberId))
      ..orderBy([OrderingTerm.desc(_database.readings.readingDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final reading = row.readTable(_database.readings);
        final meter = row.readTableOrNull(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(
          reading,
          meter?.serialNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد'
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على قراءات المشترك: $e');
    }
  }

  /// الحصول على القراءات حسب التاريخ
  Future<List<ReadingEntity>> getReadingsByDateRange(
    DateTime startDate, 
    DateTime endDate
  ) async {
    try {
      final query = _database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(
        _database.readings.readingDate.isBetweenValues(startDate, endDate)
      )..orderBy([OrderingTerm.desc(_database.readings.readingDate)]);

      final results = await query.get();
      
      return results.map((row) {
        final reading = row.readTable(_database.readings);
        final meter = row.readTableOrNull(_database.meters);
        final subscriber = row.readTableOrNull(_database.subscribers);
        final village = row.readTableOrNull(_database.villages);
        
        return _mapToEntity(
          reading,
          meter?.serialNumber ?? 'غير محدد',
          subscriber?.fullName ?? 'غير محدد',
          village?.name ?? 'غير محدد'
        );
      }).toList();
    } catch (e) {
      throw Exception('خطأ في الحصول على القراءات حسب التاريخ: $e');
    }
  }

  /// إضافة قراءة جديدة
  Future<ReadingEntity> createReading(ReadingEntity reading) async {
    try {
      // التحقق من عدم وجود قراءة مكررة لنفس العداد في نفس التاريخ
      final existingReading = await (_database.select(_database.readings)
        ..where((r) => r.meterId.equals(reading.meterId) & 
                      r.readingDate.equals(reading.readingDate)))
        .getSingleOrNull();

      if (existingReading != null) {
        throw Exception('يوجد قراءة مسجلة لهذا العداد في نفس التاريخ');
      }

      final id = await _database.into(_database.readings).insert(
        ReadingsCompanion.insert(
          meterId: reading.meterId,
          previousReading: reading.previousReading,
          currentReading: reading.currentReading,
          consumption: reading.consumption,
          readingDate: reading.readingDate,
          readingPeriod: '${reading.readingDate.year}-${reading.readingDate.month.toString().padLeft(2, '0')}',
          readingMethod: reading.type.name,
          isEstimated: Value(reading.isEstimated),
          validationNotes: Value(reading.notes),
          imagePath: Value(reading.imageUrl),
        ),
      );

      // تحديث القراءة الحالية في العداد
      await (_database.update(_database.meters)
        ..where((m) => m.id.equals(reading.meterId)))
        .write(MetersCompanion(
          currentReading: Value(reading.currentReading),
          updatedAt: Value(DateTime.now()),
        ));

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'readings',
        recordId: id,
        operation: 'INSERT',
        newValues: reading.toMap(),
      );

      return reading.copyWith(id: id);
    } catch (e) {
      throw Exception('خطأ في إضافة القراءة: $e');
    }
  }

  /// تحديث قراءة
  Future<ReadingEntity> updateReading(ReadingEntity reading) async {
    try {
      // الحصول على البيانات القديمة
      final oldReading = await getReadingById(reading.id);
      
      await (_database.update(_database.readings)
        ..where((r) => r.id.equals(reading.id)))
        .write(ReadingsCompanion(
          meterId: Value(reading.meterId),
          previousReading: Value(reading.previousReading),
          currentReading: Value(reading.currentReading),
          consumption: Value(reading.consumption),
          readingDate: Value(reading.readingDate),
          readingPeriod: Value('${reading.readingDate.year}-${reading.readingDate.month.toString().padLeft(2, '0')}'),
          readingMethod: Value(reading.type.name),
          isEstimated: Value(reading.isEstimated),
          validationNotes: Value(reading.notes),
          imagePath: Value(reading.imageUrl),
        ));

      // تحديث القراءة الحالية في العداد إذا كانت هذه آخر قراءة
      final latestReading = await (_database.select(_database.readings)
        ..where((r) => r.meterId.equals(reading.meterId))
        ..orderBy([(r) => OrderingTerm.desc(r.readingDate)]))
        .getSingleOrNull();

      if (latestReading?.id == reading.id) {
        await (_database.update(_database.meters)
          ..where((m) => m.id.equals(reading.meterId)))
          .write(MetersCompanion(
            currentReading: Value(reading.currentReading),
            updatedAt: Value(DateTime.now()),
          ));
      }

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'readings',
        recordId: reading.id,
        operation: 'UPDATE',
        oldValues: oldReading?.toMap(),
        newValues: reading.toMap(),
      );

      return reading.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      throw Exception('خطأ في تحديث القراءة: $e');
    }
  }

  /// حذف قراءة
  Future<void> deleteReading(int id) async {
    try {
      // التحقق من وجود فواتير مرتبطة
      final billsCount = await (_database.select(_database.bills)
        ..where((b) => b.readingId.equals(id)))
        .get()
        .then((bills) => bills.length);

      if (billsCount > 0) {
        throw Exception('لا يمكن حذف القراءة لوجود فواتير مرتبطة بها');
      }

      // الحصول على البيانات قبل الحذف
      final reading = await getReadingById(id);

      await (_database.delete(_database.readings)
        ..where((r) => r.id.equals(id)))
        .go();

      // تسجيل العملية في سجل المراجعة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'readings',
        recordId: id,
        operation: 'DELETE',
        oldValues: reading?.toMap(),
      );
    } catch (e) {
      throw Exception('خطأ في حذف القراءة: $e');
    }
  }

  /// تأكيد قراءة
  Future<ReadingEntity> confirmReading(int readingId, {String? readerName}) async {
    try {
      final reading = await getReadingById(readingId);
      if (reading == null) {
        throw Exception('القراءة غير موجودة');
      }

      final confirmedReading = reading.confirm(readerName: readerName);
      return await updateReading(confirmedReading);
    } catch (e) {
      throw Exception('خطأ في تأكيد القراءة: $e');
    }
  }

  /// الحصول على آخر قراءة للعداد
  Future<ReadingEntity?> getLastReadingForMeter(int meterId) async {
    try {
      final query = _database.select(_database.readings).join([
        leftOuterJoin(
          _database.meters,
          _database.meters.id.equalsExp(_database.readings.meterId),
        ),
        leftOuterJoin(
          _database.subscribers,
          _database.subscribers.id.equalsExp(_database.meters.subscriberId),
        ),
        leftOuterJoin(
          _database.villages,
          _database.villages.id.equalsExp(_database.subscribers.villageId),
        ),
      ])..where(_database.readings.meterId.equals(meterId))
      ..orderBy([OrderingTerm.desc(_database.readings.readingDate)]);

      final result = await query.getSingleOrNull();
      
      if (result == null) return null;
      
      final reading = result.readTable(_database.readings);
      final meter = result.readTableOrNull(_database.meters);
      final subscriber = result.readTableOrNull(_database.subscribers);
      final village = result.readTableOrNull(_database.villages);
      
      return _mapToEntity(
        reading,
        meter?.serialNumber ?? 'غير محدد',
        subscriber?.fullName ?? 'غير محدد',
        village?.name ?? 'غير محدد'
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على آخر قراءة: $e');
    }
  }

  /// الحصول على إحصائيات القراءات
  Future<Map<String, int>> getReadingsStatistics() async {
    try {
      final stats = <String, int>{};
      
      // إجمالي القراءات
      stats['total'] = await _database.select(_database.readings).get()
        .then((list) => list.length);
      
      // القراءات المؤكدة
      stats['confirmed'] = await (_database.select(_database.readings)
        ..where((r) => r.isValidated.equals(true))).get()
        .then((list) => list.length);

      // القراءات المفوترة (سنعتبرها المؤكدة)
      stats['billed'] = await (_database.select(_database.readings)
        ..where((r) => r.isValidated.equals(true))).get()
        .then((list) => list.length);

      // القراءات في الانتظار
      stats['pending'] = await (_database.select(_database.readings)
        ..where((r) => r.isValidated.equals(false))).get()
        .then((list) => list.length);
      
      // القراءات التقديرية
      stats['estimated'] = await (_database.select(_database.readings)
        ..where((r) => r.isEstimated.equals(true))).get()
        .then((list) => list.length);

      return stats;
    } catch (e) {
      throw Exception('خطأ في الحصول على إحصائيات القراءات: $e');
    }
  }

  /// تحويل من نموذج قاعدة البيانات إلى كيان
  ReadingEntity _mapToEntity(
    Reading reading,
    String meterNumber,
    String subscriberName,
    String villageName
  ) {
    return ReadingEntity(
      id: reading.id,
      meterId: reading.meterId,
      meterNumber: meterNumber,
      subscriberId: 0, // سيتم تحديثه من العداد
      subscriberName: subscriberName,
      villageName: villageName,
      previousReading: reading.previousReading,
      currentReading: reading.currentReading,
      consumption: reading.consumption,
      readingDate: reading.readingDate,
      type: ReadingType.values.firstWhere(
        (e) => e.name == reading.readingMethod,
        orElse: () => ReadingType.regular,
      ),
      status: reading.isValidated
          ? ReadingStatus.confirmed
          : ReadingStatus.pending,
      readerName: null, // غير متوفر في الجدول الحالي
      notes: reading.validationNotes,
      imageUrl: reading.imagePath,
      isEstimated: reading.isEstimated,
      estimatedConsumption: reading.isEstimated ? reading.consumption : null,
      createdAt: reading.createdAt,
      updatedAt: reading.createdAt, // لا يوجد updatedAt في الجدول
    );
  }
}
