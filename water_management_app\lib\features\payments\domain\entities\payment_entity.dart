import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// طرق الدفع
enum PaymentMethod {
  cash,
  bankTransfer,
  creditCard,
  debitCard,
  check,
  online,
}

/// حالات الدفع
enum PaymentStatus {
  pending,
  completed,
  failed,
  cancelled,
  refunded,
}

/// أنواع الدفع
enum PaymentType {
  full,
  partial,
  installment,
  advance,
}

/// كيان المدفوعات
class PaymentEntity extends Equatable {
  final int id;
  final String paymentNumber;
  final int billId;
  final String billNumber;
  final int subscriberId;
  final String subscriberName;
  final double amount;
  final PaymentMethod method;
  final PaymentType type;
  final PaymentStatus status;
  final DateTime paymentDate;
  final String? referenceNumber;
  final String? bankName;
  final String? checkNumber;
  final String? notes;
  final String? receiptPath;
  final String collectedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PaymentEntity({
    required this.id,
    required this.paymentNumber,
    required this.billId,
    required this.billNumber,
    required this.subscriberId,
    required this.subscriberName,
    required this.amount,
    required this.method,
    required this.type,
    required this.status,
    required this.paymentDate,
    this.referenceNumber,
    this.bankName,
    this.checkNumber,
    this.notes,
    this.receiptPath,
    required this.collectedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        paymentNumber,
        billId,
        billNumber,
        subscriberId,
        subscriberName,
        amount,
        method,
        type,
        status,
        paymentDate,
        referenceNumber,
        bankName,
        checkNumber,
        notes,
        receiptPath,
        collectedBy,
        createdAt,
        updatedAt,
      ];

  /// نسخ الكيان مع تعديل بعض الخصائص
  PaymentEntity copyWith({
    int? id,
    String? paymentNumber,
    int? billId,
    String? billNumber,
    int? subscriberId,
    String? subscriberName,
    double? amount,
    PaymentMethod? method,
    PaymentType? type,
    PaymentStatus? status,
    DateTime? paymentDate,
    String? referenceNumber,
    String? bankName,
    String? checkNumber,
    String? notes,
    String? receiptPath,
    String? collectedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentEntity(
      id: id ?? this.id,
      paymentNumber: paymentNumber ?? this.paymentNumber,
      billId: billId ?? this.billId,
      billNumber: billNumber ?? this.billNumber,
      subscriberId: subscriberId ?? this.subscriberId,
      subscriberName: subscriberName ?? this.subscriberName,
      amount: amount ?? this.amount,
      method: method ?? this.method,
      type: type ?? this.type,
      status: status ?? this.status,
      paymentDate: paymentDate ?? this.paymentDate,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      bankName: bankName ?? this.bankName,
      checkNumber: checkNumber ?? this.checkNumber,
      notes: notes ?? this.notes,
      receiptPath: receiptPath ?? this.receiptPath,
      collectedBy: collectedBy ?? this.collectedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'payment_number': paymentNumber,
      'bill_id': billId,
      'bill_number': billNumber,
      'subscriber_id': subscriberId,
      'subscriber_name': subscriberName,
      'amount': amount,
      'method': method.name,
      'type': type.name,
      'status': status.name,
      'payment_date': paymentDate.toIso8601String(),
      'reference_number': referenceNumber,
      'bank_name': bankName,
      'check_number': checkNumber,
      'notes': notes,
      'receipt_path': receiptPath,
      'collected_by': collectedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory PaymentEntity.fromMap(Map<String, dynamic> map) {
    return PaymentEntity(
      id: map['id'] as int,
      paymentNumber: map['payment_number'] as String,
      billId: map['bill_id'] as int,
      billNumber: map['bill_number'] as String? ?? '',
      subscriberId: map['subscriber_id'] as int,
      subscriberName: map['subscriber_name'] as String? ?? '',
      amount: (map['amount'] as num).toDouble(),
      method: PaymentMethod.values.firstWhere(
        (e) => e.name == map['method'],
        orElse: () => PaymentMethod.cash,
      ),
      type: PaymentType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => PaymentType.full,
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => PaymentStatus.pending,
      ),
      paymentDate: DateTime.parse(map['payment_date'] as String),
      referenceNumber: map['reference_number'] as String?,
      bankName: map['bank_name'] as String?,
      checkNumber: map['check_number'] as String?,
      notes: map['notes'] as String?,
      receiptPath: map['receipt_path'] as String?,
      collectedBy: map['collected_by'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  /// الحصول على نص طريقة الدفع
  String get methodText {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
      case PaymentMethod.debitCard:
        return 'بطاقة خصم';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.online:
        return 'دفع إلكتروني';
    }
  }

  /// الحصول على نص نوع الدفع
  String get typeText {
    switch (type) {
      case PaymentType.full:
        return 'دفع كامل';
      case PaymentType.partial:
        return 'دفع جزئي';
      case PaymentType.installment:
        return 'قسط';
      case PaymentType.advance:
        return 'دفع مقدم';
    }
  }

  /// الحصول على نص حالة الدفع
  String get statusText {
    switch (status) {
      case PaymentStatus.pending:
        return 'في الانتظار';
      case PaymentStatus.completed:
        return 'مكتمل';
      case PaymentStatus.failed:
        return 'فشل';
      case PaymentStatus.cancelled:
        return 'ملغي';
      case PaymentStatus.refunded:
        return 'مسترد';
    }
  }

  /// الحصول على لون الحالة
  Color get statusColor {
    switch (status) {
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.completed:
        return Colors.green;
      case PaymentStatus.failed:
        return Colors.red;
      case PaymentStatus.cancelled:
        return Colors.grey;
      case PaymentStatus.refunded:
        return Colors.blue;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get statusIcon {
    switch (status) {
      case PaymentStatus.pending:
        return Icons.pending;
      case PaymentStatus.completed:
        return Icons.check_circle;
      case PaymentStatus.failed:
        return Icons.error;
      case PaymentStatus.cancelled:
        return Icons.cancel;
      case PaymentStatus.refunded:
        return Icons.undo;
    }
  }

  /// الحصول على أيقونة طريقة الدفع
  IconData get methodIcon {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.creditCard:
        return Icons.credit_card;
      case PaymentMethod.debitCard:
        return Icons.payment;
      case PaymentMethod.check:
        return Icons.receipt;
      case PaymentMethod.online:
        return Icons.computer;
    }
  }

  /// التحقق من إمكانية الإلغاء
  bool get canBeCancelled {
    return status == PaymentStatus.pending;
  }

  /// التحقق من إمكانية الاسترداد
  bool get canBeRefunded {
    return status == PaymentStatus.completed;
  }

  /// الحصول على ملخص الدفع
  String get summary {
    return '$paymentNumber - $subscriberName: ${amount.toStringAsFixed(2)} ريال';
  }

  /// تحديث حالة الدفع
  PaymentEntity updateStatus(PaymentStatus newStatus) {
    return copyWith(
      status: newStatus,
      updatedAt: DateTime.now(),
    );
  }

  /// إنشاء دفعة جديدة
  factory PaymentEntity.create({
    required int billId,
    required String billNumber,
    required int subscriberId,
    required String subscriberName,
    required double amount,
    required PaymentMethod method,
    required PaymentType type,
    required String collectedBy,
    DateTime? paymentDate,
    String? referenceNumber,
    String? bankName,
    String? checkNumber,
    String? notes,
  }) {
    return PaymentEntity(
      id: 0,
      paymentNumber: '', // سيتم إنشاؤه تلقائياً
      billId: billId,
      billNumber: billNumber,
      subscriberId: subscriberId,
      subscriberName: subscriberName,
      amount: amount,
      method: method,
      type: type,
      status: PaymentStatus.pending,
      paymentDate: paymentDate ?? DateTime.now(),
      referenceNumber: referenceNumber,
      bankName: bankName,
      checkNumber: checkNumber,
      notes: notes,
      collectedBy: collectedBy,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
