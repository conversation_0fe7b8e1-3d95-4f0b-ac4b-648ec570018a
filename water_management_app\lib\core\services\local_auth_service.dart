import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';
import 'package:logger/logger.dart';

import '../database/app_database.dart';
import '../constants/app_constants.dart';
import 'database_service.dart';

class LocalAuthService {
  static final LocalAuthService instance = LocalAuthService._init();
  LocalAuthService._init();

  late SharedPreferences _prefs;
  late Encrypter _encrypter;
  late IV _iv;
  final Logger _logger = Logger();
  
  String? _currentUserId;
  String? _currentUserRole;
  String? _currentUserName;
  String? _sessionId;

  /// تهيئة خدمة المصادقة
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      
      // إنشاء أو استرداد مفاتيح التشفير
      await _initializeEncryption();
      
      // التحقق من وجود جلسة نشطة
      await _checkExistingSession();
      
      _logger.i('تم تهيئة خدمة المصادقة بنجاح');
    } catch (e) {
      _logger.e('خطأ في تهيئة خدمة المصادقة: $e');
      rethrow;
    }
  }

  /// تهيئة التشفير
  Future<void> _initializeEncryption() async {
    try {
      String? keyString = _prefs.getString('encryption_key');
      String? ivString = _prefs.getString('encryption_iv');
      
      if (keyString == null || ivString == null) {
        // إنشاء مفاتيح جديدة
        final key = Key.fromSecureRandom(32);
        _iv = IV.fromSecureRandom(16);
        
        await _prefs.setString('encryption_key', key.base64);
        await _prefs.setString('encryption_iv', _iv.base64);
        
        _encrypter = Encrypter(AES(key));
      } else {
        // استخدام المفاتيح الموجودة
        final key = Key.fromBase64(keyString);
        _iv = IV.fromBase64(ivString);
        _encrypter = Encrypter(AES(key));
      }
    } catch (e) {
      _logger.e('خطأ في تهيئة التشفير: $e');
      rethrow;
    }
  }

  /// التحقق من وجود جلسة نشطة
  Future<void> _checkExistingSession() async {
    try {
      final sessionData = _prefs.getString(AppConstants.userSessionKey);
      if (sessionData == null) return;

      final decrypted = _encrypter.decrypt64(sessionData, iv: _iv);
      final session = jsonDecode(decrypted);
      
      // التحقق من انتهاء صلاحية الجلسة
      final expiryTime = DateTime.parse(session['expiry']);
      if (DateTime.now().isAfter(expiryTime)) {
        await logout();
        return;
      }

      _currentUserId = session['userId'];
      _currentUserRole = session['role'];
      _currentUserName = session['userName'];
      _sessionId = session['sessionId'];
      
      _logger.i('تم استرداد الجلسة النشطة للمستخدم: $_currentUserName');
    } catch (e) {
      _logger.w('خطأ في استرداد الجلسة: $e');
      await logout();
    }
  }

  /// تسجيل الدخول
  Future<AuthResult> login(String username, String password) async {
    try {
      final database = DatabaseService.instance.database;
      
      // البحث عن المستخدم
      final user = await (database.select(database.users)
        ..where((u) => u.username.equals(username) & u.isActive.equals(true)))
        .getSingleOrNull();

      if (user == null) {
        await _logFailedLogin(username, 'مستخدم غير موجود');
        return AuthResult.failure('اسم المستخدم غير موجود');
      }

      // التحقق من قفل الحساب
      if (user.lockedUntil != null && DateTime.now().isBefore(user.lockedUntil!)) {
        final remainingTime = user.lockedUntil!.difference(DateTime.now());
        return AuthResult.failure(
          'الحساب مقفل لمدة ${remainingTime.inMinutes} دقيقة'
        );
      }

      // التحقق من كلمة المرور
      if (!database.verifyPassword(password, user.passwordHash)) {
        await _recordFailedLogin(user);
        return AuthResult.failure('كلمة المرور غير صحيحة');
      }

      // إعادة تعيين محاولات الدخول الفاشلة
      await _resetFailedLoginAttempts(user);

      // تحديث آخر تسجيل دخول
      await (database.update(database.users)
        ..where((u) => u.id.equals(user.id)))
        .write(UsersCompanion(
          lastLogin: Value(DateTime.now()),
          updatedAt: Value(DateTime.now()),
        ));

      // إنشاء جلسة جديدة
      await _createUserSession(user);
      
      // تسجيل عملية تسجيل الدخول
      await DatabaseService.instance.logAuditTrail(
        tableName: 'users',
        recordId: user.id,
        operation: 'LOGIN',
        userId: user.id,
        sessionId: _sessionId,
      );

      _logger.i('تم تسجيل دخول المستخدم: ${user.fullName}');
      return AuthResult.success(user);
    } catch (e) {
      _logger.e('خطأ في تسجيل الدخول: $e');
      return AuthResult.failure('خطأ في تسجيل الدخول');
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      // تسجيل عملية تسجيل الخروج
      if (_currentUserId != null) {
        await DatabaseService.instance.logAuditTrail(
          tableName: 'users',
          recordId: int.parse(_currentUserId!),
          operation: 'LOGOUT',
          userId: int.parse(_currentUserId!),
          sessionId: _sessionId,
        );
      }

      // حذف بيانات الجلسة
      await _prefs.remove(AppConstants.userSessionKey);
      await _prefs.remove('session_token');
      
      _currentUserId = null;
      _currentUserRole = null;
      _currentUserName = null;
      _sessionId = null;
      
      _logger.i('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _logger.e('خطأ في تسجيل الخروج: $e');
    }
  }

  /// التحقق من وجود جلسة نشطة
  bool get isLoggedIn => _currentUserId != null;

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => _currentUserId;

  /// الحصول على دور المستخدم الحالي
  String? get currentUserRole => _currentUserRole;

  /// الحصول على اسم المستخدم الحالي
  String? get currentUserName => _currentUserName;

  /// الحصول على معرف الجلسة
  String? get sessionId => _sessionId;

  /// التحقق من الصلاحيات
  bool hasPermission(String permission) {
    if (_currentUserRole == null) return false;
    
    final permissions = AppConstants.rolePermissions[_currentUserRole!] ?? [];
    
    // المدير له جميع الصلاحيات
    if (permissions.contains('*')) return true;
    
    return permissions.contains(permission);
  }

  /// التحقق من صلاحية متعددة
  bool hasAnyPermission(List<String> permissions) {
    return permissions.any((permission) => hasPermission(permission));
  }

  /// التحقق من جميع الصلاحيات
  bool hasAllPermissions(List<String> permissions) {
    return permissions.every((permission) => hasPermission(permission));
  }

  /// إنشاء جلسة مستخدم
  Future<void> _createUserSession(User user) async {
    try {
      _sessionId = _generateSessionId();
      _currentUserId = user.id.toString();
      _currentUserRole = user.role;
      _currentUserName = user.fullName;

      final sessionData = {
        'userId': _currentUserId,
        'userName': _currentUserName,
        'role': _currentUserRole,
        'sessionId': _sessionId,
        'loginTime': DateTime.now().toIso8601String(),
        'expiry': DateTime.now()
            .add(Duration(minutes: AppConstants.systemLimits['session_timeout_minutes']!))
            .toIso8601String(),
      };
      
      final encrypted = _encrypter.encrypt(jsonEncode(sessionData), iv: _iv);
      await _prefs.setString(AppConstants.userSessionKey, encrypted.base64);
    } catch (e) {
      _logger.e('خطأ في إنشاء جلسة المستخدم: $e');
      rethrow;
    }
  }

  /// تسجيل محاولة دخول فاشلة
  Future<void> _recordFailedLogin(User user) async {
    try {
      final database = DatabaseService.instance.database;
      final newAttempts = user.failedLoginAttempts + 1;
      final maxAttempts = AppConstants.systemLimits['max_failed_login_attempts']!;
      
      DateTime? lockUntil;
      if (newAttempts >= maxAttempts) {
        lockUntil = DateTime.now().add(const Duration(minutes: 30));
      }

      await (database.update(database.users)
        ..where((u) => u.id.equals(user.id)))
        .write(UsersCompanion(
          failedLoginAttempts: Value(newAttempts),
          lockedUntil: Value(lockUntil),
          updatedAt: Value(DateTime.now()),
        ));

      // تسجيل المحاولة الفاشلة
      await DatabaseService.instance.logAuditTrail(
        tableName: 'users',
        recordId: user.id,
        operation: 'FAILED_LOGIN',
        reason: 'كلمة مرور خاطئة',
      );

      _logger.w('محاولة دخول فاشلة للمستخدم: ${user.username}');
    } catch (e) {
      _logger.e('خطأ في تسجيل المحاولة الفاشلة: $e');
    }
  }

  /// إعادة تعيين محاولات الدخول الفاشلة
  Future<void> _resetFailedLoginAttempts(User user) async {
    try {
      if (user.failedLoginAttempts > 0 || user.lockedUntil != null) {
        final database = DatabaseService.instance.database;
        await (database.update(database.users)
          ..where((u) => u.id.equals(user.id)))
          .write(UsersCompanion(
            failedLoginAttempts: const Value(0),
            lockedUntil: const Value(null),
            updatedAt: Value(DateTime.now()),
          ));
      }
    } catch (e) {
      _logger.e('خطأ في إعادة تعيين محاولات الدخول: $e');
    }
  }

  /// تسجيل محاولة دخول فاشلة لمستخدم غير موجود
  Future<void> _logFailedLogin(String username, String reason) async {
    try {
      await DatabaseService.instance.logAuditTrail(
        tableName: 'users',
        recordId: 0,
        operation: 'FAILED_LOGIN',
        reason: '$reason - اسم المستخدم: $username',
      );
    } catch (e) {
      _logger.e('خطأ في تسجيل المحاولة الفاشلة: $e');
    }
  }

  /// إنشاء معرف جلسة عشوائي
  String _generateSessionId() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  /// تحديث كلمة المرور
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      if (_currentUserId == null) return false;

      final database = DatabaseService.instance.database;
      final user = await (database.select(database.users)
        ..where((u) => u.id.equals(int.parse(_currentUserId!))))
        .getSingle();

      // التحقق من كلمة المرور الحالية
      if (!database.verifyPassword(currentPassword, user.passwordHash)) {
        return false;
      }

      // تحديث كلمة المرور
      final newPasswordHash = database.hashPassword(newPassword);
      await (database.update(database.users)
        ..where((u) => u.id.equals(user.id)))
        .write(UsersCompanion(
          passwordHash: Value(newPasswordHash),
          updatedAt: Value(DateTime.now()),
        ));

      // تسجيل العملية
      await DatabaseService.instance.logAuditTrail(
        tableName: 'users',
        recordId: user.id,
        operation: 'PASSWORD_CHANGE',
        userId: user.id,
        sessionId: _sessionId,
      );

      _logger.i('تم تغيير كلمة المرور للمستخدم: ${user.username}');
      return true;
    } catch (e) {
      _logger.e('خطأ في تغيير كلمة المرور: $e');
      return false;
    }
  }

  /// تمديد الجلسة
  Future<bool> extendSession() async {
    try {
      if (!isLoggedIn) return false;

      final sessionData = _prefs.getString(AppConstants.userSessionKey);
      if (sessionData == null) return false;

      final decrypted = _encrypter.decrypt64(sessionData, iv: _iv);
      final session = jsonDecode(decrypted);
      
      // تحديث وقت انتهاء الجلسة
      session['expiry'] = DateTime.now()
          .add(Duration(minutes: AppConstants.systemLimits['session_timeout_minutes']!))
          .toIso8601String();
      
      final encrypted = _encrypter.encrypt(jsonEncode(session), iv: _iv);
      await _prefs.setString(AppConstants.userSessionKey, encrypted.base64);
      
      return true;
    } catch (e) {
      _logger.e('خطأ في تمديد الجلسة: $e');
      return false;
    }
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? message;
  final User? user;

  AuthResult._(this.isSuccess, this.message, this.user);

  factory AuthResult.success(User user) => AuthResult._(true, null, user);
  factory AuthResult.failure(String message) => AuthResult._(false, message, null);
}
