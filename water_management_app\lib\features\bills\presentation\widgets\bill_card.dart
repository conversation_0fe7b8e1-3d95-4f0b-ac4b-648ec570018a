import 'package:flutter/material.dart';
import '../../../../core/adaptive/adaptive_widgets.dart';
import '../../domain/entities/bill_entity.dart';

class BillCard extends StatelessWidget {
  final BillEntity bill;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onPay;
  final Function(BillStatus)? onUpdateStatus;

  const BillCard({
    super.key,
    required this.bill,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onPay,
    this.onUpdateStatus,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 12),
          _buildBillInfo(context),
          const SizedBox(height: 12),
          _buildAmountInfo(context),
          if (bill.isOverdue) ...[
            const SizedBox(height: 8),
            _buildOverdueWarning(context),
          ],
          if (onEdit != null || onDelete != null || onPay != null) ...[
            const SizedBox(height: 12),
            const Divider(),
            _buildActions(context),
          ],
        ],
      ),
    );
  }

  /// بناء الرأس
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: bill.statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            bill.statusIcon,
            color: bill.statusColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                bill.billNumber,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                bill.subscriberName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: bill.statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    bill.statusIcon,
                    size: 12,
                    color: bill.statusColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    bill.statusText,
                    style: TextStyle(
                      color: bill.statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              bill.issueDate.toString().substring(0, 10),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء معلومات الفاتورة
  Widget _buildBillInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.location_city,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  bill.villageName,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  bill.meterNumber,
                  style: const TextStyle(
                    color: Colors.blue,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  context,
                  'الاستهلاك',
                  '${bill.consumption.toStringAsFixed(2)} م³',
                  Icons.water_drop,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoItem(
                  context,
                  'الفترة',
                  '${bill.billingPeriodStart.month}/${bill.billingPeriodStart.year}',
                  Icons.calendar_month,
                  Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء معلومات المبلغ
  Widget _buildAmountInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.05),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.attach_money,
                color: Colors.green,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي المبلغ',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${bill.totalAmount.toStringAsFixed(2)} ريال',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'الاستحقاق',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    bill.dueDate.toString().substring(0, 10),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: bill.isOverdue ? Colors.red : Colors.grey[800],
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (bill.isPartiallyPaid) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: bill.paymentPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'مدفوع: ${bill.paidAmount.toStringAsFixed(2)} ريال',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Text(
                  'متبقي: ${bill.remainingAmount.toStringAsFixed(2)} ريال',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// بناء تحذير التأخير
  Widget _buildOverdueWarning(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning,
            color: Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'متأخرة عن الاستحقاق بـ ${(-bill.daysUntilDue)} يوم',
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإجراءات
  Widget _buildActions(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        if (onPay != null && bill.canBePaid)
          TextButton.icon(
            onPressed: onPay,
            icon: const Icon(Icons.payment, size: 16),
            label: const Text('دفع'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
          ),
        if (onUpdateStatus != null)
          PopupMenuButton<BillStatus>(
            onSelected: onUpdateStatus,
            itemBuilder: (context) => BillStatus.values.map((status) {
              return PopupMenuItem(
                value: status,
                child: Row(
                  children: [
                    Icon(
                      _getStatusIcon(status),
                      size: 16,
                      color: _getStatusColor(status),
                    ),
                    const SizedBox(width: 8),
                    Text(_getStatusText(status)),
                  ],
                ),
              );
            }).toList(),
            child: TextButton.icon(
              onPressed: null,
              icon: const Icon(Icons.swap_horiz, size: 16),
              label: const Text('تغيير الحالة'),
            ),
          ),
        if (onEdit != null && bill.status == BillStatus.draft)
          TextButton.icon(
            onPressed: onEdit,
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('تعديل'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue,
            ),
          ),
        if (onDelete != null && bill.canBeCancelled)
          TextButton.icon(
            onPressed: onDelete,
            icon: const Icon(Icons.delete, size: 16),
            label: const Text('حذف'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
      ],
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(BillStatus status) {
    switch (status) {
      case BillStatus.draft:
        return Icons.draft;
      case BillStatus.issued:
        return Icons.receipt;
      case BillStatus.paid:
        return Icons.check_circle;
      case BillStatus.overdue:
        return Icons.warning;
      case BillStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(BillStatus status) {
    switch (status) {
      case BillStatus.draft:
        return Colors.grey;
      case BillStatus.issued:
        return Colors.blue;
      case BillStatus.paid:
        return Colors.green;
      case BillStatus.overdue:
        return Colors.red;
      case BillStatus.cancelled:
        return Colors.orange;
    }
  }

  /// الحصول على نص الحالة
  String _getStatusText(BillStatus status) {
    switch (status) {
      case BillStatus.draft:
        return 'مسودة';
      case BillStatus.issued:
        return 'صادرة';
      case BillStatus.paid:
        return 'مدفوعة';
      case BillStatus.overdue:
        return 'متأخرة';
      case BillStatus.cancelled:
        return 'ملغية';
    }
  }
}
