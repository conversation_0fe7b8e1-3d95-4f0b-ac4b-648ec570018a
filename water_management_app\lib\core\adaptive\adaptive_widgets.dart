import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'adaptive_layout.dart';

/// بطاقة تكيفية
class AdaptiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool elevated;

  const AdaptiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.elevated = true,
  });

  @override
  Widget build(BuildContext context) {
    final adaptivePadding = padding ?? AdaptiveLayout.getPadding(context);
    final adaptiveMargin = margin ?? EdgeInsets.all(AdaptiveLayout.getSpacing(context));

    Widget cardChild = Container(
      padding: adaptivePadding,
      margin: adaptiveMargin,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: elevated ? [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: AdaptiveLayout.isDesktop(context) ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ] : null,
        border: !elevated ? Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ) : null,
      ),
      child: child,
    );

    if (onTap != null) {
      cardChild = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: cardChild,
      );
    }

    return cardChild;
  }
}

/// شبكة تكيفية
class AdaptiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final int? forceColumns;

  const AdaptiveGrid({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.forceColumns,
  });

  @override
  Widget build(BuildContext context) {
    final columns = forceColumns ?? AdaptiveLayout.getGridColumns(context);
    final spacing = AdaptiveLayout.getSpacing(context);

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: childAspectRatio ?? 1.0,
        mainAxisSpacing: mainAxisSpacing ?? spacing,
        crossAxisSpacing: crossAxisSpacing ?? spacing,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// قائمة تكيفية
class AdaptiveListView extends StatelessWidget {
  final List<Widget> children;
  final ScrollController? controller;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;

  const AdaptiveListView({
    super.key,
    required this.children,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final adaptivePadding = padding ?? AdaptiveLayout.getPadding(context);

    Widget listView = ListView(
      controller: controller,
      padding: adaptivePadding,
      shrinkWrap: shrinkWrap,
      children: children,
    );

    if (AdaptiveLayout.isDesktop(context)) {
      listView = AdaptiveScrollbar(
        controller: controller,
        child: listView,
      );
    }

    return listView;
  }
}

/// حوار تكيفي
class AdaptiveDialog extends StatelessWidget {
  final String? title;
  final Widget content;
  final List<Widget>? actions;
  final bool scrollable;

  const AdaptiveDialog({
    super.key,
    this.title,
    required this.content,
    this.actions,
    this.scrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return Dialog(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: AdaptiveLayout.isLargeDesktop(context) ? 600 : 500,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (title != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Text(
                    title!,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: scrollable ? SingleChildScrollView(child: content) : content,
                ),
              ),
              if (actions != null && actions!.isNotEmpty)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: actions!,
                  ),
                ),
            ],
          ),
        ),
      );
    }

    return AlertDialog(
      title: title != null ? Text(title!) : null,
      content: scrollable ? SingleChildScrollView(child: content) : content,
      actions: actions,
    );
  }

  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    required Widget content,
    List<Widget>? actions,
    bool scrollable = false,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AdaptiveDialog(
        title: title,
        content: content,
        actions: actions,
        scrollable: scrollable,
      ),
    );
  }
}

/// لوحة جانبية تكيفية
class AdaptiveSidePanel extends StatelessWidget {
  final String? title;
  final Widget child;
  final double? width;
  final List<Widget>? actions;

  const AdaptiveSidePanel({
    super.key,
    this.title,
    required this.child,
    this.width,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final panelWidth = width ?? AdaptiveLayout.getPanelWidth(context);

    return Container(
      width: panelWidth,
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(-2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          if (title != null || actions != null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
              ),
              child: Row(
                children: [
                  if (title != null)
                    Expanded(
                      child: Text(
                        title!,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  if (actions != null) ...actions!,
                ],
              ),
            ),
          Expanded(child: child),
        ],
      ),
    );
  }

  static void show({
    required BuildContext context,
    String? title,
    required Widget child,
    double? width,
    List<Widget>? actions,
  }) {
    if (AdaptiveLayout.isDesktop(context)) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => Align(
          alignment: Alignment.centerRight,
          child: Material(
            child: AdaptiveSidePanel(
              title: title,
              width: width,
              actions: [
                ...?actions,
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
              child: child,
            ),
          ),
        ),
      );
    } else {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) => DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (context, scrollController) => Container(
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              children: [
                if (title != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            title,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                  ),
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: child,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
  }
}

/// جدول تكيفي
class AdaptiveDataTable extends StatelessWidget {
  final List<DataColumn> columns;
  final List<DataRow> rows;
  final bool sortAscending;
  final int? sortColumnIndex;
  final Function(int, bool)? onSort;
  final bool showCheckboxColumn;

  const AdaptiveDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.sortAscending = true,
    this.sortColumnIndex,
    this.onSort,
    this.showCheckboxColumn = false,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: columns,
          rows: rows,
          sortAscending: sortAscending,
          sortColumnIndex: sortColumnIndex,
          showCheckboxColumn: showCheckboxColumn,
          columnSpacing: 24,
          horizontalMargin: 16,
        ),
      );
    }

    // للأجهزة المحمولة، عرض كقائمة
    return ListView.builder(
      itemCount: rows.length,
      itemBuilder: (context, index) {
        final row = rows[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: List.generate(
                columns.length,
                (colIndex) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 100,
                        child: Text(
                          columns[colIndex].label.toString(),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      Expanded(
                        child: row.cells[colIndex].child,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// شريط أدوات تكيفي
class AdaptiveToolbar extends StatelessWidget {
  final List<Widget> actions;
  final Widget? title;
  final bool dense;

  const AdaptiveToolbar({
    super.key,
    required this.actions,
    this.title,
    this.dense = false,
  });

  @override
  Widget build(BuildContext context) {
    if (AdaptiveLayout.isDesktop(context)) {
      return Container(
        padding: EdgeInsets.all(dense ? 8 : 16),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor,
            ),
          ),
        ),
        child: Row(
          children: [
            if (title != null) ...[
              title!,
              const Spacer(),
            ],
            ...actions.map((action) => Padding(
              padding: const EdgeInsets.only(left: 8),
              child: action,
            )),
          ],
        ),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        if (title != null) title!,
        ...actions,
      ],
    );
  }
}

/// مؤشر التحميل التكيفي
class AdaptiveLoadingIndicator extends StatelessWidget {
  final String? message;
  final double? size;

  const AdaptiveLoadingIndicator({
    super.key,
    this.message,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? (AdaptiveLayout.isDesktop(context) ? 48 : 32),
            height: size ?? (AdaptiveLayout.isDesktop(context) ? 48 : 32),
            child: const CircularProgressIndicator(),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// رسالة فارغة تكيفية
class AdaptiveEmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Widget? action;

  const AdaptiveEmptyState({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize = AdaptiveLayout.isDesktop(context) ? 80.0 : 64.0;

    return Center(
      child: Padding(
        padding: AdaptiveLayout.getPadding(context),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}
